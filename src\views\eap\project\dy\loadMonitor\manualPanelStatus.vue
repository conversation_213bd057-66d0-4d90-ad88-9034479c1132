<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.ngReason') }}
        </template>
        <el-input ref="webPanelMsg" v-model="webPanelMsg" clearable size="mini" :readonly="true" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.processType') }}
        </template>
        <el-radio v-for="(item,index) in statusList" :key="index" v-model="manualJudgeCode" :label="item.judge_code" style="margin-left:0px;width:100%;margin-bottom:10px;" border>{{ item.judge_des }}</el-radio>
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:center">
      <el-button type="primary" @click="handleInfo(1)">{{ $t('lang_pack.dialogMain.confirm') }}</el-button>
      <el-button type="primary" @click="handleInfo(0)" style="margin-left:100px;background:red;">{{ $t('lang_pack.dialogMain.cancel') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      statusList: [{ judge_code: '2', judge_des: 'Retry' }, { judge_code: '4', judge_des: 'NoRead' },
                   { judge_code: '5', judge_des: 'Mix' }, { judge_code: '6', judge_des: 'Dummy' }],
      webPanelMsg: '',
      manualJudgeCode:''
    }
  },
  mounted: function() {
  },
  created: function() {
    this.webPanelMsg = this.tag_key_list.WebPanelMsg
  },
  methods: {
    handleInfo(code) {
      var manual_judge_code='7'
      var tipMsg='Confirm that the current operation is a mistake and cancel the count?'
      if(code===1){
        if(this.manualJudgeCode===''){
          this.$message({ message: 'Please select the processing type', type: 'info' })
          return
        }
        manual_judge_code=this.manualJudgeCode
        if(manual_judge_code==='2'){
          tipMsg='Confirm to reread the board components?'
        }
        else if(manual_judge_code==='4'){
          tipMsg='Confirm to set the current board as NoRead?'
        }
        else if(manual_judge_code==='5'){
          tipMsg='Confirm to set the current board as a mixed board?'
        }
        else if(manual_judge_code==='6'){
          tipMsg='Confirm to set the current board as a companion plating board?'
        }
      }
      //弹窗等待确认
      this.$confirm(tipMsg, this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
            var sendJson = {}
            var rowJson = []
            var newRow = {
              TagKey: this.tag_key_list.WebPanelNum,
              TagValue: ''
            }
            rowJson.push(newRow)
            newRow = {
              TagKey: this.tag_key_list.WebPanelletConfirmModel,
              TagValue: manual_judge_code
            }
            rowJson.push(newRow)
            newRow = {
              TagKey: this.tag_key_list.WebPanelInfoRequest,
              TagValue: '1'
            }
            rowJson.push(newRow)
            sendJson.Data = rowJson
            sendJson.ClientName = 'SCADA_WEB'
            var sendStr = JSON.stringify(sendJson)
            var topic = 'SCADA_WRITE/' + this.tag_key_list.WebPanelInfoRequest.split('/')[0]
            this.$emit('sendMessage', topic, sendStr, false, '')
        })
        .catch(() => {
        })
    }
  }
}
</script>
<style lang="less" scoped>
.table-descriptions-label {
  width: 150px;
}
</style>>


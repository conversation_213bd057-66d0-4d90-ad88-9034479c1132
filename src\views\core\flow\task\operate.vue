<template>
  <div>
    <!-- <el-button type="warning" round>取消步骤</el-button>
    <el-button type="danger" round>跳过步骤</el-button> -->
    <el-button type="danger" round @click="stepJump">{{ $t('lang_pack.mainmain.stepJump') }}</el-button>
    <!-- <commonTable
    :columns="columns"
    :data="tableData"
    :isRadio="true"
    @on-selection-change="handleSelectionChange"
    @on-row-click="handleRowClick" /> -->

    <div style="text-align: center;" v-if="stepJumpFlag">
      <el-table border @header-dragend="crud.tableHeaderDragend()" v-loading="loading" style="margin: 10px 0;" :data="tableData" :row-key="row => row.me_step_id"
        :height="height" :highlight-current-row="highlightCurrentRow" @selection-change="handleSelectionChange">
        <el-table-column  width="100" :label="$t('lang_pack.mainmain.radio')" align="center">
          <template slot-scope="scope">
            <el-radio @change.native.stop.prevent="rowChange(scope.row)" v-model="templateSelection"
              :label="scope.row.me_step_id">&nbsp;
            </el-radio>
          </template>
        </el-table-column>
        <el-table-column  v-for="(column, index) in columns" :key="index" align="center" :width="column.width" :prop="column.prop"
          :label="column.label">
        </el-table-column>
      </el-table>
      <el-button type="primary" @click="handleOk">{{ $t('lang_pack.vie.determine') }}</el-button>
    </div>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
// import commonTable from '@/components/commonTable/common-table'
import { selCellIP } from '@/api/core/center/cell'
import axios from 'axios'
export default {
  components: {},
  data() {
    return {
      columns: [
        { label: 'StepIndex', prop: 'name',width:'100'},
        { label: 'StepName', prop: 'step_mod_des' },
      ],
      height: document.documentElement.clientHeight - 230,
      tableData: [],
      loading: false,
      highlightCurrentRow: true,
      tableColumns: [], // 表格列
      isMultipleSelect: false, // 是否多选
      selectedRows: [], // 已选中的行
      templateSelection: '',
      stepJumpFlag: false,
      meStepIdRow: {},
      cellIp:'',
      webapiPort:'',
      mqttPort:''
    }
  },
  props: {
    flow_mod_main_id: {
      type: [String, Number],
      default: ''
    },
    me_flow_task_id: {
      type: [String, Number],
      default: ''
    },
    subId_stepId: {
      type: [String, Number],
      default: ''
    },
    nodes:{
      type:Array,
      default:[],
    }
  },
  computed: {
    // 是否显示选择列
    showSelectionColumn() {
      return this.isMultipleSelect || this.onSelectionChange;
    },
  },
  mounted() {
    const that = this
    this.getCellIp()
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 230
    }
  },
  methods: {
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.cellId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    stepJump() {
      this.stepJumpFlag = true
      this.$nextTick(() => {
        this.getTableList()
      })
    },
    getTableList() {
      this.loading = true
      var method = '/cell/core/flow/CoreFlowSelectStep';
      var path = ''
      const data = {
        me_flow_task_id: this.me_flow_task_id,
        flow_mod_main_id: this.flow_mod_main_id,
      }
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      }

      //加入语言传递
      var langSel='zh-CN'
      const language = ['zh-CN', 'en-US', 'zh-TW', 'th']
      if (language.includes(localStorage.getItem('language'))) {
        langSel = localStorage.getItem('language') || 'zh-CN'
      }

      axios.post(path, data, {
        headers: {
          'Content-Type': 'application/json',
          'Ais-Languages':langSel
        }
      }).then((res) => {
        this.loading = false
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.data.code === 0) {
          //拿到值进行匹配序号名称
          defaultQuery.data.data = defaultQuery.data.data.filter(item =>{
            const obj = this.nodes.find(e=>e.subId_stepId === item.step_mod_id)
            if(obj.name){
              item.name = obj.name
              return true
            }
            return false
          })
          // 然后进行一个排序
          defaultQuery.data.data.sort((a,b)=>{
            var aParts = a.name.split('-');
            var bParts = b.name.split('-');
            var aNum = parseInt(aParts[0]);
            var bNum = parseInt(bParts[0]);
            if (aNum !== bNum) {
              return aNum - bNum;
            } else {
              var aStr = parseInt(aParts[1]);
              var bStr = parseInt(bParts[1]);

              if (aStr !== bStr) {
                return aStr - bStr;
              } else {
                return a.name.localeCompare(b.name);
              }
            }
          })
          this.tableData = defaultQuery.data.data || [];
        } else {
          this.tableData = []
          this.$message({ message: defaultQuery.data.msg, type: 'warning' })
        }
      }).catch(err => {
        this.tableData = []
        this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + err, type: 'error' })
      })
    },
    rowChange(row) {
      console.log(this.subId_stepId)
      this.templateSelection = row.me_step_id
      this.meStepIdRow = row
    },
    // 选择行事件
    handleSelectionChange(rows) {
      this.selectedRows = rows;
      this.onSelectionChange(rows);
    },
    handleOk() {
      if (!this.me_flow_task_id && !this.subId_stepId && !this.meStepIdRow.step_mod_id) {
        this.$message({ message: this.$t('lang_pack.mainmain.selectFlow'), type: 'error' })
        return
      }
      const data = {
        me_flow_task_id: this.me_flow_task_id,
        step_mod_id: this.subId_stepId,
        to_step_mod_id: this.meStepIdRow.step_mod_id
      }
      var method = '/cell/core/flow/CoreFlowSkip';
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:' + 8089 + method
      }
      axios.post(path, data, {
        headers: {
          'Content-Type': 'application/json'
        }
      }).then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.data.code === 0) {
          this.templateSelection = '' //确认后清空所选状态
          this.$message({ message: this.$t('lang_pack.mainmain.jumpSuccessful'), type: 'success' })
          this.$emit('operateFlow')
        }
      }).catch(err => {
        this.$message({ message: this.$t('lang_pack.vie.queryException') +'：' + err, type: 'error' })
      })
    },
  }
}
</script>

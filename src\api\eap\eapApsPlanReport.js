import request from '@/utils/request'

// 任务报表主任务查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanReportSelect',
    method: 'post',
    data
  })
}

// 主任务PNL明细查询
export function eapApsPlanDReportSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanDReportSelect',
    method: 'post',
    data
  })
}

// 任务过站信息
export function eapApsPlanStationFlowSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanStationFlowSelect',
    method: 'post',
    data
  })
}

// 任务过站信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanReportSelectDel',
    method: 'post',
    data
  })
}

// 暂存机数据查询
export function eapMeEachPositionSelect(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeEachPositionSelect',
    method: 'post',
    data
  })
}

// 手工同步任务到收扳机
export function eapApsPlanAsynLoadTaskToUnLoad(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapApsPlanAsynLoadTaskToUnLoad',
    method: 'post',
    data
  })
}

export default { sel, del, eapApsPlanDReportSelect, eapApsPlanStationFlowSelect,eapMeEachPositionSelect,eapApsPlanAsynLoadTaskToUnLoad }

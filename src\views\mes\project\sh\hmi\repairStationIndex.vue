<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <div style=" display: flex; align-items: center;justify-content: space-between;">
        <div class="orderInfo">
          <div style="width: 120px; font-size:12px">返修物料编号:</div>
          <el-input ref="workNumber" v-model="monitorData.MesS_PartID.value" clearable size="small" @keyup.enter.native="handleScan" />
          <el-button size="small" type="primary" @click="handleScan">
            <svg-icon icon-class="scan" style="margin-right: 5px" />{{ $t("lang_pack.vie.scan") }}
          </el-button>
        </div>
      </div>
    </el-card>
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-1">
            <div class="formChild col-md-9 col-12">
              <el-form-item label="返修策略名称:">
                <el-input v-model="query.repair_work_des" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button
            size="small"
            type="primary"
            @click="handleOk"
          >
            确认返修
          </el-button>
          <el-button
            size="small"
            type="primary"
            @click="handleOk2"
          >
            确认删除
          </el-button>
        </template>
      </crudOperation>
      <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" :highlight-current-row="true" @selection-change="crud.selectionChangeHandler">
        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
        <el-table-column type="selection" width="55" fixed="left" />
        <!-- 工位编码 -->
        <el-table-column :show-overflow-tooltip="true" prop="repair_work_des" :label="$t('返修策略名称')" align="center" />
        <!-- 工位描述 -->
        <el-table-column :show-overflow-tooltip="true" prop="repair_work_station_code" :label="$t('返修策略')" align="center" />
      </el-table>
      <!--分页组件-->
      <pagination style="margin-bottom: 10px;" />
    </el-card>
    <el-dialog
      :fullscreen="false"
      top="10px"
      :show-close="true"
      :close-on-click-modal="false"
      title="请输入返修原因"
      custom-class="step-attr-dialog"
      width="30%"
      :visible.sync="unbindDialogVisible"
    >
      <el-input v-model="remarks" type="textarea" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="unbindDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleOk3">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import crudStationSel from '@/api/mes/project/sh/shMaterialWork'
// import crudStation from '@/api/core/factory/sysStation'
import rrOperation from '@crud/RR.operation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
}
export default {
  name: 'repairStationIndex',
  components: { crudOperation, pagination, rrOperation },
  cruds() {
    return CRUD({
      title: '工位管理',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'repair_work_id',
      // 排序
      sort: ['repair_work_id asc'],
      // CRUD Method
      crudMethod: { ...crudStationSel },
      query: {
      },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 220,
      permission: {
        add: ['admin', 'sys_fmod_station:add'],
        edit: ['admin', 'sys_fmod_station:edit'],
        del: ['admin', 'sys_fmod_station:del'],
        reset: ['admin', 'sys_fmod_station:reset']
      },
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      monitorData: {
        // PLC请求写入信号
        MesS_PartID: { client_code: this.$route.query.station_code + '-Bar', group_code: 'BarStatus', tag_code: 'Bar_GetBarCodeResult', value: '' }
      },
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaHmi_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      scanValue: '',
      remarks: '',
      unbindDialogVisible: false
    }
  },
  mounted() {
    // 启动监控
    this.toStartWatch()
    this.$nextTick(() => {
      this.$refs.workNumber.focus()
    })
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 220
    }
  },
  methods: {
    handleOk() {
      this.remarks=''
      this.unbindDialogVisible = true
    },
    handleOk2() {
      var idList = []
      var idList2 = []
      if (!this.crud.selections.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      this.crud.selections.forEach(item => {
        idList.push(item.repair_id)
        idList2.push(item.repair_work_des)
      })
      this.$confirm(`确定删除【${idList2}】数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          crudStationSel
            .MesShRepairWorkDel({
              idList: idList
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch((ex) => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
        this.crud.toQuery()
    },    
    handleOk3() {
      var idList = []
      if (!this.crud.selections.length) {
        this.$message({
          type: 'warning',
          message: '请至少选择一项'
        })
        return
      }
      if (this.crud.selections.length > 1) {
        this.$message({
          type: 'warning',
          message: '最多选择一项'
        })
        return
      }
      if(!this.monitorData.MesS_PartID.value.length){
        this.$message({
          type: 'warning',
          message: '编号不能未空'
        })
        return
      }
      this.unbindDialogVisible = false
      idList.push(this.remarks)
      idList.push(this.monitorData.MesS_PartID.value)
      this.$confirm(`确定需要把【${this.monitorData.MesS_PartID.value}】返修吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {          
          this.crud.selections.forEach(item => {
            idList.push(item.repair_id)
          })
          crudStationSel
            .MesShRepairWorkRecordIns({
              idList: idList
            })
            .then((res) => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch((ex) => {
              this.$message({ message: '操作失败：' + ex, type: 'error' })
            })
        })
        .catch(() => {})
    },

    handleScan() {
      const tagKey = this.$route.query.station_code + '-Bar/BarStatus/Bar_GetBarCodeResult'
      const value = this.monitorData.MesS_PartID.value
      const clientCode = this.$route.query.station_code + '-Bar'
      this.handleRequestCodeOrde(tagKey, value, clientCode)
    },
    // ----------------------------------【MQTT】----------------------------------
    toStartWatch() {
      var query = {
        userName: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // 获取连接地址
          // 'ws://***************:8090/mqtt'
          this.cellIp = result.ip
          this.cellMqttPort = result.mqtt_port
          this.cellWebPort = result.webapi_port
          var connectUrl = 'ws://' + this.cellIp + ':' + this.cellMqttPort + '/mqtt'
          // var connectUrl = 'ws://*************:8083' + '/mqtt'
          // mqtt连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', e => {
            this.mqttConnStatus = true
            // 当MQTT连接成功后，注册CLIENT相关TOPIC
            // 订阅主题
            // 获取Tag值
            this.GetTagValue()
            Object.keys(this.monitorData).forEach(key => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
            })
            this.$message({
              message: '连接成功',
              type: 'success'
            })
          })

          // MQTT连接失败
          this.clientMqtt.on('error', () => {
            this.$message({
              message: '连接失败',
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', () => {
            this.$message({
              message: '连接断开，正在重连。。。',
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            var jsonData = JSON.parse(message)
            if (jsonData == null) return
            if (topic.indexOf('SCADA_CHANGE/') >= 0) {
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
            }
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    handleRequestCodeOrde(tagKey, value, clientCode) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tagKey,
        TagValue: value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = `SCADA_WRITE/${clientCode}`
      this.sendMessage(topic, sendStr)
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 从后台REDIS获取数据
    GetTagValue() {
      // 读取Tag集合(Key)
      var readTagArray = []
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      // 配置路径
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.cellWebPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.cellWebPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                Object.keys(this.monitorData).forEach(key => {
                  var client_code = this.monitorData[key].client_code
                  var group_code = this.monitorData[key].group_code
                  var tag_code = this.monitorData[key].tag_code
                  var tag_key = client_code + '/' + group_code + '/' + tag_code
                  const item = result.filter(item => item.tag_key === tag_key)
                  if (item.length > 0) {
                    this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                  }
                })
              }
            }
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    }
  }
}
</script>
<style lang="less" scoped>
.orderInfo {
    font-size: 11px;
    display: flex;
    align-items: center;
   ::v-deep .el-input__inner {
      width: 960px;
      height: 30px;
      margin: 0 5px;
    }
  }
 ::v-deep .el-pagination{
    margin-bottom: 10px !important;
  }
</style>

<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard header">
      <el-header>
        <div class="statuHead">
          <div>
            <!-- 模式暂时禁止点击使用 -->
            <el-button
              slot="reference"
              :disabled="true"
              :class="
                monitorData.OnOffLine.value === '1' ? 'btnone' : 'btnone0'
              "
              @click.prevent="handleOnOffLineSwitch"
            >{{
              monitorData.OnOffLine.value === "1"
                ? $t("lang_pack.hmiMain.onLine")
                : $t("lang_pack.hmiMain.offline")
            }}</el-button>
            <!-- 复位流程需要隐藏 -->
            <el-button
              v-show="false"
              slot="reference"
              class="btnone1"
              @click.prevent="handleResetProcess"
            >{{ $t('lang_pack.wx.reset') }}</el-button>
            <el-popover
              placement="right"
              width="170"
              :title="$t('lang_pack.wx.equipSelfTest')"
              trigger="click"
            >
              <div v-html="popoverContent.messageContent" />
              <el-button
                v-if="!isShowFlag"
                slot="reference"
                :class="popoverContent.messageContent === '' ? 'btnone0' : 'btnone'"
                type="primary"
                @click.prevent="handleDevice"
              >{{ $t("lang_pack.wx.equipSelfTest") }}</el-button>
            </el-popover>

            <!-- 操作模式下拉按钮 -->
            <el-dropdown style="margin-left: 5px;" @command="handleControlModeChange">
              <el-button type="primary" class="btnone">
                {{ getControlModeText() }}<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="manual">{{ $t('tlwx.manualMode') }}</el-dropdown-item>
                <el-dropdown-item command="auto">{{ $t('tlwx.autoMode') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- 启动状态下拉按钮 -->
            <el-dropdown style="margin-left: 5px;" @command="handleStartStatusChange">
              <el-button :class="monitorData.AutoStart.value === '1' ? 'btnone' : 'btnone0'">
                {{ monitorData.AutoStart.value === '1' ? $t('tlwx.autoStart') : $t('tlwx.systemStop') }}<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="start">{{ $t('tlwx.autoStart') }}</el-dropdown-item>
                <el-dropdown-item command="stop">{{ $t('tlwx.systemStop') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <!-- 加热状态下拉按钮 -->
            <el-dropdown style="margin-left: 5px;" @command="handleHeaterStatusChange">
              <el-button :class="monitorData.HeaterRun.value === '1' ? 'btnone' : 'btnone0'">
                {{ monitorData.HeaterRun.value === '1' ? $t('tlwx.heaterRun') : $t('tlwx.heaterStop') }}<i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="run">{{ $t('tlwx.heaterRun') }}</el-dropdown-item>
                <el-dropdown-item command="stop">{{ $t('tlwx.heaterStop') }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
          <div>
            <div class="wrappstyle">
              <!-- 指示灯 -->
              <p>
                <span
                  :class="monitorData.LampManual.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('tlwx.manualMode') }}</span>
              </p>
              <p>
                <span
                  :class="monitorData.LampAutoMode.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('tlwx.autoMode') }}</span>
              </p>
              <p>
                <span
                  :class="monitorData.LampAutoStart.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('tlwx.autoStart') }}</span>
              </p>
              <p>
                <span
                  :class="monitorData.LampHeaterStop.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('tlwx.heaterRun') }}</span>
              </p>
              <p>
                <span
                  :class="monitorData.HeaterStop.value === '1' ? 'wholeline wholelinenormal' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('tlwx.heaterStop') }}</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.LightStatus.value === '1'
                      ? 'wholeline1 deviceRed'
                      : monitorData.LightStatus.value === '2'
                        ? 'wholeline1 wholelineerror1'
                        : monitorData.LightStatus.value === '3'
                          ? 'wholeline1 wholelinenormal1'
                          : 'wholeline1 wholelinegray1'
                  "
                />
                <span class="statuText">{{
                  $t("lang_pack.vie.triColorLight")
                }}</span>
              </p>
              <p>
                <span
                  :class="controlStatus.plc_status === '1' ? 'wholeline wholelinenormal' :
                    (controlStatus.plc_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                />
                <span class="statuText">{{ $t('lang_pack.wx.plcHeartbeat') }}</span>
              </p>
              <!-- <p>
                <span
                  :class="
                    monitorData.UpLink.value === '0'
                      ? 'wholeline wholelinenormal'
                      : monitorData.UpLink.value === '1'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">{{ $t('lang_pack.wx.upstream') }}</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.DownLink.value === '0'
                      ? 'wholeline wholelinenormal'
                      : monitorData.DownLink.value === '1'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">{{ $t('lang_pack.wx.downstream') }}</span>
              </p>
              <p>
                <span style="font-size: 14px; margin-bottom: 4px">
                  {{
                    monitorData.ElecConSum.value
                      ? monitorData.ElecConSum.value + "%"
                      : "100%"
                  }}
                </span>
                <span class="statuText">{{ $t('lang_pack.wx.electricity') }}</span>
              </p> -->
            </div>
          </div>
        </div>
        <transition name="el-zoom-in-center">
          <span
            v-show="messageShow"
            :class="'message message-' + MessageLevel"
          ><i
            :class="
              MessageLevel === 'warning'
                ? 'el-icon-warning'
                : MessageLevel === 'error'
                  ? 'el-icon-error'
                  : 'el-icon-info'
            "
          />&nbsp;{{ messageContent }}</span>
        </transition>
      </el-header>
    </el-card>
    <el-row
      :gutter="20"
      style="margin-right: 0px; padding: 0px; margin-top: 10px"
    >
      <el-col :span="12" style="padding-right: 0">
        <el-col :span="24" style="padding: 0">
          <!-- <el-col :span="12" style="padding: 0 5px 0 0;"> -->
          <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="80px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12 barcode">
                    <el-form-item>
                      <span 
                        slot="label" 
                        @dblclick="toggleInputMode" 
                        @click="handleLabelClick"
                        style="cursor: pointer; user-select: none;"
                        :style="{color: isManualInputEnabled ? '#409EFF' : '#606266'}"
                      >
                        {{ $t('lang_pack.wx.lotNum') + ':' }}
                      </span>
                      <el-input
                        ref="lot_no"
                        v-model="lot_no"
                        type="text"
                        :disabled="popoverContent === ''"
                        clearable
                        size="small"
                        @keyup.native="handleKeyup"
                        @keydown.native="handleKeydown"
                      />
                    </el-form-item>
                    <el-button
                      type="primary"
                      style="width: 100px"
                      icon="el-icon-magic-stick"
                      :disabled="lot_no === ''"
                      @click="batchCheck()"
                    >{{ $t('lang_pack.wx.batchCheck') }}</el-button>
                  </div>
                </div>
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item :label="$t('lang_pack.wx.employeeID') + ':'">
                      <el-input
                        ref="user.nickName"
                        v-model="user.username"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </el-form>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0; height: 340px">
          <el-card shadow="never" class="wrapCard" style="background-color: #fff;height:340px;">
            <div slot="header" class="wrapTextSelect">
              <span>{{ $t('lang_pack.wx.feedbackInfo') }}</span>
              <el-button type="primary" size="small" @click="clearFeedbackInfo">{{ $t('lang_pack.wx.clear') || '清空' }}</el-button>
            </div>
            <div class="feedbackMsg">
              <div
                class="feedback-info"
                :style="{'color': esbInterLogData.isSuccess ? '#000' : '#FF0000'}"
              >
                <div>{{ $t('lang_pack.wx.userInfo') + ':' }}</div>
                <span v-html="esbInterLogData.messageContent || ''" />
              </div>
              <div
                class="feedback-info"
                :style="{'color': popoverContent.isSuccess ? '#000' : '#FF0000'}"
              >
                <div>{{ $t('lang_pack.wx.equipmentInfo') +':' }}</div>
                <span v-html="popoverContent.messageContent || ''" />
              </div>
              <div
                class="feedback-info"
                :style="{'color': esbInterLogData2.isSuccess ? '#000' : '#FF0000'}"
              >
                <div>{{ $t('lang_pack.wx.productResult') +':' }}</div>
                <span v-html="esbInterLogData2.messageContent || ''" />
              </div>
              <div
                class="feedback-info"
                :style="{'color': panelLog.isSuccess ? '#000' : '#FF0000'}"
              >
                <div>{{ $t('lang_pack.wx.panelInfo') +':' }}</div>
                <span v-html="panelLog.messageContent || ''" />
              </div>
              <div
                class="feedback-info"
                :style="{'color': paramLog.isSuccess ? '#000' : '#FF0000'}"
              >
                <div>{{ $t('lang_pack.wx.paramInfo') +':' }}</div>
                <span v-html="paramLog.messageContent || ''" />
              </div>
              <div
                class="feedback-info"
                :style="{'color': statusLog.isSuccess ? '#000' : '#FF0000','margin-bottom':'10px' }"
              >
                <div>{{ $t('lang_pack.wx.statusInfo') +':' }}</div>
                <span v-html="statusLog.messageContent || ''" />
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <el-card shadow="never" class="wrapCard">
              <div slot="header" class="wrapTextSelect">
                <span>{{ $t('lang_pack.wx.alarmMsg') }}</span>
                <el-button type="primary" @click="openAlarmMsg">{{ $t('lang_pack.hmiMain.more') }}</el-button>
              </div>
              <el-table
                ref="table"
                :cell-style="getCellStyle"
                border
                :data="alarmData"
                :row-key="(row) => row.id"
                :height="alarmHeight"
              >
                <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="station_code"
                  label="工位"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_code"
                  label="实例编号"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_des"
                  label="实例描述"
                /> -->
                <el-table-column :label="$t('lang_pack.cellprogupd.serialNumber')" type="index" width="50" />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_code"
                  :label="$t('lang_pack.wx.alarmCode')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_level"
                  :label="$t('lang_pack.wx.alarmLevel')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_des"
                  :label="$t('lang_pack.wx.alarmDesc')"
                  width="300"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="item_date"
                  :label="$t('lang_pack.wx.alarmTime')"
                  width="230"
                />
                <!-- <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_date"
                  label="复位时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_flag"
                  label="是否复位"
                >
                  <template slot-scope="scope">
                    {{ scope.row.reset_flag === "Y" ? "已复位" : "待复位" }}
                  </template>
                </el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="simulated_flag"
                  label="是否模拟"
                >
                  <template slot-scope="scope">
                    {{ scope.row.simulated_flag === "Y" ? "是" : "否" }}
                  </template>
                </el-table-column> -->
              </el-table>
            </el-card>
          </el-card></el-col>
      </el-col>
      <el-col :span="12" style="padding-right: 0">
        <el-col :span="24" style=" padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>{{ $t('lang_pack.wx.productInfo') }}</span>
            </div>
            <el-table ref="table" :data="[tableData]" border height="100">
              <el-table-column
                v-for="(item, index) in groupData"
                :key="index"
                :show-overflow-tooltip="true"
                align="center"
                :prop="item.value"
                :width="`${item.tag_des.length <=5 ? '120' : item.tag_des.length <=10 ? item.tag_des.length * 17 : item.tag_des.length * 15} `"
                :label="item.tag_des"
              >
                <template slot-scope="scope">
                  <div :ref="'headerText' + index">{{ item.value }}</div>
                  <!-- 显示对应value -->
                </template>
              </el-table-column></el-table>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>{{ $t('lang_pack.wx.producrParameter') }}</span>
            </div>
            <el-table
              ref="table"
              class="productTable"
              border
              :data="plcCraftData"
              :row-key="(row) => row.id"
              :height="gatherHeight"
            >
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                :label="$t('lang_pack.wx.projectName')"
                prop="tag_des"
                width="300"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="tag_value"
                :label="$t('lang_pack.wx.currentValue')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="status"
                :label="$t('lang_pack.wx.state')"
              >
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status === 'OK' ? 'success' : 'danger'">{{ scope.row.status }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="unit"
                :label="$t('lang_pack.wx.unit')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="down_limit"
                :label="$t('lang_pack.wx.lowerLimit')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="upper_limit"
                :label="$t('lang_pack.wx.upperLimit')"
              />
            </el-table>
          </el-card>
        </el-col>
      </el-col>
    </el-row>
    <el-dialog
      :show-close="true"
      :title="$t('lang_pack.wx.modifyFormula')"
      width="80%"
      :visible.sync="dialogVisible"
      class="dialogTable"
    >
      <div
        style="
          margin-bottom: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div v-if="$route.query.station_code !== 'OP1000'" style="width: 50%">
          <el-select v-model="recipeValue" @change="handleChange">
            <el-option
              v-for="item in recipeSelectData"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <span>{{ (thickness / 39.37).toFixed(2) }} +</span>
          <el-input
            v-model.number="surfaceValue"
            type="number"
            style="width: 150px; margin-left: 10px"
          />
          <span style="margin: 0 10px">=</span>
          <span>{{ parseFloat((thickness / 39.37).toFixed(2)) + surfaceValue }}
            {{ "mm" }}</span>
        </div>
        <div v-else style="width: 50%">
          <span>{{ $t('lang_pack.wx.thickness') }}：</span>
          <span>{{ parseFloat((thickness / 39.37).toFixed(2)) }} {{ "mm" }}</span>
        </div>
        <div>
          <span>{{ $t('lang_pack.wx.modifyParams') }}：</span>
          <el-switch
            v-model="disabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="crud.loading"
        border
        size="small"
        :data="crud.data"
        style="width: 100%"
        :cell-style="crud.cellStyle"
        height="478"
        max-height="478"
        highlight-current-row
        @header-dragend="crud.tableHeaderDragend()"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="45" align="center" />
        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
        <el-table-column
          v-if="1 == 0"
          width="10"
          prop="recipe_detail_id"
          label="id"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="parameter_code"
          :label="$t('lang_pack.wx.parameterCode')"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_des"
          :label="$t('lang_pack.wx.parameterDesc')"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_val"
          :label="$t('lang_pack.wx.parameterValue')"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.parameter_val"
              :disabled="handleDisabled(scope.row.parameter_val)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('lang_pack.commonPage.validIdentificationt')"
          align="center"
          prop="enable_flag"
          width="100"
        >
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ scope.row.enable_flag === "Y" ? $t('lang_pack.vie.effective') : $t('lang_pack.vie.invalid') }}
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('lang_pack.wx.cancel') }}</el-button>
        <el-button type="primary" @click="handleOk">{{ $t('lang_pack.wx.confirm') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="$t('lang_pack.hmiMain.CIMMessage')"
      width="50%"
      top="20px"
      :visible.sync="dialogCIMMsgVisible"
      :close-on-click-modal="false"
      class="elDialog"
    >
      <table class="table">
        <tr>
          <td class="label" style="width: 100px">
            {{ $t("lang_pack.messageReport.screen_code") + "：" }}
          </td>
          <td class="content">{{ screen_code }}</td>
        </tr>
        <tr>
          <td class="label">
            {{ $t("lang_pack.messageReport.cim_msg") + "：" }}
          </td>
          <td class="content">{{ cim_msg }}</td>
        </tr>
        <tr v-if="ConfirmBtnVisible">
          <td colspan="2" style="text-align: center">
            <el-button
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-check"
              @click="handleConfirmCIMMsg"
            >{{ $t("lang_pack.commonPage.confirm") }}</el-button>
          </td>
        </tr>
      </table>
    </el-dialog>
    <el-dialog
      :show-close="true"
      :title="$t('lang_pack.wx.alarmMsg')"
      width="90%"
      :visible.sync="dialogAlarmMsgVisible"
      class="dialogTable"
    >
      <report_scada_alarm ref="alarmScadaMsg" />
    </el-dialog>
  </div>
</template>
<script>
import {
  selLoginInfo,
  userLogin,
  userLogout
} from '@/api/eap/eapMeStationUser'
import { sel as eapRecipeSel } from '@/api/eap/project/sfjd/eapRecipe'
import eapRecipe from '@/api/wx/recipe/eapRecipe'
import { getWorkOrderInfo as selOrderInfo } from '@/api/eap/project/tlhs/eapRecipe'
import { selScadaTag } from '@/api/core/scada/tag'
import crudEapRecipeDetail from '@/api/eap/project/sfjd/eapRecipeDetail'
import { isShow } from '@/utils/permission'
import { scadaTagGroupTree } from '@/api/core/scada/tagGroup'
import { sel as selMaterial } from '@/api/eap/project/sfjd/eapMaterial'
import { eapCimMsgShow } from '@/api/eap/eapApsPlan'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import axios from 'axios'
import { selCellIP } from '@/api/core/center/cell'
import { sel as selStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { mapGetters } from 'vuex'
import report_scada_alarm from '@/views/core/scada/report/report_scada_alarm'
import { sel as selEsbInterLog } from '@/api/core/center/sysCoreEsbInterfLog'
const defaultForm = {}
export default {
  name: 'shRecipeMain',
  components: { report_scada_alarm },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 573,
      gatherHeight: document.documentElement.clientHeight - 370,
      alarmHeight: document.documentElement.clientHeight - 640,
      lot_no: '',
      material_code: '',
      lot_count: '0',
      current_user: '',
      recipeData: [],
      capacityDom: null,
      oeeDom: null,
      readbitRateDom: null,
      recipeTimer: null,
      timearr: [0, 0],
       tempTime: 0,
       isManualInputEnabled: false, // 是否允许手动输入
       labelClickCount: 0, // 标签点击次数
       labelClickTimer: null, // 标签点击定时器
       popoverContent: {
        isSuccess: true,
        messageContent: ''
      }, // 设备自检信息
      panelLog: {
        isSuccess: true,
        messageContent: ''
      }, // 扫码结果
      paramLog: {
        isSuccess: true,
        messageContent: ''
      }, // 参数上传
      statusLog: {
        isSuccess: true,
        messageContent: ''
      }, // 状态上传
      inTerfLog: {
        isSuccess: true,
        messageContent: ''
      }, // 接口异常信息
      exceptionLogs: {}, // 流程图异常信息
      capacityOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: '产能',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#6df320', '#d2e312'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: '脱岗' },
              { value: 234, name: '在岗' }
            ]
          }
        ]
      },
      oeeOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: 'OEE',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#409EFF', '#40e2ff'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: '脱岗' },
              { value: 234, name: '在岗' }
            ]
          }
        ]
      },
      readbitRateOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: '读码率',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#9d9727', '#c25b1f'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: '脱岗' },
              { value: 234, name: '在岗' }
            ]
          }
        ]
      },
      dialogVisible: false,
      dialogAlarmMsgVisible: false,
      rules: {
        material_code: [
          { required: true, message: '请输入物料编码', trigger: 'blur' }
        ],
        material_des: [
          { required: true, message: '请输入物料描述', trigger: 'blur' }
        ]
      },
      disabled: false,
      controlStatus: {
        plc_status: '0'
      },
      monitorData: {
        UpLink: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'UpLink',
          tag_des: '	[PlcStatus]上游设备连接',
          value: '0'
        },
        DownLink: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'DownLink',
          tag_des: '[PlcStatus]下游设备连接',
          value: '0'
        },
        PlcHeartBeat: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'PlcHeartBeat',
          tag_des: '[PlcStatus]PLC心跳',
          value: '0'
        },
        ElecConSum: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'ElecConSum',
          tag_des: '[PlcStatus]设备用电量',
          value: '0'
        },
        // LightGreen: {
        //   client_code: `Plc`,
        //   group_code: 'PlcStatus',
        //   tag_code: 'LightGreen',
        //   tag_des: '[PlcStatus]三色灯绿',
        //   value: '0'
        // },
        // LightYellow: {
        //   client_code: `Plc`,
        //   group_code: 'PlcStatus',
        //   tag_code: 'LightYellow',
        //   tag_des: '[PlcStatus]三色灯红',
        //   value: '0'
        // },
        // LightRed: {
        //   client_code: `Plc`,
        //   group_code: 'PlcStatus',
        //   tag_code: 'LightRed',
        //   tag_des: '[PlcStatus]设备用电量',
        //   value: '0'
        // },
        LightStatus: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightStatus',
          tag_des: '[PlcStatus]三色灯状态(1红灯、2黄灯、3绿灯)',
          value: '0'
        },
        ControlMode: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'DeviceMode',
          tag_des: '[PlcStatus]设备控制状态(1:离线,2:在线/本地,3:在线/远程)',
          value: '0'
        },
        ProductMode: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'ProductMode',
          tag_des: '[PlcStatus]设备生产模式(0:量产,1:首件,2:Dummy)',
          value: '0'
        },
        RecipeUpd: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'RecipeUpd',
          tag_des: '[PlcStatus]配方修改',
          value: '0'
        },
        RecipeSelFinish: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'RecipeSelFinish',
          tag_des: '[PlcStatus]配方选择完成',
          value: '0'
        },
        OnOffLine: {
          client_code: `Ais`,
          group_code: 'AisStatus',
          tag_code: 'OnOffLine',
          tag_des: '[AisStatus]在线/离线模式',
          value: ''
        },
        ApplyEapUploadBatchCheck: {
          client_code: `Ais`,
          group_code: 'AisStatus',
          tag_code: 'ApplyEapUploadBatchCheck',
          tag_des: '[AisStatus]申请产品批次验证',
          value: ''
        },
        UploadBatchCheckResult: {
          client_code: `Ais`,
          group_code: 'AisStatus',
          tag_code: 'UploadBatchCheckResult',
          tag_des: '[AisStatus]EAP响应批次校验结果',
          value: ''
        },
        // 操作模式
        ManualMode: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'ManualMode',
          tag_des: '[ControlGroup]手动模式',
          value: '0'
        },
        AutoMode: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'AutoMode',
          tag_des: '[ControlGroup]自动模式',
          value: '0'
        },
        // 启动状态
        AutoStart: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'AutoStart',
          tag_des: '[ControlGroup]自动开启',
          value: '0'
        },
        McStop: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'McStop',
          tag_des: '[ControlGroup]系统停止',
          value: '0'
        },
        // 加热状态
        HeaterRun: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'HeaterRun',
          tag_des: '[ControlGroup]加热启动',
          value: '0'
        },
        HeaterStop: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'HeaterStop',
          tag_des: '[ControlGroup]加热停止',
          value: '0'
        },
        // 指示灯
        LampManual: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'LampManual',
          tag_des: '[ControlGroup]手动模式指示灯',
          value: '0'
        },
        LampAutoMode: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'LampAutoMode',
          tag_des: '[ControlGroup]自动模式指示灯',
          value: '0'
        },
        LampAutoStart: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'LampAutoStart',
          tag_des: '[ControlGroup]自动启用指示灯',
          value: '0'
        },
        LampHeaterStop: {
          client_code: `Plc`,
          group_code: 'ControlGroup',
          tag_code: 'LampHeaterStop',
          tag_des: '[ControlGroup]加热停止指示灯',
          value: '0'
        }
      },

      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: '',
        shift_id: ''
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      alarmData: [],
      materialData: [],
      timer: null,
      groupData: [],
      columnWidths: [],
      productData: [],
      plcCraftData: [],
      maxLength: 5,
      stationAttr: '',
      client_id_list: '',
      tableData: {},
      thickness: 0,
      recipeValue: 7,
      surfaceValue: 7,
      recipeSelectData: [
        { label: '选择填孔+', value: 7 },
        { label: '选择表面+', value: 11 }
      ],
      // CIM消息相关
      queryCim: true,
      dialogCIMMsgVisible: false,
      ConfirmBtnVisible: false,
      screen_code: '',
      cim_msg: '',
      messageList: [],
      messageShow: false,
      messageContent: '',
      MessageLevel: 'info',
      isShowFlag: false,
      deviceSelfTestTimer: null, // 添加设备自检定时器
      esbInterLogData: {
        isSuccess: true,
        messageContent: ''
      },
      esbInterLogData2: {
        isSuccess: true,
        messageContent: ''
      },
      // 反馈信息清空相关
      feedbackStartDate: null // 反馈信息查询的起始时间
    }
  },
  cruds() {
    return CRUD({
      title: '配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_detail_id ',
      // 排序
      sort: ['recipe_detail_id asc'],
      // CRUD Method
      crudMethod: { ...crudEapRecipeDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  dicts: ['PROJECT_PARAMS_WARNING'],
  computed: {
    ...mapGetters(['user'])
  },
  mounted: function() {
    // 首先更根据工位号查询，获取到所要查询的点位实例
    this.getStationAttr()
    const that = this
    this.getMaterialInfo()
    this.timer = setInterval(this.getAlarmData, 5000)
    // this.timer = setInterval(this.getTagValue, 5000)
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 608
      that.gatherHeight = document.documentElement.clientHeight - 368
      that.alarmHeight = document.documentElement.clientHeight - 640
    }
    this.timer = setInterval(this.messageScroll, 5000)
    // this.$nextTick(() => {
    //   this.getCapacity()
    //   this.getOee()
    //   this.getreadbitRate()
    // })
    this.$refs.lot_no.focus()
    
    // 启动用户活动监听，用于跨页面倒计时重置
    this.startUserActivityListener()
  },
  beforeDestroy() {
    clearInterval(this.timer)
    if (this.deviceSelfTestTimer) {
      clearInterval(this.deviceSelfTestTimer)
    }
    // 停止用户活动监听
    this.stopUserActivityListener()
  },
  async created() {
    this.getStationData()
    // this.handleMouseEnter()
    this.getUploadUserLoginLog()
    this.recipeTimer = setInterval(() => {
      this.getUploadBatchCheckLog()
      this.getUploadPanelIdCheckLog()
      this.getUploadProductionDataLog()
      this.getUploadDeviceStatusLog()
      // this.getTagValue()
    }, 8000)
    // 首次进入页面立即执行一次设备自检
    this.handleDevice()
    // 添加2小时定时调用设备自检
    this.deviceSelfTestTimer = setInterval(() => {
      this.handleDevice()
    }, 2 * 60 * 60 * 1000)
    setTimeout(() => {
      this.setupReadonlyFlag()
    }, 1000)
    
    // 初始化反馈信息查询起始时间为当前时间，避免查询历史数据
    const now = new Date()
    this.feedbackStartDate = now.getFullYear() + '-' + 
      String(now.getMonth() + 1).padStart(2, '0') + '-' + 
      String(now.getDate()).padStart(2, '0') + ' ' + 
      String(now.getHours()).padStart(2, '0') + ':' + 
      String(now.getMinutes()).padStart(2, '0') + ':' + 
      String(now.getSeconds()).padStart(2, '0')
    
    // 首次进入页面时立即获取反馈信息（基于当前时间过滤）
    this.getUploadUserLoginLog()
    this.getDeviceSelfCheck()
    this.getUploadBatchCheckLog()
    this.getUploadPanelIdCheckLog()
    this.getUploadProductionDataLog()
    this.getUploadDeviceStatusLog()
  },
  methods: {












    // 清空反馈信息
    clearFeedbackInfo() {
      // 更新起始时间为当前时间
      const now = new Date()
      this.feedbackStartDate = now.getFullYear() + '-' + 
        String(now.getMonth() + 1).padStart(2, '0') + '-' + 
        String(now.getDate()).padStart(2, '0') + ' ' + 
        String(now.getHours()).padStart(2, '0') + ':' + 
        String(now.getMinutes()).padStart(2, '0') + ':' + 
        String(now.getSeconds()).padStart(2, '0')
      
      // 清空所有反馈信息
      this.esbInterLogData = {
        isSuccess: true,
        messageContent: ''
      }
      this.popoverContent = {
        isSuccess: true,
        messageContent: ''
      }
      this.esbInterLogData2 = {
        isSuccess: true,
        messageContent: ''
      }
      this.panelLog = {
        isSuccess: true,
        messageContent: ''
      }
      this.paramLog = {
        isSuccess: true,
        messageContent: ''
      }
      this.statusLog = {
        isSuccess: true,
        messageContent: ''
      }
      
      this.$message({
        type: 'success',
        message: '反馈信息已清空'
      })
    },



    // 获取当前控制模式文本
    getControlModeText() {
      if (this.monitorData.ManualMode.value === '1') {
        return this.$t('tlwx.manualMode')
      } else if (this.monitorData.AutoMode.value === '1') {
        return this.$t('tlwx.autoMode')
      } else {
        return this.$t('tlwx.selectMode')
      }
    },
    // 处理控制模式变更
    handleControlModeChange(command) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('tlwx.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }

      let tagKey = ''
      let tagValue = ''

      if (command === 'manual') {
        // 设置为手动模式
        tagKey = `${this.stationAttr}Plc/ControlGroup/ManualMode`
        tagValue = '1'
        // 同时将自动模式设为0
        this.handleWrite(`${this.stationAttr}Plc/ControlGroup/AutoMode`, '0')
      } else if (command === 'auto') {
        // 设置为自动模式
        tagKey = `${this.stationAttr}Plc/ControlGroup/AutoMode`
        tagValue = '1'
        // 同时将手动模式设为0
        this.handleWrite(`${this.stationAttr}Plc/ControlGroup/ManualMode`, '0')
      }

      this.handleWrite(tagKey, tagValue)
    },

    // 处理启动状态变更
    handleStartStatusChange(command) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.tlwx.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }

      let tagKey = ''
      let tagValue = ''

      if (command === 'start') {
        // 设置为自动开启
        tagKey = `${this.stationAttr}Plc/ControlGroup/AutoStart`
        tagValue = '1'
        // 同时将系统停止设为0
        this.handleWrite(`${this.stationAttr}Plc/ControlGroup/McStop`, '0')
      } else if (command === 'stop') {
        // 设置为系统停止
        tagKey = `${this.stationAttr}Plc/ControlGroup/McStop`
        tagValue = '1'
        // 同时将自动开启设为0
        this.handleWrite(`${this.stationAttr}Plc/ControlGroup/AutoStart`, '0')
      }

      this.handleWrite(tagKey, tagValue)
    },

    // 处理加热状态变更
    handleHeaterStatusChange(command) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.tlwx.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }

      let tagKey = ''
      let tagValue = ''

      if (command === 'run') {
        // 设置为加热启动
        tagKey = `${this.stationAttr}Plc/ControlGroup/HeaterRun`
        tagValue = '1'
        // 同时将加热停止设为0
        this.handleWrite(`${this.stationAttr}Plc/ControlGroup/HeaterStop`, '0')
      } else if (command === 'stop') {
        // 设置为加热停止
        tagKey = `${this.stationAttr}Plc/ControlGroup/HeaterStop`
        tagValue = '1'
        // 同时将加热启动设为0
        this.handleWrite(`${this.stationAttr}Plc/ControlGroup/HeaterRun`, '0')
      }

      this.handleWrite(tagKey, tagValue)
    },

    userMsg() {
      return Cookies.get('userMsg')
    },
    openAlarmMsg() {
      this.dialogAlarmMsgVisible = true
    },
    getCellStyle({ row, column, rowIndex, columnIndex }) {
      if (row.simulated_flag === 'Y') {
        return { backgroundColor: '#ff0000', color: '#fff' }
      } else {
        return { backgroundColor: '#eeeeee' }
      }
    },
    async setupReadonlyFlag() {
      this.isShowFlag = await isShow()
      this.crud.optShow.add = !this.isShowFlag
    },
    getUploadUserLoginLog() {
      const query = {
        currentPage: 1,
        page: 1,
        size: 1,
        sort: 'item_date desc',
        tableSize: 1,
        esb_interf_code: 'UploadUserLogin', // 员工登录
        user_name: Cookies.get('userName'),
        item_date: [this.feedbackStartDate, '2222-12-31 23:59:59'] // 使用item_date数组格式，结束时间固定为2222年
      }
      selEsbInterLog(query).then((res) => {
        this.esbInterLogData = {
          isSuccess: true,
          messageContent: ''
        }
        if (res && res.code === 0 && res.data && res.data.length > 0) {
          const firstItem = res.data[0]
          if (firstItem && firstItem.response_paras) {
            try {
              var response_paras = JSON.parse(firstItem.response_paras)
              if (response_paras) {
                const messageContent = response_paras.messageContent || ''
                response_paras.messageContent = messageContent.replace(/(\\r\\n|\r\n)/g, '<br>')
                this.esbInterLogData = response_paras
              }
            } catch (e) {
              console.error('解析 response_paras 失败:', e)
            }
          }
        }
        // if (res.code === 0 && res.data.length > 0) {
        //   // res.data[0].response_paras = '{\"message_content"\:"登录成功\",\"is_success\":true}'
        //   const response_paras = res.data.length && res.data[0].response_paras && JSON.parse(res.data[0].response_paras)
        //   response_paras.messageContent = response_paras.messageContent && response_paras.messageContent.replace(/\\r\\n/g, '<br>').replace(/\r\n/g, '<br>')
        //   this.esbInterLogData = response_paras || {
        //     isSuccess: true,
        //     messageContent: ''
        //   }
        //   return
        // }
      })
    },
    getDeviceSelfCheck() {
      const query = {
        currentPage: 1,
        page: 1,
        size: 1,
        sort: 'item_date desc',
        tableSize: 1,
        esb_interf_code: 'DeviceSelfCheck', // 设备自检
        user_name: Cookies.get('userName'),
        item_date: [this.feedbackStartDate, '2222-12-31 23:59:59'] // 使用item_date数组格式，结束时间固定为2222年
      }
      selEsbInterLog(query).then((res) => {
        this.popoverContent = {
          isSuccess: true,
          messageContent: ''
        }
        if (res && res.code === 0 && res.data && res.data.length > 0) {
          const firstItem = res.data[0]
          if (firstItem && firstItem.response_paras) {
            try {
              var response_paras = JSON.parse(firstItem.response_paras)
              if (response_paras) {
                const messageContent = response_paras.messageContent || ''
                response_paras.messageContent = messageContent.replace(/(\\r\\n|\r\n)/g, '<br>')
                this.popoverContent = response_paras
              }
            } catch (e) {
              console.error('解析 response_paras 失败:', e)
            }
          }
        }
        // if (res.code === 0 && res.data.length > 0) {
        //   const response_paras = res.data.length && res.data[0].response_paras && JSON.parse(res.data[0].response_paras)
        //   response_paras.messageContent = response_paras.messageContent && response_paras.messageContent.replace(/\\r\\n/g, '<br>').replace(/\r\n/g, '<br>')
        //   this.popoverContent = response_paras || {
        //     isSuccess: true,
        //     messageContent: ''
        //   }
        //   return
        // }
      })
    },
    getUploadBatchCheckLog() {
      const query = {
        currentPage: 1,
        page: 1,
        size: 1,
        sort: 'item_date desc',
        tableSize: 1,
        esb_interf_code: 'UploadBatchCheck', // 产品结果
        user_name: Cookies.get('userName'),
        item_date: [this.feedbackStartDate, '2222-12-31 23:59:59'] // 使用item_date数组格式，结束时间固定为2222年
      }
      selEsbInterLog(query).then((res) => {
        this.esbInterLogData2 = {
          isSuccess: true,
          messageContent: ''
        }
        if (res && res.code === 0 && res.data && res.data.length > 0) {
          const firstItem = res.data[0]
          if (firstItem && firstItem.response_paras) {
            try {
              var response_paras = JSON.parse(firstItem.response_paras)
              if (response_paras) {
                const messageContent = response_paras.messageContent || ''
                response_paras.messageContent = messageContent.replace(/(\\r\\n|\r\n)/g, '<br>')
                this.esbInterLogData2 = response_paras
              }
            } catch (e) {
              console.error('解析 response_paras 失败:', e)
            }
          }
        }
        // if (res.code === 0 && res.data.length > 0) {
        //   const response_paras = res.data[0].response_paras && JSON.parse(res.data[0].response_paras)
        //   response_paras.messageContent = response_paras.messageContent.replace(/\\r\\n/g, '<br>').replace(/\r\n/g, '<br>')
        //   this.esbInterLogData2 = response_paras || {
        //     isSuccess: true,
        //     messageContent: ''
        //   }
        //   return
        // }
        // this.esbInterLogData2 = {
        //   isSuccess: true,
        //   messageContent: ''
        // }
      })
    },
    getUploadPanelIdCheckLog() {
      const query = {
        currentPage: 1,
        page: 1,
        size: 1,
        sort: 'item_date desc',
        tableSize: 1,
        esb_interf_code: 'UploadPanelIdCheck', // 扫码结果
        user_name: Cookies.get('userName'),
        item_date: [this.feedbackStartDate, '2222-12-31 23:59:59'] // 使用item_date数组格式，结束时间固定为2222年
      }
      selEsbInterLog(query).then((res) => {
        this.panelLog = {
          isSuccess: true,
          messageContent: ''
        }
        if (res && res.code === 0 && res.data && res.data.length > 0) {
          const firstItem = res.data[0]
          if (firstItem && firstItem.response_paras) {
            try {
              var response_paras = JSON.parse(firstItem.response_paras)
              if (response_paras) {
                const messageContent = response_paras.messageContent || ''
                response_paras.messageContent = messageContent.replace(/(\\r\\n|\r\n)/g, '<br>')
                this.panelLog = response_paras
              }
            } catch (e) {
              console.error('解析 response_paras 失败:', e)
            }
          }
        }
        // if (res.code === 0 && res.data.length > 0) {
        //   const response_paras = res.data.length && res.data[0].response_paras && JSON.parse(res.data[0].response_paras)
        //   response_paras.messageContent = response_paras.messageContent.replace(/\\r\\n/g, '<br>').replace(/\r\n/g, '<br>')
        //   this.panelLog = response_paras || {
        //     isSuccess: true,
        //     messageContent: ''
        //   }
        //   return
        // }
        // this.panelLog = {
        //   isSuccess: true,
        //   messageContent: ''
        // }
      })
    },
    getUploadProductionDataLog() {
      const query = {
        currentPage: 1,
        page: 1,
        size: 1,
        sort: 'item_date desc',
        tableSize: 1,
        esb_interf_code: 'UploadDeviceParam', // 参数上传
        user_name: Cookies.get('userName'),
        item_date: [this.feedbackStartDate, '2222-12-31 23:59:59'] // 使用item_date数组格式，结束时间固定为2222年
      }
      selEsbInterLog(query).then((res) => {
        this.paramLog = {
          isSuccess: true,
          messageContent: ''
        }
        if (res && res.code === 0 && res.data && res.data.length > 0) {
          const firstItem = res.data[0]
          if (firstItem && firstItem.response_paras) {
            try {
              var response_paras = JSON.parse(firstItem.response_paras)
              if (response_paras) {
                const messageContent = response_paras.messageContent || ''
                response_paras.messageContent = messageContent.replace(/(\\r\\n|\r\n)/g, '<br>')
                this.paramLog = response_paras
              }
            } catch (e) {
              console.error('解析 response_paras 失败:', e)
            }
          }
        }
        // if (res.code === 0 && res.data.length > 0) {
        //   const response_paras = res.data[0].response_paras && JSON.parse(res.data[0].response_paras)
        //   response_paras.messageContent = response_paras.messageContent.replace(/\\r\\n/g, '<br>').replace(/\r\n/g, '<br>')
        //   this.paramLog = response_paras || {
        //     isSuccess: true,
        //     messageContent: ''
        //   }
        //   return
        // }
      })
    },
    getUploadDeviceStatusLog() {
      const query = {
        currentPage: 1,
        page: 1,
        size: 1,
        sort: 'item_date desc',
        tableSize: 1,
        esb_interf_code: 'UploadDeviceStatus', // 状态上传
        user_name: Cookies.get('userName'),
        item_date: [this.feedbackStartDate, '2222-12-31 23:59:59'] // 使用item_date数组格式，结束时间固定为2222年
      }
      selEsbInterLog(query).then((res) => {
        this.statusLog = {
          isSuccess: true,
          messageContent: ''
        }
        if (res && res.code === 0 && res.data && res.data.length > 0) {
          const firstItem = res.data[0]
          if (firstItem && firstItem.response_paras) {
            try {
              var response_paras = JSON.parse(firstItem.response_paras)
              if (response_paras) {
                const messageContent = response_paras.messageContent || ''
                response_paras.messageContent = messageContent.replace(/(\\r\\n|\r\n)/g, '<br>')
                this.statusLog = response_paras
              }
            } catch (e) {
              console.error('解析 response_paras 失败:', e)
            }
          }
        }
        // if (res.code === 0 && res.data.length > 0) {
        //   const response_paras = res.data[0].response_paras && JSON.parse(res.data[0].response_paras)
        //   this.statusLog = response_paras || {
        //     isSuccess: true,
        //     messageContent: ''
        //   }
        //   return
        // }
      })
    },
    getNodeExceptionLogs() {
      eapRecipe
        .getLatestInterfLog({ station_id: this.$route.query.station_id })
        .then((res) => {
          if (res.code === 0 && res.data.length > 0) {
            this.inTerfLog = res.data[0]
            return
          }
          this.inTerfLog = {}
        })
    },
    getNodeInterfLog() {
      eapRecipe
        .nodeExceptionLogs({ station_id: this.$route.query.station_id })
        .then((res) => {
          if (res.code === 0 && res.data.length > 0) {
            this.exceptionLogs = res.data[0]
            return
          }
          this.exceptionLogs = {}
        })
    },
    handleKeydown(event) {
      // 如果启用了手动输入模式，允许所有输入
      if (this.isManualInputEnabled) {
        this.timearr[0] = new Date().getTime()
        return
      }
      
      // 扫描枪模式：只允许功能按键
      const allowedKeys = [
        'Delete', 'Backspace', 'Tab', 'Enter', 'Escape',
        'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown',
        'Home', 'End', 'PageUp', 'PageDown'
      ]
      
      // 阻止所有非功能按键的输入
      if (!allowedKeys.includes(event.key)) {
        event.preventDefault()
        return
      }
      
      this.timearr[0] = new Date().getTime()
    },
    handleKeyup(event) {
      this.timearr[1] = new Date().getTime()
      
      // 如果启用了手动输入模式，不进行速度检测
      if (this.isManualInputEnabled) {
        return
      }
      
      // 扫描枪模式：检测输入速度
      const inputSpeed = this.timearr[1] - this.timearr[0]
      
      // 如果输入速度太慢（超过80ms），可能是手动输入，清空内容
      if (inputSpeed > 80) {
        this.lot_no = ''
      } else if (this.tempTime !== 0 && this.timearr[1] - this.tempTime > 80) {
        this.lot_no = ''
        this.tempTime = this.timearr[0]
      }
      
      this.tempTime = this.timearr[0]
    },
    
    // 处理标签点击（用于三击检测）
    handleLabelClick() {
      if (!this.labelClickTimer) {
        this.labelClickCount = 0
      }
      
      this.labelClickCount++
      
      // 清除之前的定时器
      if (this.labelClickTimer) {
        clearTimeout(this.labelClickTimer)
      }
      
      // 设置新的定时器，500ms内检测点击次数
      this.labelClickTimer = setTimeout(() => {
        if (this.labelClickCount >= 3) {
          this.toggleInputMode()
        }
        this.labelClickCount = 0
        this.labelClickTimer = null
      }, 500)
    },
    
    // 切换输入模式
     toggleInputMode() {
       this.isManualInputEnabled = !this.isManualInputEnabled
       
       // 清空输入框
       this.lot_no = ''
     },

    handleChange(val) {
      this.surfaceValue = val
    },
    getStationAttr() {
      const query = {
        stationCodeDes: this.$route.query.station_code,
        user_name: Cookies.get('userName')
      }
      selStation(query).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.stationAttr = res.data[0].station_attr
            this.client_id_list = res.data[0].client_id_list
            this.monitorData && Object.keys(this.monitorData).forEach((key) => {
              this.monitorData[key].client_code =
                `${this.stationAttr}` + this.monitorData[key].client_code
            })
            this.getScadaData()
            return
          }
          this.stationAttr = ''
        }
      })
    },
    getScadaData() {
      const query = {
        client_id: this.client_id_list,
        enable_flag: 'Y',
        sort: 'tag_group_id',
        user_name: Cookies.get('userName')
      }
      scadaTagGroupTree(query).then((res) => {
        if (res.data.length > 0) {
          const data = res.data.find(
            (item) => item.tag_group_code === 'PlcRecipe'
          )
          console.log(this.stationAttr, '1111')
          if (data && data.children.length > 0) {
            data.children.forEach((e) => {
              this.groupData.push({
                tag_key: `${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`,
                tag_des: e.tag_des,
                value: ''
              })
            })
            this.tableData = data.children.reduce((acc, e) => {
              acc[`${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`] =
                ''
              return acc
            }, {})
          }
        }
      })
      const params = {
        tableOrder: 'asc',
        tableOrderField: 'tag_id',
        tablePage: 1,
        tableSize: 1000,
        tag_group_id: this.client_id_list + '04',
        user_name: Cookies.get('userName')
      }
      selScadaTag(params).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              this.maxLength = item.tag_des.length > this.maxLength ? item.tag_des.length : this.maxLength
              const result = {
                tag_des: item.tag_des,
                tag_key: `${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}`,
                tag_value: '',
                unit: item.opc_addr,
                down_limit: item.down_limit,
                upper_limit: item.upper_limit,
                status: ''
              }
              this.plcCraftData.push(result)
            })
          }
        }
      })
    },
    // CIM消息处理功能
    handleConfirmCIMMsg() {
      this.queryCim = true
      this.dialogCIMMsgVisible = false
    },
    messageScroll() {
      if (this.messageList.length > 0) {
        this.messageShow = false
        setTimeout(() => {
          this.messageShow = true
          this.messageList.push(this.messageList[0]) // 将数组的第一个元素追加到数组最后面
          this.messageList.shift() // 然后删除数组的第一个元素
          this.messageContent = this.messageList[0].content
          this.MessageLevel = this.messageList[0].level
        }, 300)
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      }
      if (this.queryCim) {
        eapCimMsgShow(query)
          .then((res) => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.count !== 0) {
                var msgInfo = defaultQuery.data[0]
                this.screen_code = msgInfo.screen_code
                this.cim_msg = msgInfo.cim_msg
                var interval_second_time = msgInfo.interval_second_time
                if (msgInfo.screen_control === '0') {
                  this.ConfirmBtnVisible = false
                  var _time = setTimeout(() => {
                    this.queryCim = true
                    this.dialogCIMMsgVisible = false
                    clearTimeout(_time)
                  }, interval_second_time * 1000)
                } else {
                  this.ConfirmBtnVisible = true
                }
                this.queryCim = false
                this.dialogCIMMsgVisible = true
              }
            }
          })
          .catch(() => {})
      }
    },
    getMaterialInfo() {
      const query = {
        page: 1,
        size: 1000,
        sort: 'material_id desc',
        user_name: Cookies.get('userName')
      }
      selMaterial(query)
        .then((res) => {
          if (res.code === 0) {
            if (res.data.length > 0) {
              this.materialData = res.data
              return
            }
            this.materialData = []
            this.$message({
              type: 'warning',
              message: this.$t('lang_pack.wx.currently')
            })
          }
        })
        .catch((error) => {
          this.materialData = []
          this.$message({ type: 'error', message: this.$t('lang_pack.wx.queryFailed') + error.msg })
        })
    },
    clientCodeSelect(e) {
      const query = {
        page: 1,
        size: 10,
        sort: 'recipe_id asc',
        enable_flag: 'Y',
        material_code: e
      }
      eapRecipeSel(query)
        .then((res) => {
          if (res.code === 0) {
            if (res.data.length > 0) {
              this.query.recipe_id = res.data[0].recipe_id
              this.query.size = 1000
              this.$nextTick(() => {
                this.crud.toQuery()
                this.dialogVisible = true
              })
              return
            }
            this.$message({
              type: 'error',
              message: this.$t('lang_pack.wx.failedMaterial') + `【${e}】` + this.$t('lang_pack.wx.foundRecipe')
            })
          }
        })
        .catch((error) => {
          this.$message({ type: 'error', message: this.$t('lang_pack.wx.queryFailed') + error.msg })
        })
    },
    manInput() {
      if (!this.lot_no) {
        this.$message({ type: 'warning', message: this.$t('lang_pack.wx.pleaseScan') })
        return
      }
      var queryParameter = {
        user_name: Cookies.get('userName'),
        station_code: this.$route.query.station_code,
        make_order: this.lot_no,
        user_id: this.user.username
      }
      selOrderInfo(queryParameter)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              this.crud.data = defaultQuery.data
              const pointVal =
                this.$route.query.station_code === 'OP1000'
                  ? 'Paras41'
                  : 'Paras25'
              const data = defaultQuery.data.filter(
                (item) =>
                  item.tag_key === `${this.stationAttr}Plc/PcRecipe/${pointVal}`
              )
              if (data.length > 0) {
                this.thickness = Number(data[0].parameter_val)
              }
            }
            this.dialogVisible = true
          }
        })
        .catch(() => {
          this.thickness = 0
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    choiceLotNum() {
      this.dialogVisible = true
    },
    handleDisabled(value) {
      if (this.disabled) return false
      const Arr = ['null', '', 'undefined', '0']
      if (!value || Arr.includes(value)) return true
    },
    handleWrite(key, value) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: key,
        TagValue: value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + key.split('/')[0]
      this.sendMessage(topic, sendStr)
    },
    handleOk() {
      var rowJson = []
      var sendJson = {}
      var sendStr = {}
      var topic = ''
      // 1.读取plc修改配方，判断是否允许下发配方
      if (this.monitorData.RecipeUpd.value === '0') {
        this.dialogVisible = false
        this.$message({
          type: 'warning',
          message: 'Device Is Running，Do Not Allow DownRecipe'
        })
        var newRow = {
          TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
          TagValue: '0'
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        sendStr = JSON.stringify(sendJson)
        topic = `SCADA_WRITE/${this.stationAttr}Plc`
        this.sendMessage(topic, sendStr)
        return
      }

      const writeValue = (item) => {
        const code = this.$route.query.station_code
        const pointVal = code === 'OP1000' ? 'Paras41' : 'Paras25'
        if (
          code === 'OP1000' &&
          item.tag_key === `${this.stationAttr}Plc/PcRecipe/${pointVal}`
        ) {
          return parseFloat((this.thickness / 39.37).toFixed(2))
        } else if (
          code !== 'OP1000' &&
          item.tag_key === `${this.stationAttr}Plc/PcRecipe/${pointVal}`
        ) {
          return (
            parseFloat((this.thickness / 39.37).toFixed(2)) + this.surfaceValue
          )
        } else {
          return item.parameter_val
        }
      }
      // 2.请求下发配方
      this.crud.data.forEach((item) => {
        var newRow2 = {
          TagKey: item.tag_key,
          TagValue: writeValue(item)
        }
        rowJson.push(newRow2)
      })
      var newRow1 = {
        TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
        TagValue: '1'
      }

      rowJson.push(newRow1)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      sendStr = JSON.stringify(sendJson)
      topic = `SCADA_WRITE/${this.stationAttr}Plc`
      this.sendMessage(topic, sendStr)
      this.$message({ type: 'success', message: 'DownRecipe Success!' })
      // 3.间隔3秒后进行复位
      rowJson = []
      sendJson = {}
      sendStr = {}
      var newRow3 = {
        TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
        TagValue: '0'
      }
      rowJson.push(newRow3)
      // 写入到网板间距点位里面去

      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      sendStr = JSON.stringify(sendJson)
      topic = `SCADA_WRITE/${this.stationAttr}Plc`
      setTimeout(() => {
        this.dialogVisible = false
        this.sendMessage(topic, sendStr)
      }, 3000)
    },
    getCapacity() {
      this.capacityDom = this.$echarts.init(
        document.getElementById('capacityDom')
      )
      var that = this
      this.capacityDom.setOption(this.capacityOption)
      window.addEventListener('resize', function() {
        that.capacityDom.resize()
      })
    },
    getOee() {
      this.oeeDom = this.$echarts.init(document.getElementById('oeeDom'))
      var that = this
      this.oeeDom.setOption(this.oeeOption)
      window.addEventListener('resize', function() {
        that.oeeDom.resize()
      })
    },
    getreadbitRate() {
      this.readbitRateDom = this.$echarts.init(
        document.getElementById('readbitRateDom')
      )
      var that = this
      this.readbitRateDom.setOption(this.readbitRateOption)
      window.addEventListener('resize', function() {
        that.readbitRateDom.resize()
      })
    },
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        cell_id: this.$route.query.cell_id
      }
      this.getCellIp()
      this.getLoginInfo()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            setTimeout(() => {
              this.toStartWatch()
              this.getTagValue()
              this.getAlarmData()
            }, 1000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.wx.queryFailed'), type: 'error' })
        })
    },
    // 获取当前登录信息
    getLoginInfo() {
      this.loginInfo.user_name = '---'
      this.loginInfo.nick_name = '---'
      this.loginInfo.dept_id = '---'
      this.loginInfo.shift_id = '---'
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selLoginInfo(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.hmiMain.procedure'),
            type: 'error'
          })
        })
    },
    // // 提示信息
    // async handleMouseEnter() {
    //   await this.fetchContent()
    // },
    handleMouseLeave() {},
    // async fetchContent() {
    //   try {
    //     const res = await eapRecipe.selectEquipStatusInfo({})
    //     const defaultQuery = JSON.parse(JSON.stringify(res))
    //     if (defaultQuery.code === 0) {
    //       // defaultQuery.result = '{\"messageId\":\"534915BFC48B4969A6B0A418882A589C\",\"messageCode\":\"90001\",\"resourceName\":\"2P-DES-002\",\"isSuccess\":false,\"serverName\":\"UCVWMFCAP01\",\"messageContent\":\"设备[2P-DES-002]Buyoff通过。\\r\\n设备[2P-DES-002]无叫修任务。\\r\\n设备[2P-DES-002]保养有效期至[2025-04-02 08:00:00]。\\r\\n设备[2P-DES-002]未锁定。\\r\\n\"}'
    //       if (defaultQuery.result !== '') {
    //         const result = defaultQuery.result.replace(
    //           /\\r\\n/g,
    //           '<br>'
    //         )
    //         this.popoverContent = JSON.parse(result)
    //       }
    //     } else {
    //       this.popoverContent = {
    //         isSuccess: false,
    //         messageContent: res.msg
    //       }
    //     }
    //   } catch (error) {
    //     this.$message({
    //       message: this.$t('lang_pack.vie.queryException'),
    //       type: 'error'
    //     })
    //   }
    // },

    // 启动用户活动监听
     startUserActivityListener() {
       const events = [
         'mousedown', 'mousemove', 'mouseup',
         'keydown', 'keyup', 'keypress',
         'scroll', 'wheel',
         'touchstart', 'touchmove', 'touchend',
         'click', 'dblclick',
         'focus', 'blur',
         'input', 'change'
       ]
       
       events.forEach(event => {
         document.addEventListener(event, this.handleUserActivity, {
           passive: true,
           capture: true
         })
       })
       
       //console.log('tlwx页面：用户活动监听已启动，支持跨页面倒计时重置')
     },
     
     // 停止用户活动监听
     stopUserActivityListener() {
       const events = [
         'mousedown', 'mousemove', 'mouseup',
         'keydown', 'keyup', 'keypress',
         'scroll', 'wheel',
         'touchstart', 'touchmove', 'touchend',
         'click', 'dblclick',
         'focus', 'blur',
         'input', 'change'
       ]
       
       events.forEach(event => {
         document.removeEventListener(event, this.handleUserActivity, true)
       })
     },
     
     // 处理用户活动
     handleUserActivity(event) {
       // 检查是否有倒计时激活
       const isCountdownActive = localStorage.getItem('lockscreen_countdown_active') === 'true'
       
       if (isCountdownActive) {
         // 更新localStorage中的重置时间
         const resetTime = Date.now()
         localStorage.setItem('lockscreen_reset_time', resetTime.toString())
         
         // 开发环境下输出详细信息
         if (process.env.NODE_ENV === 'development') {
           console.log(' tlwx页面检测到用户活动，已更新localStorage重置时间:', {
             eventType: event.type,
             target: event.target ? event.target.tagName : 'unknown',
             resetTime: new Date(resetTime).toLocaleTimeString()
           })
         }
       }
     },
     
     // 设备自检
     handleDevice() {
      this.onOffLine = this.monitorData.OnOffLine.value
      eapRecipe
        .equipStatusReport({ station_code: this.$route.query.station_code })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          this.deviceStatus = defaultQuery.code
          this.handleStopDeviceSwitch()
          this.getDeviceSelfCheck()
          if (defaultQuery.code === 0) {
            this.$message({
              type: 'success',
              message: this.$t('lang_pack.wx.SuccessfullyMes')
            })
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    },
    // 复位流程
    handleResetProcess() {
      this.$confirm(this.$t('lang_pack.wx.comfirmReset'), this.$t('lang_pack.Prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.commonPage.cancel'),
        type: 'warning'
      })
        .then(() => {
          var sendJson = {}
          var rowJson = []
          // 复位上传配方点位
          var newRow1 = {
            TagKey: `${this.stationAttr}Ais` + '/AisStatus/ApplyUploadRecipe',
            TagValue: '0'
          }
          rowJson.push(newRow1)
          // 复位下载配方点位
          var newRow2 = {
            TagKey: `${this.stationAttr}Ais` + '/AisStatus/ApplyDownLoadRecipe',
            TagValue: '0'
          }
          rowJson.push(newRow2)
          // 复位下发配方点位
          var newRow3 = {
            TagKey:
              `${this.stationAttr}Ais` + '/AisStatus/ApplyDistributeRecipe',
            TagValue: '0'
          }
          rowJson.push(newRow3)
          // 复位批次校验
          var newRow4 = {
            TagKey:
              `${this.stationAttr}Ais` + '/AisStatus/ApplyEapUploadBatchCheck',
            TagValue: '0'
          }
          rowJson.push(newRow4)

          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/' + `${this.stationAttr}Ais`
          this.sendMessage(topic, sendStr)
          this.$message({
            type: 'success',
            message: this.$t('lang_pack.wx.resetSuccess')
          })
        })
        .catch(() => {
          // 取消操作
        })
    },
    // 处理在线离线模式切换
    handleOnOffLineSwitch() {
      var tag_value = this.monitorData.OnOffLine.value === '1' ? '0' : '1'
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: `${this.stationAttr}Ais` + '/AisStatus/OnOffLine',
        TagValue: tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + `${this.stationAttr}Ais`
      this.sendMessage(topic, sendStr)
      this.onOffLine = tag_value
      this.handleStopDeviceSwitch()
    },
    // 给plc设备停止状态，(0为离线 1在线并认证状态成功 2在线且认证失败)
    handleStopDeviceSwitch() {
      var stopflag = '0'
      if (this.onOffLine === '0') {
        stopflag = '0'
      } else if (this.onOffLine === '1') {
        if (this.deviceStatus === 0) {
          stopflag = '1'
        } else {
          stopflag = '2'
        }
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: 'MainPlc/PlcStatus/StopDeviceTag',
        TagValue: stopflag
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/MainPlc'
      this.sendMessage(topic, sendStr)
    },
    // 批次校验
    batchCheck() {
      var sendJson = {}
      var rowJson = []
      var newRow1 = {
        TagKey: `${this.stationAttr}Ais` + '/AisStatus/WebUserId',
        TagValue: this.user.username
      }
      rowJson.push(newRow1)
      var newRow2 = {
        TagKey: `${this.stationAttr}Ais` + '/AisStatus/DownLoadRecipeName',
        TagValue: this.lot_no
      }
      rowJson.push(newRow2)
      var newRow = {
        TagKey:
          `${this.stationAttr}Ais` + '/AisStatus/ApplyEapUploadBatchCheck',
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + `${this.stationAttr}Ais`
      this.sendMessage(topic, sendStr)
    },

    getAlarmData() {
      var method = '/cell/core/scada/CoreScadaAlarmSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      var queryData = {
        tablePage: 1,
        tableSize: 10,
        item_date: [this.feedbackStartDate, '2222-12-31 23:59:59'] // 使用item_date数组格式，结束时间固定为2222年
      }
      axios
        .post(path, queryData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: this.$t('lang_pack.wx.queryFailed') + ex, type: 'error' })
        })
    },
    getTagValue() {
      var readTagArray = []
      this.monitorData && Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.$route.query.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      this.groupData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        })
      })
      this.plcCraftData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        })
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              this.monitorData && Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code =
                    client_code + '_' + this.$route.query.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter((item) => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
              result.forEach((e) => {
                this.plcCraftData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.tag_value = e.tag_value
                    if (
                      typeof +e.tag_value === 'number' &&
                      +e.tag_value >= item.down_limit &&
                      +e.tag_value <= item.upper_limit
                    ) {
                      item.status = 'OK'
                    } else {
                      item.status = 'NG'
                    }
                  }
                })
                this.groupData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.value = e.tag_value
                  }
                })
              })
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: this.$t('lang_pack.wx.queryFailed') + ex, type: 'error' })
        })
    },
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'

      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        var aisClientCode = `${this.stationAttr}Ais`
        var plcClientCode = `${this.stationAttr}Plc`
        this.topicSubscribe('SCADA_STATUS/' + aisClientCode)
        this.topicSubscribe('SCADA_BEAT/' + aisClientCode)
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        this.groupData.forEach((item) => {
          this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
        })
        this.plcCraftData.forEach((item) => {
          this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
        })
        this.monitorData && Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.$route.query.station_code
          }
          this.topicSubscribe(
            'SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code
          )
        })
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
        // 记录当前登录账户
        this.handleWrite(
          `${this.stationAttr}Ais/AisStatus/CurrentUser`,
          this.user.username
        )
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {})
      this.clientMqtt.on('close', () => {})
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          if (topic.indexOf('SCADA_BEAT/') >= 0) {
            var heartBeatValue = jsonData.Beat
            if (topic.indexOf(`SCADA_BEAT/${this.stationAttr}Plc`) >= 0) {
              if (this.controlStatus.plc_status !== '2') {
                this.controlStatus.plc_status = heartBeatValue
              }
            }
          } else if (topic.indexOf('SCADA_STATUS/') >= 0) {
            var statusValue = jsonData.Status
            if (topic.indexOf(`SCADA_BEAT/${this.stationAttr}Plc`) >= 0) {
              if (statusValue === '0') {
                this.controlStatus.plc_status = '2'
                this.$message({ message: this.$t('lang_pack.hmiMain.PLCCommunication'), type: 'error' })
              } else {
                if (this.controlStatus.plc_status === '2') {
                  this.controlStatus.plc_status = '1'
                }
              }
            }
          } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            this.plcCraftData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.tag_value = jsonData.TagNewValue
                if (
                  typeof +jsonData.TagNewValue === 'number' &&
                  +jsonData.TagNewValue >= item.down_limit &&
                  +jsonData.TagNewValue <= item.upper_limit
                ) {
                  item.status = 'OK'
                } else {
                  item.status = 'NG'
                }
              }
            })
            this.monitorData && Object.keys(this.monitorData).forEach((key) => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              if (this.aisMonitorMode === 'AIS-SERVER') {
                client_code =
                  client_code + '_' + this.$route.query.station_code
              }
              var tag_key = client_code + '/' + group_code + '/' + tag_code
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue
              }
            })
            this.groupData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.value = jsonData.TagNewValue
              }
            })
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          if (this.showMsg) {
            this.$message({
              message: this.$t('view.dialog.operationSucceed'),
              type: 'success'
            })
          }
          // 执行完成后都复位为true
          this.showMsg = true
        } else {
          // 执行完成后都复位为true
          this.showMsg = true
          this.$message({
            message: this.$t('view.dialog.operationFailed'),
            type: 'error'
          })
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.app-container {
  padding: 10px;
  ::v-deep .el-header {
    padding: 0;
  }
  .header {
    ::v-deep .el-card__body {
      padding: 10px 15px 0 !important;
    }
  }
  .wrapTextSelect {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrapElForm {
    display: flex;
    ::v-deep .barcode {
      display: flex;
      .el-form-item--small {
        width: 90%;
        margin-right: 10px;
      }
    }
  }
  .pieChart {
    width: 100%;
    display: flex;
    div {
      width: 33%;
    }
    #capacityDom {
      height: 300px;
    }
    #capacityDom {
      height: 300px;
    }
    #readbitRateDom {
      height: 300px;
    }
  }
  .active {
    ::v-deep .el-input__inner {
      background-color: #ffff00;
    }
  }
  .dialog-footer {
    text-align: center;
  }
  .statuHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrappstyle {
    display: flex;
    align-items: center;
    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 12px;
        font-weight: 700;
      }
      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }

    p:last-child {
      margin-right: 0 !important;
    }

    .el-divider--vertical {
      width: 2px;
      height: 2em;
    }
  }
  .btnone {
    background: #50d475;
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
  }

  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
    font-size: 18px;
  }

  .btnone1 {
    background: #e6530a;
    border-color: #e6530a;
    color: #ffffff;
    font-size: 18px;
  }

  .btnone:active {
    background: #13887c;
  }
  .wholeline {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }
  .wholeline1 {
    width: 20px;
    height: 20px;
  }
  .wholelinenormal1,
  .deviceGreen {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .deviceRed {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelineerror1,
  .deviceYellow {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #eeff00;
    box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelinegray1 {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .dialogTable {
    ::v-deep .el-dialog {
      margin-top: 5vh !important;
    }
  }
  ::v-deep .el-table th.el-table__cell>.cell {
    font-size: 14px;
    white-space: nowrap;
  }
  ::v-deep .el-table td.el-table__cell div{
    font-size: 18px;
  }
  .feedback-info{
    display: flex;
    margin-bottom: 10px;
    font-size: 20px;
    color: #000;
    overflow: break-word;
    white-space: normal;
    position:relative;
    div{
      min-width: 100px;
      display: flex;
      justify-content: left;
    }
  }
  .feedbackMsg{
    width: 100%;
    height: 270px;
    overflow: auto;
  }
  .feedbackMsg::-webkit-scrollbar {
    width: 10px;
    height: 10px;
    background-color: #ebeef5;
    cursor: pointer !important;
  }
  .feedbackMsg::-webkit-scrollbar-thumb {
    box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
    -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
    background-color: #f6f9ff;
    cursor: pointer !important;
  }
}
</style>
<style lang="scss" scoped>
@import "~@/assets/styles/dy/dialog_hmi.scss";
</style>

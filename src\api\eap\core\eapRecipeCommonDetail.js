import request from '@/utils/request'

// 查询配方维护详情信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeDetailSel',
    method: 'post',
    data
  })
}
// 新增配方维护详情信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeDetailIns',
    method: 'post',
    data
  })
}
// 修改配方维护详情信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeDetailUpd',
    method: 'post',
    data
  })
}
// 删除配方维护详情信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeDetailDel',
    method: 'post',
    data
  })
}

export default { sel, add, edit, del }


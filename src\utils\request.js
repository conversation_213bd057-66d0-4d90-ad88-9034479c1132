import axios from 'axios'
import router from '@/router/routers'
import { Notification } from 'element-ui'
import store from '../store'
// import { getToken } from '@/utils/auth'
import Config from '@/settings'
import Cookies from 'js-cookie'

// 拦截器
// https://blog.csdn.net/weixin_30768661/article/details/97082160 vue/cli 3.0配置NODE_ENV

// Notification通知组件：

// Promise 对象(是处理异步的利器，按顺序执行) 为了避免界面冻结（任务）
// 1、主要用于异步计算
// 2、可以将异步操作队列化，按照期望的顺序执行，返回符合预期的结果
// 3、可以在对象之间传递和操作promise，帮助我们处理队列

// FileReader：实现上传图片预览功能

// 创建axios实例
const service = axios.create({
  baseURL: process.env.NODE_ENV === 'production' ? process.env.VUE_APP_BASE_API : '/', // api 的 base_url
  // baseURL: process.env.VUE_APP_BASE_API || '/', // api 的 base_url
  timeout: Config.timeout // 请求超时时间
})

// request拦截器
service.interceptors.request.use(
  config => {
    // if (getToken()) {
    //  config.headers['Authorization'] = getToken() // 让每个请求携带自定义token 请根据实际情况自行修改
    // }
    config.headers['Content-Type'] = 'application/json'
    // 这个位置 孙恒说 只有中文或者英文的时候接口里面传入'zh-CN', 'en-US'  其他的都传入ohter  他们的数据库都不一样
    const language = ['zh-CN', 'en-US', 'zh-TW', 'th']
    if (language.includes(localStorage.getItem('language'))) {
      config.headers['Ais-Languages'] = localStorage.getItem('language') || 'zh-CN'
    } else {
      config.headers['Ais-Languages'] = 'zh-CN'
    }
    // config.headers['Access-Control-Allow-Origin'] = '*'
    return config
  },
  error => {
    Promise.reject(error)
  }
)

// response 拦截器
service.interceptors.response.use(
  response => {
    if (response.data &&
      response.data.code &&
      response.data.code < 0
    ) {
      // Notification.warning({
      //   title: response.data.msg || response.data.error || '接口请求失败',
      //   duration: 5000
      // })
    }
    return response.data
  },
  error => {
    // 兼容blob下载出错json提示
    if (error.response.data instanceof Blob && error.response.data.type.toLowerCase().indexOf('json') !== -1) {
      const reader = new FileReader()
      reader.readAsText(error.response.data, 'utf-8')
      reader.onload = function(e) {
        const errorMsg = JSON.parse(reader.result).message
        // Notification.error({
        //   title: errorMsg,
        //   duration: 5000
        // })
        console.log(errorMsg)
      }
    } else {
      let status = 0
      try {
        status = error.response.status
        const data = error.response.data.data
        if (data && data === 'sys.passwordHasExpired') {
          Cookies.set(data, true)
          location.reload()
        }
      } catch (e) {
        if (error.toString().indexOf('Error: timeout') !== -1) {
          // Notification.error({
          //   title: '网络请求超时',
          //   duration: 5000
          // })
          console.log('网络请求超时')
          return Promise.reject(error)
        }
      }
      if (status) {
        if (status === 401) {
          store.dispatch('LogOut').then(() => {
            // 用户登录界面提示
            Cookies.set('point', 401)
            location.reload()
          })
        } else if (status === 403) {
          router.push({ path: '/401' }) // 跳转到URL
        } else if (error.response.data) {
          const errorMsg = error.response.data.message || error.response.data.msg || error.response.data.error || 'Interface request failed'
          Notification.error({
            title: errorMsg,
            duration: 5000
          })
        }
      } else {
        // Notification.error({
        //   title: '接口请求失败',
        //   duration: 5000
        // })
        console.log('Interface request failed')
      }
    }
    return Promise.reject(error)
  }
)
export default service

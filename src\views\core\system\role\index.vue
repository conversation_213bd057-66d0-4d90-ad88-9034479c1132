<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceRole.roleNumberDescription')">
                <!-- 角色编号/描述： -->
                <el-input v-model="query.roleCodeDes" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <!-- 有效标识： -->
                <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="query.enable_flag" control_type="select" size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <!--新增/修改抽屉-->
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.maintenanceRole.roleNumber')" prop="role_code" display:none>
            <!-- 角色编号 -->
            <el-input v-model="form.role_code" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.maintenanceRole.roleDescription')" prop="role_des">
            <!-- 角色描述 -->
            <el-input v-model="form.role_des" />
          </el-form-item>
          <!--快速编码：ENABLE_FLAG-->
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
            <!-- 有效标识 -->
            <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" />
          </el-form-item>
        </el-form>

        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <!-- 角色菜单权限维护 抽屉 -->
      <el-drawer append-to-body title="菜单权限" :wrapper-closable="false" :visible.sync="centerMenuDrawerVisible" size="650px" @closed="centerMenuDrawerClose" @opened="centerMenuDrawerOpened">
        <div id="scrollBar" style="text-align: center; max-height: 600px; overflow-y: auto; border: 1px solid #e4e7ed; padding: 10px; margin: 10px 0;">
            <el-tree ref="tree" :data="treeData" :props="treeProps" show-checkbox node-key="id" />
        </div>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="centerMenuFromCancel">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="centerMenuFormSubmit">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>
      <el-drawer append-to-body title="工位权限" :wrapper-closable="false" :visible.sync="drawerStation" size="750px">
        <el-form :inline="true" size="small">
          <el-form-item label="产线">
            <el-select v-model="prodLineId" style="width:200px" clearable>
              <el-option v-for="item in prodLineData" :key="item.prod_line_code" :label="item.prod_line_des" :value="item.prod_line_id" />
            </el-select>
          </el-form-item>
          <el-form-item label="工位编码/描述">
            <el-input v-model="stationCodeDes" clearable size="small" style="width:200px" />
            &nbsp;
            <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="handleSelRoleStation">搜索</el-button>
            <el-button class="filter-item" size="small" :icon="allSelectStation ? 'el-icon-close' : 'el-icon-check'" @click="allSelectStationChange">{{ allSelectStation ? '取消全选' : '全选' }}</el-button>
          </el-form-item>
        </el-form>
        <!-- 工位权限 -->
        <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" size="small" :data="roleStationData" style="width: 100%;margin-top:10px" height="350px" highlight-current-row>
          <el-table-column  :show-overflow-tooltip="true" width="70">
            <template slot-scope="scope">
              <el-checkbox v-model="scope.row.selectstation" />
            </template>
          </el-table-column>
          <el-table-column  v-if="false" :show-overflow-tooltip="true" width="70">
            <template slot="header">
              <el-checkbox v-model="allSelectLine" @change="allSelectLineChange">{{ $t('lang_pack.commonPage.checkAll') }}</el-checkbox>
              <!-- 全选 -->
            </template>
            <template slot-scope="scope">
              <!-- <el-checkbox v-model="scope.row.selectLine" :checked="scope.row.menu_show_type === 'LINE'" /> -->
              <el-radio v-model="scope.row.menu_show_type" label="LINE">{{ $t('lang_pack.maintenanceRole.productionLine') }}</el-radio>
              <!-- 产线 -->
            </template>
          </el-table-column>
          <el-table-column  v-if="false" :show-overflow-tooltip="true" width="70">
            <template slot="header">
              <el-checkbox v-model="allSelectProcedure" @change="allSelectProcedureChange">{{ $t('lang_pack.commonPage.checkAll') }}</el-checkbox>
              <!-- 全选 -->
            </template>
            <template slot-scope="scope">
              <!-- <el-checkbox v-model="scope.row.selectProcedure" :checked="scope.row.menu_show_type === 'PROCEDURE'" /> -->
              <el-radio v-model="scope.row.menu_show_type" label="PROCEDURE">{{ $t('lang_pack.maintenanceRole.process') }}</el-radio>
              <!-- 工序 -->
            </template>
          </el-table-column>
          <el-table-column  :show-overflow-tooltip="true" prop="prod_line_des" :label="$t('lang_pack.maintenanceRole.productionLine')" />
          <!-- 产线 -->
          <el-table-column  :show-overflow-tooltip="true" prop="station_code" :label="$t('lang_pack.maintenanceRole.locationCoding')" />
          <!-- 工位编码 -->
          <el-table-column  :show-overflow-tooltip="true" prop="station_des" :label="$t('lang_pack.maintenanceRole.locationDescription')" />
          <!-- 工位描述 -->
        </el-table>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="drawerStation = false">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
          <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" @click="handleSaveRoleStation">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          <!-- 确认 -->
        </div>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column  v-if="1 == 0" width="10" prop="role_id" label="id" />
            <el-table-column  :show-overflow-tooltip="true" prop="role_code" :label="$t('lang_pack.maintenanceRole.roleEncoding')" />
            <!-- 角色编码 -->
            <el-table-column  :show-overflow-tooltip="true" prop="role_des" :label="$t('lang_pack.maintenanceRole.roleDescription')" />
            <!-- 角色描述 -->
            <el-table-column  :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column  :label="$t('lang_pack.maintenanceRole.permission')" width="115" align="center" fixed="right">
              <!-- 权限 -->
              <template slot-scope="scope">
                <el-button size="small" type="text" @click="handleRoleCenter(scope.row)">{{ $t('lang_pack.maintenanceRole.menu') }}</el-button>
                <!-- 菜单 -->
                <el-button size="small" type="text" style="margin-left:0px;" @click="openRoleStation(scope.row)">{{ $t('lang_pack.maintenanceRole.station') }}</el-button>
                <!-- 工位 -->
              </template>
            </el-table-column>
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudRole from '@/api/core/system/sysRole'
import { lovProdLine } from '@/api/core/factory/sysProdLine'
import { queryRoleMenuTree, queryRoleMenu, insRoleMenu } from '@/api/core/system/sysRoleMenu'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  role_id: '',
  role_code: '',
  role_des: '',
  enable_flag: 'Y'
}
export default {
  name: 'SYS_ROLE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '角色维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'role_id',
      // 排序
      sort: ['role_id asc'],
      // CRUD Method
      crudMethod: { ...crudRole },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: true,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
        add: ['admin', 'sys_role:add'],
        edit: ['admin', 'sys_role:edit'],
        del: ['admin', 'sys_role:del'],
        down: ['admin', 'sys_role:down']
      },
      rules: {
        // 提交验证规则
        role_code: [{ required: true, message: '请输入角色编码', trigger: 'blur' }]
      },
      // 权限菜单树
      treeData: [],
      treeProps: {
        children: 'children',
        label: 'lable'
      },
      drawerStation: false,
      prodLineId: '',
      stationCodeDes: '',
      roleStationData: [],
      allSelectStation: false,
      allSelectLine: false,
      allSelectProcedure: false,
      centerMenuDrawerVisible: false,
      currentRowData: [], // 选中行
      // 产线数据
      prodLineData: []
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      dom.style.height = (window.innerHeight - 150) + 'px'
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    // 初始化角色菜单(有/无权限)
    queryRoleMenuTree({})
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.count > 0) {
            var result = defaultQuery.data
            var _parent = result.filter(item => item.parent === 0)
            var treeResult = [] // 树结果

            // 解析第一级
            for (var i = 0; i < _parent.length; i++) {
              var firstNode = {}
              firstNode.id = '1_' + _parent[i].id // 节点
              firstNode.lable = _parent[i].lable // 显示
              firstNode.MenuId = _parent[i].id
              firstNode.level = 1
              firstNode.children = [] // 子节点
              var _sub = result.filter(item => item.parent === _parent[i].id)
              // 解析第二级
              for (var k = 0; k < _sub.length; k++) {
                var twoNode = {}
                twoNode.id = '2_' + _sub[k].id // 子节点
                twoNode.lable = _sub[k].lable // 显示
                twoNode.MenuId = _sub[k].id
                twoNode.level = 2
                twoNode.children = [] // 子节点
                firstNode.children.push(twoNode)
              }
              treeResult.push(firstNode)
            }

            // 树赋值
            this.treeData = treeResult
          }
        }
      })
      .catch(() => {})

    // 加载 产线LOV
    const query = {
      userID: Cookies.get('userName')
    }
    lovProdLine(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    openRoleStation(data) {
      this.currentRowData = data
      this.allSelectStation = false
      this.allSelectLine = false
      this.allSelectProcedure = false
      this.handleSelRoleStation()
      this.drawerStation = true
    },
    allSelectStationChange() {
      this.allSelectStation = !this.allSelectStation
      if (this.allSelectStation) {
        this.roleStationData.forEach(item => {
          item.selectstation = true
        })
      } else {
        this.roleStationData.forEach(item => {
          item.selectstation = false
        })
      }
    },
    allSelectLineChange(val) {
      if (val) {
        this.allSelectProcedure = false
        this.roleStationData.forEach(item => {
          item.menu_show_type = 'LINE'
        })
      } else {
        if (!this.allSelectProcedure) {
          this.roleStationData.forEach(item => {
            item.menu_show_type = ''
          })
        }
      }
    },
    allSelectProcedureChange(val) {
      if (val) {
        this.allSelectLine = false
        this.roleStationData.forEach(item => {
          item.menu_show_type = 'PROCEDURE'
        })
      } else {
        if (!this.allSelectLine) {
          this.roleStationData.forEach(item => {
            item.menu_show_type = ''
          })
        }
      }
    },
    // 角色菜单权限
    handleRoleCenter(data) {
      this.currentRowData = data
      this.centerMenuDrawerVisible = true
    },
    centerMenuDrawerClose() {
      this.$refs.tree.setCheckedKeys([])
    },
    centerMenuDrawerOpened() {
      this.initTree(this.currentRowData.role_id)
    },
    // 初始化Tree
    initTree(role_id) {
      // 格式化查询条件
      const query = {
        role_id: role_id
      }
      // 查询选中的树的值
      queryRoleMenu(query)
        .then(resSel => {
          const defaultQuerySel = JSON.parse(JSON.stringify(resSel))
          if (defaultQuerySel.code === 0) {
            if (defaultQuerySel.data.length > 0) {
              var ids = []
              defaultQuerySel.data.forEach(item => {
                if (item.role_menu_id !== '' && item.role_menu_id !== undefined && item.role_menu_id !== null) {
                  ids.push('2_' + item.menu_item_id)
                }
              })
              this.$refs.tree.setCheckedKeys(ids)
            } else {
              this.$refs.tree.setCheckedKeys([])
            }
          }
        })
        .catch(() => {})
    },
    // 取消 抽屉
    centerMenuFromCancel() {
      this.centerMenuDrawerVisible = false
    },
    // 保存菜单(树)
    centerMenuFormSubmit() {
      const data = this.$refs.tree.getCheckedNodes()
      if (data.length === 0) {
        this.$message({ message: '请选择菜单', type: 'info' })
        return
      }
      var ids = []
      this.$refs.tree.getCheckedNodes().forEach(item => {
        if (item.level === 2) {
          ids.push(item.MenuId)
        }
      })
      insRoleMenu({
        role_id: this.currentRowData.role_id,
        menu_id_list: ids.join(',')
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.centerMenuDrawerVisible = false
            this.$message({ message: '保存成功', type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'info' })
          }
        })
        .catch(() => {
          this.$message({ message: '保存异常', type: 'error' })
        })
    },
    handleSelRoleStation() {
      crudRole
        .selRoleStation({
          user_name: Cookies.get('userName'),
          role_id: this.currentRowData.role_id,
          prod_line_id: this.prodLineId,
          station_code_des: this.stationCodeDes
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.roleStationData = defaultQuery.data
          } else {
            this.roleStationData = []
            this.$message({ message: defaultQuery.msg, type: 'info' })
          }
        })
        .catch(() => {
          this.roleStationData = []
          this.$message({ message: '保存异常', type: 'error' })
        })
    },
    handleSaveRoleStation() {
      crudRole
        .addRoleStation({
          role_id: this.currentRowData.role_id,
          Station_list: this.roleStationData
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.centerMenuDrawerVisible = false
            this.$message({ message: '保存成功', type: 'success' })
            this.handleSelRoleStation()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'info' })
          }
        })
        .catch(() => {
          this.$message({ message: '保存异常', type: 'error' })
        })
    }
  }
}
</script>
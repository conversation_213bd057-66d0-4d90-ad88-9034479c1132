import request from '@/utils/request'

/**
 * 获取设备OEE统计数据
 * @param {Object} data 请求参数
 * @param {string} data.device_code 设备编码
 * @param {string} data.date 日期 (yyyy-MM-dd)
 * @param {string} data.user_name 用户名
 */
export function getDeviceOeeStatistics(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/getDeviceOeeStatistics',
    method: 'post',
    data
  })
}

/**
 * 获取设备当前状态
 * @param {Object} data 请求参数
 * @param {string} data.device_code 设备编码
 * @param {string} data.user_name 用户名
 */
export function getDeviceCurrentStatus(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/getDeviceCurrentStatus',
    method: 'post',
    data
  })
}

/**
 * 获取监控设备列表
 * @param {Object} data 请求参数
 * @param {string} data.user_name 用户名
 */
export function getMonitoredDevices(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/getMonitoredDevices',
    method: 'post',
    data
  })
}

/**
 * 获取设备状态映射配置
 * @param {Object} data 请求参数
 * @param {string} data.device_code 设备编码(可选)
 * @param {string} data.user_name 用户名
 */
export function getDeviceStatusMappings(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/getDeviceStatusMappings',
    method: 'post',
    data
  })
}

/**
 * 保存设备状态映射配置
 * @param {Object} data 请求参数
 * @param {number} data.mapping_id 映射ID(可选，新增时不传)
 * @param {string} data.device_code 设备编码
 * @param {string} data.device_type 设备类型
 * @param {string} data.plc_status_value PLC状态值
 * @param {string} data.standard_status_name 标准状态名称
 * @param {string} data.status_color 状态颜色
 * @param {string} data.status_description 状态描述
 * @param {number} data.sort_order 排序
 * @param {string} data.user_name 用户名
 */
export function saveDeviceStatusMapping(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/saveDeviceStatusMapping',
    method: 'post',
    data
  })
}

/**
 * 删除设备状态映射配置
 * @param {Object} data 请求参数
 * @param {number} data.mapping_id 映射ID
 * @param {string} data.user_name 用户名
 */
export function deleteDeviceStatusMapping(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/deleteDeviceStatusMapping',
    method: 'post',
    data
  })
}

/**
 * 手动触发数据采集
 * @param {Object} data 请求参数
 * @param {string} data.device_code 设备编码
 * @param {string} data.user_name 用户名
 */
export function manualCollectData(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/manualCollectData',
    method: 'post',
    data
  })
}

/**
 * 手动触发数据汇总
 * @param {Object} data 请求参数
 * @param {string} data.device_code 设备编码
 * @param {string} data.date 日期 (yyyy-MM-dd)
 * @param {string} data.user_name 用户名
 */
export function manualSummarizeData(data) {
  return request({
    url: '/aisEsbWeb/eap/oee/manualSummarizeData',
    method: 'post',
    data
  })
}

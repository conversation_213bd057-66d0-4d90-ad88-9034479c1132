<template>
  <div class="oee-statistics-container">
    <!-- 头部控制区域 -->
    <el-card class="header-card" shadow="never">
      <div class="header-controls">
        <div class="left-controls">
          <el-select
            v-model="selectedDevice"
            placeholder="请选择设备"
            @change="onDeviceChange"
            style="width: 200px; margin-right: 10px;"
          >
            <el-option
              v-for="device in deviceList"
              :key="device"
              :label="device"
              :value="device"
            />
          </el-select>

          <el-date-picker
            v-model="selectedDate"
            type="date"
            placeholder="选择日期"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            @change="onDateChange"
            style="width: 150px; margin-right: 10px;"
          />

          <el-button type="primary" @click="loadStatistics" :loading="loading">
            <i class="el-icon-search"></i> 查询
          </el-button>

          <el-button @click="refreshData" :loading="refreshing">
            <i class="el-icon-refresh"></i> 刷新
          </el-button>
        </div>

        <div class="right-controls">
          <el-button type="success" size="small" @click="manualCollect">
            <i class="el-icon-download"></i> 手动采集
          </el-button>

          <el-button type="warning" size="small" @click="manualSummarize">
            <i class="el-icon-s-data"></i> 手动汇总
          </el-button>

          <el-button type="info" size="small" @click="showConfigDialog">
            <i class="el-icon-setting"></i> 状态配置
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 当前状态显示 -->
    <el-card class="status-card" shadow="never" v-if="currentStatus">
      <div class="current-status">
        <span class="status-label">当前状态：</span>
        <el-tag
          :color="getStatusColor(currentStatus)"
          effect="dark"
          size="medium"
        >
          {{ currentStatus }}
        </el-tag>
        <span class="update-time">更新时间：{{ formatTime(new Date()) }}</span>
      </div>
    </el-card>

    <!-- 统计数据展示 -->
    <div class="statistics-content" v-if="statisticsData">
      <!-- 状态统计饼图 -->
      <el-card class="chart-card" shadow="never">
        <div slot="header" class="card-header">
          <span>状态分布统计</span>
          <span class="date-info">{{ selectedDate }}</span>
        </div>
        <div class="pie-chart-container">
          <div id="pieChart" style="width: 100%; height: 300px;"></div>
          <div class="statistics-table">
            <el-table :data="statusTableData" size="small" style="width: 100%">
              <el-table-column prop="statusName" label="状态" width="80">
                <template slot-scope="scope">
                  <el-tag :color="scope.row.statusColor" effect="dark" size="mini">
                    {{ scope.row.statusName }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="totalMinutes" label="时长(分钟)" width="100" />
              <el-table-column prop="percentage" label="占比" width="80">
                <template slot-scope="scope">
                  {{ scope.row.percentage.toFixed(1) }}%
                </template>
              </el-table-column>
              <el-table-column prop="occurrenceCount" label="次数" width="60" />
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 时间轴状态图 -->
      <el-card class="chart-card" shadow="never">
        <div slot="header" class="card-header">
          <span>全天状态时间轴 (0-23点)</span>
        </div>
        <div id="timelineChart" style="width: 100%; height: 200px;"></div>
      </el-card>
    </div>

    <!-- 无数据提示 -->
    <el-card v-else-if="!loading" class="no-data-card" shadow="never">
      <div class="no-data">
        <i class="el-icon-warning-outline"></i>
        <p>暂无统计数据</p>
        <p>请选择设备和日期后点击查询</p>
      </div>
    </el-card>

    <!-- 状态配置对话框 -->
    <el-dialog
      title="设备状态映射配置"
      :visible.sync="configDialogVisible"
      width="800px"
      :before-close="handleConfigClose"
    >
      <div class="config-content">
        <div class="config-toolbar">
          <el-button type="primary" size="small" @click="addMapping">
            <i class="el-icon-plus"></i> 新增映射
          </el-button>
        </div>

        <el-table :data="mappingList" size="small" style="width: 100%">
          <el-table-column prop="deviceCode" label="设备编码" width="120" />
          <el-table-column prop="deviceType" label="设备类型" width="100" />
          <el-table-column prop="plcStatusValue" label="PLC状态值" width="100" />
          <el-table-column prop="standardStatusName" label="标准状态" width="100" />
          <el-table-column prop="statusColor" label="颜色" width="80">
            <template slot-scope="scope">
              <div
                class="color-block"
                :style="{ backgroundColor: scope.row.statusColor }"
              ></div>
            </template>
          </el-table-column>
          <el-table-column prop="statusDescription" label="描述" />
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="editMapping(scope.row)">
                编辑
              </el-button>
              <el-button type="text" size="small" @click="deleteMapping(scope.row)">
                删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 映射编辑对话框 -->
    <el-dialog
      :title="editingMapping.mappingId ? '编辑映射' : '新增映射'"
      :visible.sync="editDialogVisible"
      width="500px"
    >
      <el-form :model="editingMapping" :rules="mappingRules" ref="mappingForm" label-width="120px">
        <el-form-item label="设备编码" prop="deviceCode">
          <el-input v-model="editingMapping.deviceCode" placeholder="请输入设备编码" />
        </el-form-item>
        <el-form-item label="设备类型" prop="deviceType">
          <el-input v-model="editingMapping.deviceType" placeholder="请输入设备类型" />
        </el-form-item>
        <el-form-item label="PLC状态值" prop="plcStatusValue">
          <el-input v-model="editingMapping.plcStatusValue" placeholder="请输入PLC状态值" />
        </el-form-item>
        <el-form-item label="标准状态" prop="standardStatusName">
          <el-input v-model="editingMapping.standardStatusName" placeholder="请输入标准状态名称" />
        </el-form-item>
        <el-form-item label="状态颜色" prop="statusColor">
          <el-color-picker v-model="editingMapping.statusColor" />
        </el-form-item>
        <el-form-item label="状态描述">
          <el-input v-model="editingMapping.statusDescription" placeholder="请输入状态描述" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="editingMapping.sortOrder" :min="0" />
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="saveMapping" :loading="saving">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getDeviceOeeStatistics,
  getDeviceCurrentStatus,
  getMonitoredDevices,
  getDeviceStatusMappings,
  saveDeviceStatusMapping,
  deleteDeviceStatusMapping,
  manualCollectData,
  manualSummarizeData
} from '@/api/eap/oee/oeeStatistics'
import Cookies from 'js-cookie'
import * as echarts from 'echarts'

export default {
  name: 'OeeStatistics',
  data() {
    return {
      loading: false,
      refreshing: false,
      saving: false,
      selectedDevice: '',
      selectedDate: this.formatDate(new Date()),
      deviceList: [],
      currentStatus: '',
      statisticsData: null,
      statusTableData: [],
      configDialogVisible: false,
      editDialogVisible: false,
      mappingList: [],
      editingMapping: {
        mappingId: null,
        deviceCode: '',
        deviceType: '',
        plcStatusValue: '',
        standardStatusName: '',
        statusColor: '#409EFF',
        statusDescription: '',
        sortOrder: 0
      },
      mappingRules: {
        deviceCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
        deviceType: [{ required: true, message: '请输入设备类型', trigger: 'blur' }],
        plcStatusValue: [{ required: true, message: '请输入PLC状态值', trigger: 'blur' }],
        standardStatusName: [{ required: true, message: '请输入标准状态名称', trigger: 'blur' }],
        statusColor: [{ required: true, message: '请选择状态颜色', trigger: 'change' }]
      },
      pieChart: null,
      timelineChart: null,
      statusTimer: null
    }
  },
  mounted() {
    this.loadDeviceList()
    this.startStatusPolling()
  },
  beforeDestroy() {
    this.stopStatusPolling()
    if (this.pieChart) {
      this.pieChart.dispose()
    }
    if (this.timelineChart) {
      this.timelineChart.dispose()
    }
  },
  methods: {
    // 加载设备列表
    async loadDeviceList() {
      try {
        const response = await getMonitoredDevices({
          user_name: Cookies.get('userName')
        })
        if (response.code === 0) {
          this.deviceList = response.data.deviceCodes
          if (this.deviceList.length > 0 && !this.selectedDevice) {
            this.selectedDevice = this.deviceList[0]
            this.loadCurrentStatus()
          }
        }
      } catch (error) {
        this.$message.error('加载设备列表失败')
      }
    },

    // 加载统计数据
    async loadStatistics() {
      if (!this.selectedDevice) {
        this.$message.warning('请先选择设备')
        return
      }

      this.loading = true
      try {
        const response = await getDeviceOeeStatistics({
          device_code: this.selectedDevice,
          date: this.selectedDate,
          user_name: Cookies.get('userName')
        })

        if (response.code === 0) {
          this.statisticsData = response.data
          this.processStatisticsData()
          this.$nextTick(() => {
            this.renderCharts()
          })
        } else {
          this.$message.error(response.msg || '获取统计数据失败')
          this.statisticsData = null
        }
      } catch (error) {
        this.$message.error('获取统计数据失败')
        this.statisticsData = null
      } finally {
        this.loading = false
      }
    },

    // 加载当前状态
    async loadCurrentStatus() {
      if (!this.selectedDevice) return

      try {
        const response = await getDeviceCurrentStatus({
          device_code: this.selectedDevice,
          user_name: Cookies.get('userName')
        })

        if (response.code === 0) {
          this.currentStatus = response.data.currentStatus || '未知'
        }
      } catch (error) {
        console.error('获取当前状态失败:', error)
      }
    },

    // 处理统计数据
    processStatisticsData() {
      if (!this.statisticsData || !this.statisticsData.statusStatistics) {
        this.statusTableData = []
        return
      }

      this.statusTableData = Object.values(this.statisticsData.statusStatistics)
    },

    // 渲染图表
    renderCharts() {
      this.renderPieChart()
      this.renderTimelineChart()
    },

    // 渲染饼图
    renderPieChart() {
      const chartDom = document.getElementById('pieChart')
      if (!chartDom) return

      if (this.pieChart) {
        this.pieChart.dispose()
      }
      this.pieChart = echarts.init(chartDom)

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c}分钟 ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '状态分布',
            type: 'pie',
            radius: '50%',
            data: this.statusTableData.map(item => ({
              value: item.totalMinutes,
              name: item.statusName,
              itemStyle: {
                color: item.statusColor
              }
            })),
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      }

      this.pieChart.setOption(option)
    },

    // 渲染时间轴图
    renderTimelineChart() {
      const chartDom = document.getElementById('timelineChart')
      if (!chartDom) return

      if (this.timelineChart) {
        this.timelineChart.dispose()
      }
      this.timelineChart = echarts.init(chartDom)

      if (!this.statisticsData.compressedTimeline) {
        return
      }

      const data = this.statisticsData.compressedTimeline.map(segment => ({
        name: segment.statusName,
        value: [
          0, // y轴固定为0
          segment.startMinute,
          segment.endMinute,
          segment.duration
        ],
        itemStyle: {
          color: segment.statusColor
        }
      }))

      const option = {
        tooltip: {
          formatter: function(params) {
            const start = Math.floor(params.value[1] / 60) + ':' + (params.value[1] % 60).toString().padStart(2, '0')
            const end = Math.floor(params.value[2] / 60) + ':' + (params.value[2] % 60).toString().padStart(2, '0')
            return `${params.name}<br/>时间: ${start} - ${end}<br/>时长: ${params.value[3]}分钟`
          }
        },
        grid: {
          height: 50
        },
        xAxis: {
          type: 'value',
          min: 0,
          max: 1440,
          axisLabel: {
            formatter: function(value) {
              const hour = Math.floor(value / 60)
              return hour + ':00'
            }
          },
          splitNumber: 24
        },
        yAxis: {
          type: 'value',
          show: false,
          min: -0.5,
          max: 0.5
        },
        series: [
          {
            type: 'custom',
            renderItem: function(params, api) {
              const start = api.value(1)
              const end = api.value(2)
              const height = 30

              const startPoint = api.coord([start, 0])
              const endPoint = api.coord([end, 0])

              return {
                type: 'rect',
                shape: {
                  x: startPoint[0],
                  y: startPoint[1] - height / 2,
                  width: endPoint[0] - startPoint[0],
                  height: height
                },
                style: {
                  fill: api.visual('color')
                }
              }
            },
            data: data
          }
        ]
      }

      this.timelineChart.setOption(option)
    },

    // 获取状态颜色
    getStatusColor(statusName) {
      const colorMap = {
        '运行': '#52c41a',
        '停机': '#ff4d4f',
        '待机': '#faad14',
        '故障': '#f5222d',
        '维护': '#722ed1'
      }
      return colorMap[statusName] || '#d9d9d9'
    },

    // 事件处理
    onDeviceChange() {
      this.loadCurrentStatus()
      if (this.selectedDevice && this.selectedDate) {
        this.loadStatistics()
      }
    },

    onDateChange() {
      if (this.selectedDevice && this.selectedDate) {
        this.loadStatistics()
      }
    },

    refreshData() {
      this.refreshing = true
      Promise.all([
        this.loadDeviceList(),
        this.loadCurrentStatus(),
        this.selectedDevice && this.selectedDate ? this.loadStatistics() : Promise.resolve()
      ]).finally(() => {
        this.refreshing = false
      })
    },

    // 手动操作
    async manualCollect() {
      if (!this.selectedDevice) {
        this.$message.warning('请先选择设备')
        return
      }

      try {
        const response = await manualCollectData({
          device_code: this.selectedDevice,
          user_name: Cookies.get('userName')
        })

        if (response.code === 0) {
          this.$message.success('数据采集完成')
          this.loadCurrentStatus()
        } else {
          this.$message.error(response.msg || '数据采集失败')
        }
      } catch (error) {
        this.$message.error('数据采集失败')
      }
    },

    async manualSummarize() {
      if (!this.selectedDevice) {
        this.$message.warning('请先选择设备')
        return
      }

      try {
        const response = await manualSummarizeData({
          device_code: this.selectedDevice,
          date: this.selectedDate,
          user_name: Cookies.get('userName')
        })

        if (response.code === 0) {
          this.$message.success('数据汇总完成')
          this.loadStatistics()
        } else {
          this.$message.error(response.msg || '数据汇总失败')
        }
      } catch (error) {
        this.$message.error('数据汇总失败')
      }
    },

    // 配置管理
    async showConfigDialog() {
      this.configDialogVisible = true
      await this.loadMappingList()
    },

    async loadMappingList() {
      try {
        const response = await getDeviceStatusMappings({
          user_name: Cookies.get('userName')
        })

        if (response.code === 0) {
          this.mappingList = response.data
        }
      } catch (error) {
        this.$message.error('加载状态映射失败')
      }
    },

    addMapping() {
      this.editingMapping = {
        mappingId: null,
        deviceCode: this.selectedDevice || '',
        deviceType: '',
        plcStatusValue: '',
        standardStatusName: '',
        statusColor: '#409EFF',
        statusDescription: '',
        sortOrder: 0
      }
      this.editDialogVisible = true
    },

    editMapping(mapping) {
      this.editingMapping = { ...mapping }
      this.editDialogVisible = true
    },

    async saveMapping() {
      this.$refs.mappingForm.validate(async (valid) => {
        if (!valid) return

        this.saving = true
        try {
          const response = await saveDeviceStatusMapping({
            ...this.editingMapping,
            user_name: Cookies.get('userName')
          })

          if (response.code === 0) {
            this.$message.success('保存成功')
            this.editDialogVisible = false
            await this.loadMappingList()
            await this.loadDeviceList()
          } else {
            this.$message.error(response.msg || '保存失败')
          }
        } catch (error) {
          this.$message.error('保存失败')
        } finally {
          this.saving = false
        }
      })
    },

    async deleteMapping(mapping) {
      this.$confirm('确定要删除这个状态映射吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          const response = await deleteDeviceStatusMapping({
            mapping_id: mapping.mappingId,
            user_name: Cookies.get('userName')
          })

          if (response.code === 0) {
            this.$message.success('删除成功')
            await this.loadMappingList()
            await this.loadDeviceList()
          } else {
            this.$message.error(response.msg || '删除失败')
          }
        } catch (error) {
          this.$message.error('删除失败')
        }
      })
    },

    handleConfigClose() {
      this.configDialogVisible = false
    },

    // 状态轮询
    startStatusPolling() {
      this.statusTimer = setInterval(() => {
        if (this.selectedDevice) {
          this.loadCurrentStatus()
        }
      }, 30000) // 30秒轮询一次
    },

    stopStatusPolling() {
      if (this.statusTimer) {
        clearInterval(this.statusTimer)
        this.statusTimer = null
      }
    },

    // 工具方法
    formatDate(date) {
      const year = date.getFullYear()
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const day = date.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${day}`
    },

    formatTime(date) {
      return date.toLocaleTimeString()
    }
  }
}
</script>

<style lang="scss" scoped>
.oee-statistics-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: calc(100vh - 84px);
}

.header-card {
  margin-bottom: 20px;

  .header-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .left-controls {
      display: flex;
      align-items: center;
    }

    .right-controls {
      display: flex;
      gap: 10px;
    }
  }
}

.status-card {
  margin-bottom: 20px;

  .current-status {
    display: flex;
    align-items: center;
    gap: 10px;

    .status-label {
      font-weight: bold;
      color: #606266;
    }

    .update-time {
      color: #909399;
      font-size: 12px;
      margin-left: auto;
    }
  }
}

.statistics-content {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

.chart-card {
  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .date-info {
      color: #909399;
      font-size: 14px;
    }
  }

  .pie-chart-container {
    display: flex;
    gap: 20px;

    #pieChart {
      flex: 1;
    }

    .statistics-table {
      flex: 1;
      min-width: 300px;
    }
  }
}

.no-data-card {
  .no-data {
    text-align: center;
    padding: 60px 20px;
    color: #909399;

    i {
      font-size: 48px;
      margin-bottom: 20px;
    }

    p {
      margin: 10px 0;
      font-size: 16px;
    }
  }
}

.config-content {
  .config-toolbar {
    margin-bottom: 20px;
  }

  .color-block {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
  }
}

::v-deep .el-card__body {
  padding: 20px;
}

::v-deep .el-table--small td {
  padding: 8px 0;
}
</style>

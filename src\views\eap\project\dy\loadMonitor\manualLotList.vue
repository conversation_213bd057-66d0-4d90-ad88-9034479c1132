<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.portSelect') }}
        </template>
        <el-radio v-for="(item,index) in portList" :key="index" v-model="webLotPortCode" :label="item.port_index" style="margin-left:0px;" border>{{ item.port_des }}</el-radio>
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.carrierID') }}
        </template>
        <el-input ref="webPalletNum" v-model="webPalletNum" clearable size="mini" @input="handleInputPallNum" />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.lotInfo') }}
        </template>
        <el-table ref="table1" style="width: 100%;margin-top:10px;" :data="lot_list" :header-cell-style="{ background: '#F1F4F7', color: '#757575' }" border :stripe="true" :highlight-current-row="true">
          <el-table-column type="index" width="50" align="center" label="#" />
          <el-table-column :show-overflow-tooltip="true" :label="$t('lang_pack.dialogMain.lotID')" prop="lot_num">
            <template slot-scope="scope">
              <el-input :ref="'mark'+scope.$index" v-model="scope.row.lot_num" clearable size="mini" style="width: 100%" @input="handleInput($event,scope.$index)" @keyup.enter.native="nextFocus(scope.$index)" />
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" :label="$t('lang_pack.dialogMain.count')" prop="qty">
            <template slot-scope="scope">
              <el-input v-model="scope.row.qty" clearable size="mini" style="width: 100%" />
            </template>
          </el-table-column>
        </el-table>
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo">{{ $t('lang_pack.dialogMain.report') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    },
    DyCheckCode: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      portList: [{ port_index: '1', port_des: 'Port1' }, { port_index: '2', port_des: 'Port2' }],
      lot_list: [{ id: 1, lot_num: '', qty: '0' }, { id: 2, lot_num: '', qty: '0' }, { id: 3, lot_num: '', qty: '0' }, { id: 4, lot_num: '', qty: '0' }, { id: 5, lot_num: '', qty: '0' }],
      webPalletNum: '',
      webLotPortCode: '',
      isFirst: 'Y'
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webPalletNum.focus()
    })
    this.webPalletNum = this.tag_key_list.PalletNum
    this.webLotPortCode = this.tag_key_list.PortCode
  },
  methods: {
    // 自动聚焦下到一行
    nextFocus(index) {
      if (index >= 4) return
      this.$refs['mark' + (index + 1)].select()
      this.$refs['mark' + (index + 1)].focus()
    },
    handleInputPallNum(e) {
      if (this.DyCheckCode) {
        if (e.length === 12) {
          this.$message({ type: 'info', message: 'The length of the vehicle number cannot exceed 12 digits' })
          return
        }
        if (e.length > 12) {
          this.$message({ type: 'error', message: 'Vehicle number length greater than 12 digits, cleared' })
          this.webPalletNum = ''
          return
        }
      }
    },
    handleInput(e, index) {
      if (this.DyCheckCode) {
        if (e.length === 24) {
          this.$message({ type: 'info', message: `lotID${index + 1}length cannot greater than 24 digits` })
          return
        }
        if (e.length > 24) {
          this.$message({ type: 'error', message: `lotID${index + 1}length greater than 24 digits,cleared` })
          this.$set(this.lot_list[index], 'lot_num', '')
          return
        }
      }
    },
    handleSendInfo() {
      var lot_list2 = []
      var firstLotNum = ''
      if (this.webLotPortCode === '') {
        this.$message({ message: 'Please Select Port', type: 'info' })
        return
      }
      if (this.webPalletNum === '') {
        this.$message({ message: 'Please Input Carrier', type: 'info' })
        return
      }
      this.lot_list.forEach(item => {
        if (item.lot_num !== '' && item.qty !== '') {
          lot_list2.push({ lot_id: item.lot_num.toString(), lot_count: item.qty.toString() })
          if (firstLotNum === '') {
            firstLotNum = item.lot_num.toString()
          }
        }
      })
      if (lot_list2.length === 0) {
        this.$message({ message: 'Please Input LotList', type: 'info' })
        return
      }
      var lot_list_str=JSON.stringify(lot_list2)
      // 提交
      var sendJson = {}
      var rowJson = []
      var newRow2 = {
        TagKey: this.tag_key_list.WebLotNum,
        TagValue: this.webPalletNum
      }
      rowJson.push(newRow2)
      var newRow3 = {
        TagKey: this.tag_key_list.WebLotPortCode,
        TagValue: lot_list_str
      }
      rowJson.push(newRow3)
      var newRow = {
        TagKey: this.tag_key_list.WebLotRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebLotNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr, this.webPalletNum, lot_list2, this.webLotPortCode)
    },
    reflashLotCount(lot_count, isFirst) {
      this.isFirst = isFirst
      var lotCount = parseInt(lot_count)
      for (var i = 2; i <= lotCount; i++) {
        var itemJson = {}
        itemJson.id = i
        itemJson.lot_num = ''
        itemJson.qty = '0'
        this.lot_list.push(itemJson)
      }
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>

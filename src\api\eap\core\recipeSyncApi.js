import request from '@/utils/request'
import Cookies from 'js-cookie'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'

/**
 * 配方同步到设备API
 * @param {Object} data 请求数据
 * @param {string} data.user_name 用户名
 * @param {string} data.station_code 工站代码
 * @param {Array} data.Data 配方数据数组
 * @returns {Promise} 请求结果
 */
export function eapRecipeSynToDevice(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapRecipeSynToDevice',
    method: 'post',
    data
  })
}



/**
 * 配方同步工具类
 * <AUTHOR>
 * @date 2025/06/23
 */

/**
 * 获取系统参数值
 * @param {string} parameterCode 参数代码
 * @returns {Promise<string>} 参数值
 */
export function getSysParameterValue(parameterCode) {
  return new Promise((resolve, reject) => {
    const queryParameter = {
      userName: Cookies.get('userName'),
      parameter_code: parameterCode,
      enable_flag: 'Y'
    }
    
    selSysParameter(queryParameter)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0 && defaultQuery.data && defaultQuery.data.length > 0) {
          const parameterVal = defaultQuery.data[0].parameter_val
          resolve(parameterVal || '')
        } else {
          resolve('')
        }
      })
      .catch(error => {
        console.error(`获取系统参数${parameterCode}失败:`, error)
        reject(error)
      })
  })
}

/**
 * 检查是否需要同步配方到设备
 * @returns {Promise<boolean>} 是否需要同步
 */
export function checkRecipeSyncRequired() {
  return getSysParameterValue('RECIPE_SYN_TO_DEVICE')
    .then(value => {
      return value === 'Y' || (value && value.trim() !== '')
    })
    .catch(() => false)
}

/**
 * 同步配方到其他设备
 * @param {string} stationCode 工站代码
 * @param {Array} recipeData 配方数据
 * @param {string} userName 用户名
 * @returns {Promise<Object>} 同步结果
 */
export function syncRecipeToDevice(stationCode, recipeData, userName) {
  return new Promise((resolve, reject) => {
    if (!stationCode) {
      reject(new Error('station_code不能为空'))
      return
    }
    
    if (!recipeData || recipeData.length === 0) {
      reject(new Error('配方数据不能为空'))
      return
    }
    
    // 构建请求数据
    const requestData = {
      user_name: userName || Cookies.get('userName'),
      station_code: stationCode,
      Data: recipeData.map(item => ({
        TagKey: item.TagKey || item.tag_key,
        TagValue: item.TagValue || item.tag_value || item.parameter_val
      }))
    }
    
    // 调用后端同步接口
    eapRecipeSynToDevice(requestData)
      .then(response => {
        const result = JSON.parse(JSON.stringify(response))
        if (result.code === 0) {
          console.log('配方同步成功:', response)
          resolve(response)
        } else {
          // API返回错误码，抛出错误
          const errorMsg = result.msg || result.error || '配方同步失败'
          console.error('配方同步API返回错误:', result)
          reject(new Error(errorMsg))
        }
      })
      .catch(error => {
        console.error('配方同步网络错误:', error)
        reject(error)
      })
  })
}

/**
 * 配方下发后处理同步逻辑的主函数
 * @param {string} stationCode 工站代码
 * @param {Array} recipeData 配方数据
 * @param {string} userName 用户名
 * @returns {Object} 返回同步结果 { needSync: boolean, success: boolean, error?: string }
 */
export async function handleRecipeSyncAfterDispatch(stationCode, recipeData, userName) {
  try {
    // 1. 检查是否需要同步
    const needSync = await checkRecipeSyncRequired()
    
    if (!needSync) {
      console.log('系统未配置配方同步，跳过同步步骤')
      return { needSync: false, success: true }
    }
    
    // 2. 执行同步
    console.log('开始同步配方到其他设备...')
    await syncRecipeToDevice(stationCode, recipeData, userName)
    console.log('配方同步完成')
    
    return { needSync: true, success: true }
    
  } catch (error) {
    console.error('配方同步处理过程中发生错误:', error)
    return { needSync: true, success: false, error: error.message || '同步失败' }
  }
}
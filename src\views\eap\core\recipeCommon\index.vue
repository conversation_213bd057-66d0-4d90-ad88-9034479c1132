<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('core.recipe.recipeName')">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('core.recipe.versionNo')">
                <el-input v-model="query.recipe_version" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('core.recipe.recipeType')">
                <el-select v-model="query.recipe_type" clearable>
                  <el-option v-for="item in dict.EAP_RECIPE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('core.recipe.enableFlag')">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <div class="recipeInfo">
        <div style="visibility: hidden;">
          <!-- 预留位置 -->
        </div>
        <div v-if="isButtonVisible('edit')" style="display: flex;justify-content: right;align-items: center;">
          <span>{{ $t('lang_pack.wx.editParameters') }}</span>
          <el-switch v-model="disabled" active-color="#13ce66" inactive-color="#ff4949" />
        </div>
      </div>

      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="left">
          <el-button
            v-if="isButtonVisible('export')"
            v-permission="permission.export"
            slot="reference"
            class="filter-item"
            type="success"
            icon="el-icon-download"
            plain
            round
            size="small"
            :disabled="crud.selections.length === 0"
            @click="exportRecipe()"
          >
            {{ $t('core.recipe.exportRecipe') }}
          </el-button>
          <el-button
            v-if="isButtonVisible('template')"
            v-permission="permission.template"
            slot="reference"
            class="filter-item"
            type="warning"
            icon="el-icon-document"
            plain
            round
            size="small"
            @click="exportTemplate()"
          >
            {{ $t('core.recipe.downloadTemplate') }}
          </el-button>
          <el-upload
            v-if="isButtonVisible('import')"
            v-permission="permission.import"
            ref="upload"
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept=".xls"
            style="display: inline-block; margin-left: 10px;"
          >
            <el-button
              slot="trigger"
              class="filter-item"
              type="primary"
              icon="el-icon-upload2"
              plain
              round
              size="small"
            >
              {{ $t('core.recipe.importRecipe') }}
            </el-button>
          </el-upload>
          <!-- <el-button
            v-if="isButtonVisible('batchDelete')"
            v-permission="permission.batchDelete"
            slot="reference"
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="crud.selections.length === 0"
            @click="batchDelete()"
          >
            {{ $t('core.recipe.batchDelete') }}
          </el-button> -->
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('core.recipe.recipeType')" prop="recipe_type">
            <el-select v-model="form.recipe_type" clearable @change="chooseRecipeType">
              <el-option v-for="item in dict.EAP_RECIPE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('core.recipe.recipeName')" prop="recipe_name">
            <el-input v-model="form.recipe_name" />
          </el-form-item>
          <el-form-item :label="$t('core.recipe.versionNo')" prop="recipe_version">
            <el-input v-model="form.recipe_version" />
          </el-form-item>
          <el-form-item :label="$t('core.recipe.deviceCode')" prop="device_code">
            <el-input v-model="form.device_code" />
          </el-form-item>
          <el-form-item :label="$t('core.recipe.deviceDes')" prop="device_des">
            <el-input v-model="form.device_des" />
          </el-form-item>
          <el-form-item :label="$t('core.recipe.materialCode')" prop="material_code">
            <el-input v-model="form.material_code" />
          </el-form-item>
          <el-form-item :label="$t('core.recipe.materialDes')" prop="material_des">
            <el-input v-model="form.material_des" />
          </el-form-item>
          <el-form-item :label="$t('core.recipe.enableFlag')" prop="enable_flag">
            <el-select v-model="form.enable_flag" clearable>
              <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <div v-if="copyInfo.show" style="background-color: #f5f7fa; padding: 15px; margin-bottom: 20px; border-radius: 4px; border-left: 4px solid #409eff;">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
               <div><strong>{{ $t('core.recipe.copyRecipeName') }}:</strong> {{ copyInfo.originalName }}</div>
             </div>
          </div>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">取消</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">确认</el-button>
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column  type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column  v-if="1 == 0" width="10" prop="recipe_id" label="id" />
              <el-table-column  :show-overflow-tooltip="true" prop="recipe_type" :label="$t('core.recipe.recipeType')">
                <template slot-scope="scope">
                  {{ getRecipeTypeLabel(scope.row.recipe_type) }}
                </template>
              </el-table-column>
              <el-table-column  :show-overflow-tooltip="true" prop="recipe_name" :label="$t('core.recipe.recipeName')" />
              <el-table-column  :show-overflow-tooltip="true" prop="recipe_version" :label="$t('core.recipe.versionNo')" />
              <el-table-column  :show-overflow-tooltip="true" prop="device_code" :label="$t('core.recipe.deviceCode')" />
              <el-table-column  :show-overflow-tooltip="true" prop="device_des" :label="$t('core.recipe.deviceDes')" />
              <el-table-column  :show-overflow-tooltip="true" prop="material_code" :label="$t('core.recipe.materialCode')" />
              <el-table-column  :show-overflow-tooltip="true" prop="material_des" :label="$t('core.recipe.materialDes')" />
              <el-table-column  :label="$t('core.recipe.enableFlag')" align="center" prop="enable_flag">
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
                </template>
              </el-table-column>
              <el-table-column  label="操作" width="220" align="center" fixed="right">
                <template slot-scope="scope">
                  <operation :data="scope.row" :permission="permission" :disabled-edit="!isButtonVisible('edit')" :disabled-dle="!isButtonVisible('del')" @ok="doDelete">
                    <template slot="right">
                      <el-button v-if="isButtonVisible('copy')" v-permission="permission.copy" slot="reference" type="text" size="small" @click="handleCopyRecipe(scope.row)">{{ $t('core.recipe.copyRecipe') }}</el-button>
                      <el-button v-if="isButtonVisible('addParams')" v-permission="permission.addParams" slot="reference" type="text" size="small" @click="$refs.detail && $refs.detail.crud.toAdd()">{{ $t('lang_pack.wx.addParams') }}</el-button>
                    </template>
                  </operation>
                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <detail ref="detail" class="tableFirst" :recipe_detail_id="currentRecipeId" :disabled="disabled" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import eapRecipeCommon from '@/api/eap/core/eapRecipeCommon'
import detail from './detail'
import Cookies from 'js-cookie'
import axios from 'axios'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import operation from '@/views/core/system/errorMsg/operation.vue'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import { EapRecipeCommonExport, EapRecipeCommonImport } from '@/api/eap/core/eapRecipeCommon'
const defaultForm = {
  recipe_id: '',
  recipe_name: '',
  recipe_version: '',
  device_code: '',
  device_des: '',
  material_code: '',
  material_des: '',
  recipe_type: '',
  enable_flag: 'Y'
}
export default {
  name: 'EAP_RECIPE_COMMON',
  components: { crudOperation, rrOperation, udOperation, pagination, detail, operation },
  props: {},
  cruds() {
    return CRUD({
      title: '通用配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id desc'],
      // CRUD Method
      crudMethod: { ...eapRecipeCommon },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false, // 隐藏删除按钮
        down: false,
        reset: true
      }
    })
  },
  hooks() {
    return {
      afterToCU: (crud, form) => {
        // 清除复制信息
        this.copyInfo.show = false
        this.copyInfo.originalName = ''
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'EAP_RECIPE_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'eap_recipe_common:add'],
        edit: ['admin', 'eap_recipe_common:edit'],
        del: false, // 隐藏删除按钮del: ['admin', 'eap_recipe_common:del'],
        copy: ['admin', 'eap_recipe_common:copy'],
        addParams: ['admin', 'eap_recipe_common:addParams'],
        download: ['admin', 'eap_recipe_common:download'],
        export: ['admin', 'eap_recipe_common:export'],
        template: ['admin', 'eap_recipe_common:template'],
        import: ['admin', 'eap_recipe_common:import'],
        batchDelete: ['admin', 'eap_recipe_common:batchDelete']
      },
      copyInfo: {
        show: false,
        originalName: ''
      },
      rules: {
        recipe_type: [{ required: true, message: this.$t('core.recipe.recipeTypeRequired'), trigger: 'blur' }],
        recipe_version: [{ required: true, message: this.$t('core.recipe.versionNoRequired'), trigger: 'blur' }],
        recipe_name: [{ required: true, message: this.$t('core.recipe.recipeNameRequired'), trigger: 'blur' }],
        material_code: [{ required: false, message: this.$t('core.recipe.materialCodeRequired'), trigger: 'blur' }],
        material_des: [{ required: false, message: this.$t('core.recipe.materialDesRequired'), trigger: 'blur' }],
        device_code: [{ required: false, message: this.$t('core.recipe.deviceCodeRequired'), trigger: 'blur' }],
        device_des: [{ required: false, message: this.$t('core.recipe.deviceDesRequired'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('core.recipe.enableFlagRequired'), trigger: 'blur' }]
      },
      currentRecipeId: 0,
      disabled: false
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
    
    // 根据权限动态设置每个按钮的显示
    this.crud.optShow.add = this.isButtonVisible('add')
    this.crud.optShow.edit = this.isButtonVisible('edit')
    this.crud.optShow.del = false // 直接隐藏删除按钮    this.crud.optShow.del = this.isButtonVisible('del')

    
    // 输出权限配置调试信息

  },
  created: function() {
  },
  methods: {
    // 获取配方类型的中文标签
    getRecipeTypeLabel(value) {
      const recipeType = this.dict.EAP_RECIPE_TYPE.find(item => item.value === value)
      return recipeType ? recipeType.label : value
    },
    chooseRecipeType(val) {
      console.log(val)
      if (val === 'MATERIAL') {
        this.rules.material_code[0].required = true
        this.rules.material_des[0].required = true
        this.rules.device_code[0].required = false
        this.rules.device_des[0].required = false
      } else {
        this.rules.material_code[0].required = false
        this.rules.material_des[0].required = false
        this.rules.device_code[0].required = true
        this.rules.device_des[0].required = true
      }
    },
    handleRowClick(row, column, event) {
      this.currentRecipeId = row.recipe_id
      
    },
    // 检查用户是否有指定权限
    hasPermission(permission) {
      try {
        // 获取用户权限信息
        var userInfo = this.$store.state.user || {}
        var userPermissions = []
         
        
        // 优先从Store的permissions字段获取
        if (this.$store.state.user.permissions && Array.isArray(this.$store.state.user.permissions)) {
          userPermissions = this.$store.state.user.permissions
        } else if (userInfo.permissions && Array.isArray(userInfo.permissions)) {
          userPermissions = userInfo.permissions
        } else {
          userPermissions = []
        }
          
        // 确保permissions是数组
        if (!Array.isArray(userPermissions)) {
          userPermissions = []
        }
        // 检查是否包含指定权限
        var hasPermission = userPermissions.includes(permission)
          
        return hasPermission
        
      } catch (error) {
        console.error('权限检查出错:', error)
        // 出错时返回false，确保安全
        return false
      }
    },
    // 检查按钮是否可见（基于权限控制）
    isButtonVisible(permissionType) {
       
      if (!permissionType) {
        var result = this.hasPermission('eap_recipe_common:edit') || 
                    this.hasPermission('eap_recipe_common:add') ||
                    this.hasPermission('eap_recipe_common:del')
        console.log('默认权限检查结果:', result)
        return result
      }
      
      var permissionMap = {
        'add': 'eap_recipe_common:add',
        'edit': 'eap_recipe_common:edit',
        'del': 'eap_recipe_common:del',
        'copy': 'eap_recipe_common:copy',
        'addParams': 'eap_recipe_common:addParams',
        'download': 'eap_recipe_common:download',
        'export': 'eap_recipe_common:export',
        'template': 'eap_recipe_common:template',
        'import': 'eap_recipe_common:import',
        'batchDelete': 'eap_recipe_common:batchDelete'
      }
      var permission = permissionMap[permissionType]
       
      if (permission) {
        var result = this.hasPermission(permission)
        return result
      }
      
      return false
    },
    // 多个删除
    batchDelete() {
      this.$confirm(this.$t('core.recipe.confirmBatchDelete') + ' ' + `${this.crud.selections.length}` + ' ' + this.$t('core.recipe.dataItems'),
        '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          this.crud.delAllLoading = true
          const ids = []
          this.crud.selections.forEach(val => {
            ids.push(val.recipe_id)
          })
          eapRecipeCommon.delAll(ids).then(() => {
            this.crud.delAllLoading = false
            this.crud.dleChangePage(1)
            this.crud.delSuccessNotify()
            this.crud.toQuery()
            // 删除父级的数据后，子级table也要清空
            this.$refs.detail.crud.data = []
          }).catch(err => {
            this.crud.delAllLoading = false
            this.crud.notify('删除失败', CRUD.NOTIFICATION_TYPE.ERROR)
            console.log(err.response.data.message)
          })
        }).catch(() => {})
    },
    // 单个删除
    doDelete(data) {
      const query = {
        ids: data.recipe_id,
        recipeFileNames: data.recipe_name + '-' + data.recipe_version,
        user_name: Cookies.get('userName')
      }
      this.delete(query)
    },
    delete(data) {
      eapRecipeCommon.del(data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: '删除成功',
            type: 'success'
          })
          this.crud.toQuery()
          // 删除父级的数据后，子级table也要清空
          this.$refs.detail.crud.data = []
        } else {
          this.$message({
            message: res.msg || '操作失败',
            type: 'error'
          })
        }
      }).catch((e) => {
        this.$message({
          message: '操作失败',
          type: 'error'
        })
      })
    },
    // 复制配方
    copyRecipe() {
      if (this.crud.selections.length !== 1) {
        this.$message({
           message: this.$t('core.recipe.selectOneRecipeToCopy'),
           type: 'warning'
         })
        return
      }

      const selectedRecipe = this.crud.selections[0]
      this.performCopy(selectedRecipe)
    },
    // 处理单行复制
    handleCopyRecipe(row) {
      this.performCopy(row)
    },
    // 执行复制操作
    performCopy(selectedRecipe) {
      const currentDateTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')

      const copiedData = {
        recipe_type: selectedRecipe.recipe_type,
        recipe_name: selectedRecipe.recipe_name ? `${selectedRecipe.recipe_name}_复制_${currentDateTime}` : '',
        recipe_version: selectedRecipe.recipe_version ? `${selectedRecipe.recipe_version}_复制_${currentDateTime}` : '',
        material_code: selectedRecipe.material_code || '',
        material_des: selectedRecipe.material_des || '',
        device_code: selectedRecipe.device_code || '',
        device_des: selectedRecipe.device_des || '',
        enable_flag: selectedRecipe.enable_flag || '',
        copy_id: selectedRecipe.recipe_id
      }

      this.crud.toAdd()

      Object.keys(copiedData).forEach(key => {
        if (copiedData[key] !== undefined && copiedData[key] !== null) {
          this.crud.form[key] = copiedData[key]
        }
      })

      this.copyInfo.show = true
      this.copyInfo.originalName = selectedRecipe.recipe_name || ''
    },
    // 导出配方
    exportRecipe() {
      if (this.crud.selections.length === 0) {
        this.$message({
           type: 'warning',
           message: this.$t('core.recipe.selectRecipeToExport')
         })
        return
      }

      if (this.crud.selections.length > 1) {
        this.$message({
           type: 'warning',
           message: this.$t('core.recipe.selectOneRecipeToExport')
         })
        return
      }

      const selectedRecipe = this.crud.selections[0]
      const exportData = {
        recipe_id: selectedRecipe.recipe_id,
        recipe_name: selectedRecipe.recipe_name
      }

      EapRecipeCommonExport(exportData).then(response => {
        const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        const now = new Date()
        const timestamp = now.getFullYear().toString() +
                         (now.getMonth() + 1).toString().padStart(2, '0') +
                         now.getDate().toString().padStart(2, '0') +
                         now.getHours().toString().padStart(2, '0') +
                         now.getMinutes().toString().padStart(2, '0') +
                         now.getSeconds().toString().padStart(2, '0')
        link.download = `export_${selectedRecipe.recipe_name}_${selectedRecipe.recipe_version}_${timestamp}.xls`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message({
           type: 'success',
           message: this.$t('core.recipe.exportSuccess')
         })
      }).catch(error => {
        console.error('导出配方失败:', error)
        this.$message({
           type: 'error',
           message: this.$t('core.recipe.exportFailed')
         })
      })
    },
    // 下载模板
    exportTemplate() {
      const loading = this.$loading({
        lock: true,
        text: '正在生成模板...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      axios({
          method: 'get',
          url: '/aisEsbWeb/eap/core/common/EapRecipeExportTemplate',
          responseType: 'blob'
        }).then(response => {
        loading.close()

        const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        const now = new Date()
        const timestamp = now.getFullYear().toString() +
                         (now.getMonth() + 1).toString().padStart(2, '0') +
                         now.getDate().toString().padStart(2, '0') +
                         now.getHours().toString().padStart(2, '0') +
                         now.getMinutes().toString().padStart(2, '0') +
                         now.getSeconds().toString().padStart(2, '0')
        link.download = `recipe_template_${timestamp}.xls`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message({
           type: 'success',
           message: this.$t('core.recipe.templateDownloadSuccess')
         })
      }).catch(error => {
        loading.close()
        console.error('下载模板失败:', error)
        this.$message({
           type: 'error',
           message: this.$t('core.recipe.templateDownloadFailed')
         })
      })
    },
    // 处理文件选择
    handleFileChange(file, fileList) {
      if (!file.raw) {
        return
      }

      if (!file.name.endsWith('.xls')) {
        this.$message({
           type: 'error',
           message: this.$t('core.recipe.onlyXlsSupported')
         })
        return
      }

      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('userName', Cookies.get('userName'))

      const loading = this.$loading({
        lock: true,
        text: '正在导入配方...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      EapRecipeCommonImport(formData).then(response => {
        loading.close()

        const reader = new FileReader()
        reader.onload = () => {
          try {
            const result = JSON.parse(reader.result)
            if (result.result === '配方導入成功' || result.code === 0) {
              this.$message({
                 type: 'success',
                 message: result.result || result.msg || this.$t('core.recipe.importSuccess')
               })
              this.crud.toQuery()
            } else {
              this.$alert(result.result || result.msg || this.$t('core.recipe.importFailed'), '导入失败', {
                confirmButtonText: '确定',
                type: 'error',
                dangerouslyUseHTMLString: true,
                customClass: 'wide-alert-dialog'
              })
            }
          } catch (e) {
            this.$alert('导入响应解析失败', '解析错误', {
              confirmButtonText: '确定',
              type: 'error',
              customClass: 'wide-alert-dialog'
            })
          }
        }
        reader.readAsText(response)
      }).catch(error => {
        loading.close()
        console.error('导入配方失败:', error)
        this.$alert(this.$t('core.recipe.importFailed'), '导入错误', {
          confirmButtonText: '确定',
          type: 'error',
          customClass: 'wide-alert-dialog'
        })
      })

      this.$refs.upload.clearFiles()
    },
    // 批量删除
    batchDelete() {
      this.$confirm(this.$t('core.recipe.confirmBatchDelete') + ' ' + `${this.crud.selections.length}` + ' ' + this.$t('core.recipe.dataItems'),
        '提示', {
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          type: 'warning'
        })
        .then(() => {
          this.crud.delAllLoading = true
          const ids = []
          this.crud.selections.forEach(val => {
            ids.push(val.recipe_id)
          })
          eapRecipeCommon.delAll(ids).then(() => {
            this.crud.delAllLoading = false
            this.crud.dleChangePage(1)
            this.crud.delSuccessNotify()
            this.crud.toQuery()
            // 删除父级的数据后，子级table也要清空
            this.$refs.detail.crud.data = []
          }).catch(err => {
            this.crud.delAllLoading = false
            this.crud.notify('删除失败', CRUD.NOTIFICATION_TYPE.ERROR)
            console.log(err.response.data.message)
          })
        }).catch(() => {})
    }
  }
}
</script>
<style lang="less" scoped>
.wrapRowItem {
  display: flex;
  justify-content: space-between;
  .el-table {
    border-radius: 10px;
    border-color: rgba(0, 0, 0, 0.09);
    box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
  }
  .el-row {
    width: 50%;
  }
}
.recipeInfo {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
</style>

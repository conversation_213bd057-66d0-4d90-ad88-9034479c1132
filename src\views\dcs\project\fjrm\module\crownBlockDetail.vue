<template>
  <el-dialog :append-to-body="true" modal-append-to-body :title="modalTitle" :visible.sync="dialogVisible" :width="modalWidth" :before-close="handleClose">
    <el-table
      ref="table"
      v-loading="crud.loading"
      border
      size="small"
      :data="this.alarmDate"
      style="width: 100%"
      :cell-style="crud.cellStyle"
      :height="150"
      highlight-current-row
      @header-dragend="crud.tableHeaderDragend()"
      @selection-change="crud.selectionChangeHandler"
    >
      <el-table-column :show-overflow-tooltip="true" prop="time" label="时间" />
      <el-table-column :show-overflow-tooltip="true" prop="alarm_description" label="报警描述" />
      <el-table-column :show-overflow-tooltip="true" prop="alarm_status" label="报警状态" />
    </el-table>
    <div class="header">天车信息</div>
    <div class="car-info">
      <el-descriptions title="" :column="2" border class="left">
        <el-descriptions-item label="抓斗位置" label-class-name="my-label" content-class-name="my-content">{{ ((this.westZ-0+375)/1375).toFixed(0) }}</el-descriptions-item>
        <el-descriptions-item label="升降速度" label-class-name="my-label" content-class-name="my-content">{{ (this.speedZ-0).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="大车位置" label-class-name="my-label" content-class-name="my-content">{{ ((this.westX-2800)/3400+1).toFixed(0) }}</el-descriptions-item>
        <el-descriptions-item label="大车速度" label-class-name="my-label" content-class-name="my-content">{{ (this.speedX-0).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="小车位置" label-class-name="my-label" content-class-name="my-content">{{ -((this.westY-2000)/3100-6).toFixed(0) }}</el-descriptions-item>
        <el-descriptions-item label="小车速度" label-class-name="my-label" content-class-name="my-content">{{ (this.speedY-0).toFixed(2) }}</el-descriptions-item>
        <el-descriptions-item label="上升到位" label-class-name="my-label" content-class-name="my-content">{{ this.riseIntoPosition }}</el-descriptions-item>
        <el-descriptions-item label="上升减速" label-class-name="my-label" content-class-name="my-content">{{ this.RisingDeceleration }}</el-descriptions-item>
      </el-descriptions>
      <el-descriptions title="" :column="2" border class="right">
        <el-descriptions-item label="吊具顶升到位信号西北" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace1 === '1', 'gray-light': this.LiftInPlace1 === '0' }" />
        </el-descriptions-item>
        <el-descriptions-item label="吊具顶升到位信号西南" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace2 === '1', 'gray-light': this.LiftInPlace2 === '0' }" />
        </el-descriptions-item>
        <el-descriptions-item label="吊具顶升到位信号东北" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace3 === '1', 'gray-light': this.LiftInPlace3 === '0' }" />
        </el-descriptions-item>
        <el-descriptions-item label="吊具顶升到位信号东南" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace4 === '1', 'gray-light': this.LiftInPlace4 === '0' }" />
        </el-descriptions-item>
        <el-descriptions-item label="吊具开锁到位" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace5 === '1', 'gray-light': this.LiftInPlace5 === '0' }" />
        </el-descriptions-item>
        <el-descriptions-item label="吊具闭锁到位" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace6 === '1', 'gray-light': this.LiftInPlace6 === '0' }" />
        </el-descriptions-item>
        <el-descriptions-item label="上电就绪" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace7 === '1', 'gray-light': this.LiftInPlace7 === '0' }" />
        </el-descriptions-item>
        <el-descriptions-item label="执行任务执行中" label-class-name="my-label" content-class-name="my-content">
          <div class="light" :class="{ 'blue-light': this.LiftInPlace8 === '1', 'gray-light': this.LiftInPlace8 === '0' }" />
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="sports-position-info">
      <!-- <div class="sports-info">
        <div class="header">运动信息</div>
        <div class="table-wrapper">
        </div>
      </div> -->
      <div class="position-info">
        <div class="header">位置信息</div>
        <div class="table-wrapper">
          <table>
            <thead>
              <tr>
                <th />
                <th>实际</th>
                <th>目标</th>
                <th>▲</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>X:</th>
                <td>{{ ((this.westX-2800)/3400+1).toFixed(0) }} </td>
                <td>{{ ((this.taskX-2800)/3400+1).toFixed(0) }} </td>
                <td>{{ (((this.westX-2800)/3400+1).toFixed(0))-(((this.taskX-2800)/3400+1).toFixed(0)) }} </td>
              </tr>
              <tr>
                <th>Y:</th>
                <td>{{ -((this.westY-2000)/3100-6).toFixed(0) }} </td>
                <td>{{ -((this.taskY-2000)/3100-6).toFixed(0) }} </td>
                <td>{{ (-((this.westY-2000)/3100-6).toFixed(0))-(-((this.taskY-2000)/3100-6).toFixed(0)) }} </td>
              </tr>
              <tr>
                <th>Z:</th>
                <td>{{ ((this.westZ-0+375)/1375).toFixed(0) }} </td>
                <td>{{ ((this.taskZ-0+375)/1375).toFixed(0) }} </td>
                <td>{{ (((this.westZ-0+375)/1375).toFixed(0))-(((this.taskZ-0+375)/1375).toFixed(0)) }} </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="control-info">
        <div class="header">控制信息</div>
        <div class="mode">
          <span>模式切换：</span>
          <el-button @click="(EasyHandycam2('SlCarPlc01/DcsStatus/RcsStartTask2'))"> 自动</el-button>
          <el-button @click="(EasyHandycam3('SlCarPlc01/DcsStatus/RcsStartTask2'))"> 手动</el-button>
          <!-- <el-button> 检修</el-button> -->
        </div>
        <!-- <div class="operation">
          <span>操作按钮：</span>
          <el-button> 前移车</el-button>
          <el-button> 抓框</el-button>
          <el-button> 后移车</el-button>
          <el-button> 放框</el-button>
        </div> -->
        <!-- <div class="input-info">
          <span>&emsp;&emsp;&emsp;&nbsp;Y：</span>
          <el-input v-model="inputValue" />
          <span>&emsp;&emsp;&nbsp;Z：</span>
          <el-input v-model="inputValue" />
        </div>
        <div class="input-info">
          <span>&emsp;&nbsp;X高位：</span>
          <el-input />
          <span>X低位：</span>
          <el-input />
        </div> -->
        <div class="manual-stack">
          <span>手动倒垛：</span>
          <el-input
            v-model="start"
            placeholder="请输入起始货位（如：010101）"
            size="mini"
            style="width: 180px; margin: 0 8px;"
          />
          <svg-icon icon-class="right" />
          <el-input
            v-model="target"
            placeholder="请输入目标货位（如：B03-01-01）"
            size="mini"
            style="width: 180px; margin: 0 8px;"
          />
          <el-button @click="InventoryScheduling()"> 确定</el-button>
        </div>
      </div>
    </div>
    <div class="footer">
      <div class="control-btn">
        <el-button type="primary" class="A">西行车 当前模式：{{ this.AutoMode }}</el-button>
        <el-button type="primary" class="D" @click="(EasyHandycam('SlCarPlc01/DcsStatus/PowerOn'))">上总电</el-button>
        <el-button type="primary" class="E" @click="(EasyHandycam('SlCarPlc01/DcsStatus/Poweroff'))">断总电</el-button>
        <el-button type="primary" class="F" @click="(EasyHandycam2('SlCarPlc01/DcsStatus/Lighting'))">开照明</el-button>
        <el-button type="primary" class="G" @click="(EasyHandycam3('SlCarPlc01/DcsStatus/Lighting'))">关照明</el-button>
        <el-button type="primary" class="B" @click="(EasyHandycam3('SlCarPlc01/DcsStatus/RcsStartTask2'))">紧急停止</el-button>
        <el-button type="primary" class="H" @click="(EasyHandycam('SlCarPlc01/DcsStatus/restart'))">重新启动</el-button>
        <el-button type="primary" class="C" @click="(EasyHandycam('SlCarPlc01/DcsStatus/FaultReset'))">故障复位</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import dayjs from 'dayjs'
import LargeScreenPage from '@/api/dcs/project/fjrm/LargeScreenPage/LargeScreenPage'
import { DcsFmodMapMeStockSelect } from '@/api/dcs/project/fjrm/stock/stock'
import Cookies from 'js-cookie'
import crownBlock from '@/api/dcs/project/fjrm/crownBlock.json'
import { selCellIP } from '@/api/core/center/cell'
import axios from 'axios'
import Paho from 'paho-mqtt'
import crudWmsFjTask from '@/api/dcs/project/wms/wmsFjTask'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
const defaultForm = {
}
export default {
  name: 'CROWBLOCKDETAIL',
  cruds() {
    return CRUD({
      title: '天车任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'fj_task_id',
      // 排序
      sort: ['fj_task_id desc'],
      // CRUD Method
      crudMethod: { ...crudWmsFjTask },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      dialogVisible: false,
      modalTitle: '查看西行车明细',
      modalWidth: '50%',
      value: false,
      inputValue: '',
      start: '',
      target: '',
      westX: '',
      westY: '',
      westZ: '',
      taskX: '',
      taskY: '',
      taskZ: '',
      speedX: '',
      speedY: '',
      speedZ: '',
      RisingDeceleration: '',
      riseIntoPosition: '',
      AutoMode: '',
      LiftInPlace1: '',
      LiftInPlace2: '',
      LiftInPlace3: '',
      LiftInPlace4: '',
      LiftInPlace5: '',
      LiftInPlace6: '',
      LiftInPlace7: '',
      LiftInPlace8: '',
      tagKeyList: [
        'SlCarPlc01/PlcStatus/Tag023', // 西边x大车激光
        'SlCarPlc01/PlcStatus/Tag022', // 西边y小车车激光
        'SlCarPlc01/PlcStatus/Tag024', // 起升位置
        'SlCarPlc01/PlcStatus/Tag011', // 任务.X
        'SlCarPlc01/PlcStatus/Tag012', // 任务.Y
        'SlCarPlc01/PlcStatus/Tag013', // 任务.Z
        'SlCarPlc01/PlcStatus/Tag035', // 速度.X
        'SlCarPlc01/PlcStatus/Tag036', // 速度.Y
        'SlCarPlc01/PlcStatus/Tag034', // 速度.Z
        'SlCarPlc01/PlcStatus/Tag032', // 上升减速
        'SlCarPlc01/PlcStatus/Tag033', // 上升到位
        'SlCarPlc01/PlcStatus/Tag009', // 自动模式
        'SlCarPlc01/PlcStatus/Tag028', // 吊具顶升到位信号西北
        'SlCarPlc01/PlcStatus/Tag029', // 吊具顶升到位信号西南
        'SlCarPlc01/PlcStatus/Tag030', // 吊具顶升到位信号东北
        'SlCarPlc01/PlcStatus/Tag031', // 吊具顶升到位信号东南
        'SlCarPlc01/PlcStatus/Tag038', // 吊具开锁到位
        'SlCarPlc01/PlcStatus/Tag039', // 吊具闭锁到位
        'SlCarPlc01/PlcStatus/Tag037', // 上电就绪
        'SlCarPlc01/PlcStatus/Tag016', // 执行任务执行中
        'SlCarPlc01/PlcAlarm/police1', // 总接触器未上电
        'SlCarPlc01/PlcAlarm/police2', // 相序故障
        'SlCarPlc01/PlcAlarm/police3', // 支持超载
        'SlCarPlc01/PlcAlarm/police4', // 柜内湿度报警
        'SlCarPlc01/PlcAlarm/police5', // 柜内烟雾报警
        'SlCarPlc01/PlcAlarm/police6', // 格雷母线大车数据异常
        'SlCarPlc01/PlcAlarm/police7', // 格雷母线小车数据异常
        'SlCarPlc01/PlcAlarm/police8', // 大车制动器断路器未合
        'SlCarPlc01/PlcAlarm/police9', // 大车变频器故障
        'SlCarPlc01/PlcAlarm/police10', // 大车热继故障
        'SlCarPlc01/PlcAlarm/police11', // 大车激光故障
        'SlCarPlc01/PlcAlarm/police12', // 小车制动器断路器未合
        'SlCarPlc01/PlcAlarm/police13', // 小车变频器故障
        'SlCarPlc01/PlcAlarm/police14', // 小车激光故障
        'SlCarPlc01/PlcAlarm/police15', // 支持制动器断路器未合
        'SlCarPlc01/PlcAlarm/police16', // 支持变频器故障
        'SlCarPlc01/PlcAlarm/police17', // 支持溜钩故障
        'SlCarPlc01/PlcAlarm/police18', // 支持编码器故障
        'SlCarPlc01/PlcAlarm/police19', // 支持激光故障
        'SlCarPlc01/PlcAlarm/police20', // 夹具通讯故障
        'SlCarPlc01/PlcAlarm/police21', // 左伺服故障
        'SlCarPlc01/PlcAlarm/police22', // 右伺服故障
        'SlCarPlc01/PlcAlarm/police23', // 葫芦变频器故障
        'SlCarPlc01/PlcAlarm/police24', // 大车西极限开关触发
        'SlCarPlc01/PlcAlarm/police25', // 大车东极限开关触发
        'SlCarPlc01/PlcAlarm/police26', // 大车西减速开关触发
        'SlCarPlc01/PlcAlarm/police27', // 大车东减速开关触发
        'SlCarPlc01/PlcAlarm/police28', // 小车北极限开关触发
        'SlCarPlc01/PlcAlarm/police29', // 小车南极限开关触发
        'SlCarPlc01/PlcAlarm/police30', // 小车北减速开关触发
        'SlCarPlc01/PlcAlarm/police31', // 小车南减速开关触发
        'SlCarPlc01/PlcAlarm/police32', // 支持上升极限开关触发
        'SlCarPlc01/PlcAlarm/police33', // 支持下降极限开关触发
        'SlCarPlc01/PlcAlarm/police34', // 支持上升减速开关触发
        'SlCarPlc01/PlcAlarm/police35', // 支持下降减速开关触发
        'SlCarPlc01/PlcAlarm/police36', // 中控急停触发
        'SlCarPlc01/PlcAlarm/police37', // 3#柜门急停触发
        'SlCarPlc01/PlcAlarm/police38', // 4#柜门急停触发
        'SlCarPlc01/PlcAlarm/police39', // 东吊具柜门急停触发
        'SlCarPlc01/PlcAlarm/police40', // 西吊具柜门急停触发
        'SlCarPlc01/PlcAlarm/police41', // 1#按钮盒急停触发
        'SlCarPlc01/PlcAlarm/police42', // 2#按钮盒急停触发
        'SlCarPlc01/PlcAlarm/police43', // 3#按钮盒急停触发
        'SlCarPlc01/PlcAlarm/police44', // 4#按钮盒急停触发
        'SlCarPlc01/PlcAlarm/police45', // 5#按钮盒急停触发
        'SlCarPlc01/PlcAlarm/police46', // 1#入库门急停触发
        'SlCarPlc01/PlcAlarm/police47', // 2#入库门急停触发
        'SlCarPlc01/PlcAlarm/police48', // 3#入库门急停触发
        'SlCarPlc01/PlcAlarm/police49', // 4#入库门急停触发
        'SlCarPlc01/PlcAlarm/police50', // 5#入库门急停触发
        'SlCarPlc01/PlcAlarm/police51', // 大车左软限位触发
        'SlCarPlc01/PlcAlarm/police52', // 大车右软限位触发
        'SlCarPlc01/PlcAlarm/police53', // 小车前软限位触发
        'SlCarPlc01/PlcAlarm/police54', // 小车后软限位触发
        'SlCarPlc01/PlcAlarm/police55', // 支持上软极限触发
        'SlCarPlc01/PlcAlarm/police56', // 支持下软极限触发
        'SlCarPlc01/PlcAlarm/police57', // 大车软限位屏蔽开启
        'SlCarPlc01/PlcAlarm/police58', // 小车软限位屏蔽开启
        'SlCarPlc01/PlcAlarm/police59', // 支持软限位屏蔽开启
        'SlCarPlc01/PlcAlarm/police60', // 支持编码器标定完成
        'SlCarPlc01/PlcAlarm/police61', // 1#人员入库门打开
        'SlCarPlc01/PlcAlarm/police62', // 2#人员入库门打开
        'SlCarPlc01/PlcAlarm/police63', // 3#人员入库门打开
        'SlCarPlc01/PlcAlarm/police64', // 4#人员入库门打开
        'SlCarPlc01/PlcAlarm/police65', // 5#人员入库门打开
        'SlCarPlc01/PlcAlarm/police66', // 1#通道门打开
        'SlCarPlc01/PlcAlarm/police67', // 2#通道门打开
        'SlCarPlc01/PlcAlarm/police68', // 物流门打开
        'SlCarPlc01/PlcAlarm/police69', // 抓放料高度异常
        'SlCarPlc01/PlcAlarm/police70', // 大车变频器通讯故障
        'SlCarPlc01/PlcAlarm/police71', // 小车变频器通讯故障
        'SlCarPlc01/PlcAlarm/police72', // 支持变频器通讯故障
        'SlCarPlc01/PlcAlarm/police73', // 与东行车通讯故障
        'SlCarPlc01/PlcAlarm/police74', // 与中控PLC通讯故障
        'SlCarPlc01/PlcAlarm/police75', // 与3#地面柜PLC通讯故障
        'SlCarPlc01/PlcAlarm/police76', // 与4#地面柜PLC通讯故障
        'SlCarPlc01/PlcAlarm/police77', // 顶升未到位
        'SlCarPlc01/PlcAlarm/police78', // 前端梁门打开
        'SlCarPlc01/PlcAlarm/police79', // 后端梁门打开
        'SlCarPlc01/PlcAlarm/police80', // 人员入库
        'SlCarPlc01/PlcAlarm/police81', // 大车制动未打开
        'SlCarPlc01/PlcAlarm/police82', // 小车制动未打开
        'SlCarPlc01/PlcAlarm/police83', // 支持制动未打开
        'SlCarPlc01/PlcAlarm/police84' // 夹具开闭锁状态异常
      ],
      clients: {},
      alarmDate: [],
      alarmConfigs: [
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police1',
          description: '总接触器未上电'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police2',
          description: '相序故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police3',
          description: '支持超载'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police4',
          description: '柜内湿度报警'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police5',
          description: '柜内烟雾报警'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police6',
          description: '格雷母线大车数据异常'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police7',
          description: '格雷母线小车数据异常'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police8',
          description: '大车制动器断路器未合'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police9',
          description: '大车变频器故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police10',
          description: '大车热继故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police11',
          description: '大车激光故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police12',
          description: '小车制动器断路器未合'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police13',
          description: '小车变频器故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police14',
          description: '小车激光故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police15',
          description: '支持制动器断路器未合'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police16',
          description: '支持变频器故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police17',
          description: '支持溜钩故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police18',
          description: '支持编码器故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police19',
          description: '支持激光故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police20',
          description: '夹具通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police21',
          description: '左伺服故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police22',
          description: '右伺服故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police23',
          description: '葫芦变频器故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police24',
          description: '大车西极限开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police25',
          description: '大车东极限开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police26',
          description: '大车西减速开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police27',
          description: '大车东减速开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police28',
          description: '小车北极限开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police29',
          description: '小车南极限开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police30',
          description: '小车北减速开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police31',
          description: '小车南减速开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police32',
          description: '支持上升极限开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police33',
          description: '支持下降极限开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police34',
          description: '支持上升减速开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police35',
          description: '支持下降减速开关触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police36',
          description: '中控急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police37',
          description: '3#柜门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police38',
          description: '4#柜门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police39',
          description: '东吊具柜门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police40',
          description: '西吊具柜门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police41',
          description: '1#按钮盒急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police42',
          description: '2#按钮盒急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police43',
          description: '3#按钮盒急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police44',
          description: '4#按钮盒急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police45',
          description: '5#按钮盒急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police46',
          description: '1#入库门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police47',
          description: '2#入库门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police48',
          description: '3#入库门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police49',
          description: '4#入库门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police50',
          description: '5#入库门急停触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police51',
          description: '大车左软限位触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police52',
          description: '大车右软限位触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police53',
          description: '小车前软限位触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police54',
          description: '小车后软限位触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police55',
          description: '支持上软极限触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police56',
          description: '支持下软极限触发'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police57',
          description: '大车软限位屏蔽开启'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police58',
          description: '小车软限位屏蔽开启'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police59',
          description: '支持软限位屏蔽开启'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police60',
          description: '支持编码器标定完成'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police61',
          description: '1#人员入库门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police62',
          description: '2#人员入库门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police63',
          description: '3#人员入库门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police64',
          description: '4#人员入库门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police65',
          description: '5#人员入库门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police66',
          description: '1#通道门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police67',
          description: '2#通道门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police68',
          description: '物流门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police69',
          description: '抓放料高度异常'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police70',
          description: '大车变频器通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police71',
          description: '小车变频器通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police72',
          description: '支持变频器通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police73',
          description: '与东行车通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police74',
          description: '与中控PLC通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police75',
          description: '与3#地面柜PLC通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police76',
          description: '与4#地面柜PLC通讯故障'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police77',
          description: '顶升未到位'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police78',
          description: '前端梁门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police79',
          description: '后端梁门打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police80',
          description: '人员入库'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police81',
          description: '大车制动未打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police82',
          description: '小车制动未打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police83',
          description: '支持制动未打开'
        },
        {
          tagKey: 'SlCarPlc01/PlcAlarm/police84',
          description: '夹具开闭锁状态异常'
        }
      ]
    }
  },
  created() {
    // this.DateTime = getFormatDate().split(' ')
    // this.timer = setInterval(() => {
    //   this.DateTime = getFormatDate().split(' ')
    //   // this.stoppedState()
    //   // this.CraneData()
    // }, 1000)
    // this.getStock()
    this.toStartWatch()
  },
  methods: {
    open() {
      this.dialogVisible = true
      console.log('111')
    },
    handleClose(done) {
      done()
    },
    // 倒垛接口
    InventoryScheduling() {
      LargeScreenPage.DcsInventoryScheduling({
        start: this.start,
        target: this.target
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
      })
    },
    // 处理报警的通用函数
    handleAlarm(data) {
      // 找到匹配当前TagKey的报警配置
      const config = this.alarmConfigs.find(item => item.tagKey === data.TagKey)
      if (config) {
        if (data.TagNewValue === '0') {
          // 解除报警
          this.alarmDate.forEach(item => {
            if (item.alarm_description === config.description) {
              item.alarm_status = '已解除'
            }
          })
        } else if (data.TagNewValue === '1') {
          // 触发报警
          this.alarmDate.unshift({
            time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            alarm_description: config.description,
            alarm_status: '触发中'
          })
        }
      }
    },
    EasyHandycam(Tag) {
      // 提取公共参数，减少重复代码
      const baseQuery = {
        station_code: 'P01',
        user_name: Cookies.get('userName')
      }
      // 传值给点位
      LargeScreenPage.DcsPointModificationSelect({
        ...baseQuery,
        Tag: Tag,
        Value: '1'
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
        // 延迟1秒后执行第三次接口调用（复位）
        setTimeout(() => {
          LargeScreenPage.DcsPointModificationSelect({
            ...baseQuery,
            Tag: Tag,
            Value: '0'
          })
        }, 1000) // 1000毫秒 = 0.5秒
      }).catch(() => {
        this.$message({
          message: '执行失败',
          type: 'error'
        })
      })
    },
    EasyHandycam2(Tag) {
      // 提取公共参数，减少重复代码
      const baseQuery = {
        station_code: 'P01',
        user_name: Cookies.get('userName')
      }
      // 传值给点位
      LargeScreenPage.DcsPointModificationSelect({
        ...baseQuery,
        Tag: Tag,
        Value: '1'
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
      })
    },
    EasyHandycam3(Tag) {
      // 提取公共参数，减少重复代码
      const baseQuery = {
        station_code: 'P01',
        user_name: Cookies.get('userName')
      }
      // 传值给点位
      LargeScreenPage.DcsPointModificationSelect({
        ...baseQuery,
        Tag: Tag,
        Value: '0'
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
      })
    },
    // async getStock() {
    //   DcsFmodMapMeStockSelect({
    //     user_name: Cookies.get('userName')
    //   }).then(res => {
    //     if (res.code === 0 && res.data.length > 0) {
    //       res.data.map(item => {
    //         item.type = 'stock'
    //         item.x = (item.location_x - 2800) * this.widthPx
    //         item.y = (item.location_y - 2000) * this.heightPx
    //       })
    //       res.data.push(...crownBlock)
    //       this.nodes = res.data
    //       this.toStartWatch()
    //     }
    //   })
    // },
    toStartWatch() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
          this.getScadaTagValue(result.ip, result.webapi_port)
          this.$nextTick(() => {
            this.connectMQTT(result.ip, +result.mqtt_port, (c) => {
              this.clients['SCADA_CHANGE'] = c
              this.tagKeyList.forEach(item => {
                c.subscribe(`SCADA_CHANGE/${item}`, {
                  onSuccess: () => {
                    console.debug('Subscribe success.')
                  },
                  onFailure: (responseObject) => {
                    console.error('Subscribe fail:', responseObject.errorMessage)
                  }
                })
              })
            })
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getScadaTagValue(ip, port) {
      var readTagArray = []
      for (var i = 0; i < this.tagKeyList.length; i++) {
        var readTag = {}
        readTag.tag_key = this.tagKeyList[i].toString()
        readTagArray.push(readTag)
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
      } else {
        path = 'http://' + ip + ':' + port + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var TagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value && result[k].tag_value.toString() || ''
                  if (TagKey === 'SlCarPlc01/PlcStatus/Tag023') { // 西边大车激光
                    this.westX = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag022') { // 西边小车激光
                    this.westY = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag024') { // 起升位置
                    this.westZ = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag011') { // 任务.X
                    this.taskX = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag012') { // 任务.Y
                    this.taskY = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag013') { // 任务.Z
                    this.taskZ = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag035') { // 速度.X
                    this.speedX = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag036') { // 速度.Y
                    this.speedY = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag034') { // 速度.Z
                    this.speedZ = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag032') { // 上升减速
                    this.RisingDeceleration = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag033') { // 上升到位
                    this.riseIntoPosition = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag009') { // 自动模式
                    this.AutoMode = tagValue === '1' ? '自动' : '手动'
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag028') { // 吊具顶升到位信号西北
                    this.LiftInPlace1 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag029') { // 吊具顶升到位信号西南
                    this.LiftInPlace2 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag030') { // 吊具顶升到位信号东北
                    this.LiftInPlace3 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag031') { // 吊具顶升到位信号东南
                    this.LiftInPlace4 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag038') { // 吊具开锁到位
                    this.LiftInPlace5 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag039') { // 吊具闭锁到位
                    this.LiftInPlace6 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag037') { // 上电就绪
                    this.LiftInPlace7 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag016') { // 执行任务执行中
                    this.LiftInPlace8 = tagValue
                  }
                  // 调用通用报警处理函数
                  this.handleAlarm({ TagKey: TagKey, TagNewValue: tagValue })
                }
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    connectMQTT(host, port, onConnected) {
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        // this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          // this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        const topic = message.destinationName
        const payload = message.payloadString
        const data = JSON.parse(payload)
        if (data && data.TagNewValue && data.TagNewValue !== '') {
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag023') {
            this.westX = data.TagNewValue // 西边大车激光
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag022') {
            this.westY = data.TagNewValue // 西边小车激光
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag024') {
            this.westZ = data.TagNewValue // 起升位置
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag011') {
            this.taskX = data.TagNewValue // 任务.X
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag012') {
            this.taskY = data.TagNewValue // 任务.Y
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag013') {
            this.taskZ = data.TagNewValue // 任务.Z
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag035') {
            this.speedX = data.TagNewValue // 速度.X
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag036') {
            this.speedY = data.TagNewValue // 速度.Y
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag034') {
            this.speedZ = data.TagNewValue // 速度.Z
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag032') {
            this.RisingDeceleration = data.TagNewValue // 上升减速
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag033') {
            this.riseIntoPosition = data.TagNewValue // 上升到位
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag009') {
            this.AutoMode = data.TagNewValue === '1' ? '自动' : '手动'// 自动模式
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag028') {
            this.LiftInPlace1 = data.TagNewValue // 吊具顶升到位信号西北
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag029') {
            this.LiftInPlace2 = data.TagNewValue // 吊具顶升到位信号西南
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag030') {
            this.LiftInPlace3 = data.TagNewValue // 吊具顶升到位信号东北
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag031') {
            this.LiftInPlace4 = data.TagNewValue // 吊具顶升到位信号东南
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag038') { // 吊具开锁到位
            this.LiftInPlace5 = data.TagNewValue
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag039') { // 吊具闭锁到位
            this.LiftInPlace6 = data.TagNewValue
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag037') { // 上电就绪
            this.LiftInPlace7 = data.TagNewValue
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag016') { // 执行任务执行中
            this.LiftInPlace8 = data.TagNewValue
          }
          // 调用报警通用处理函数
          this.handleAlarm(data)
        } else {
          console.error('Invalid data:', payload)
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
  }

}
</script>
<style scoped lang="less">
::v-deep .el-dialog{
  margin-top: 5vh !important;
}
 .header{
    display: flex;
    align-items: center;
    margin: 10px 0;
    color: #ffffff;
    font-size: 20px;
    font-family: YouSheBiaoTiHei;
    color: #00ffff;
 }
 .my-label {
    background: #E1F3D8;
  }
  .my-content {
    background: #FDE2E2;
  }
  .car-info{
    display: flex;
    justify-content: space-between;
    .left{
        width: 57%;
    }
    .right{
        width: 42%;
    }
    ::v-deep .my-label{
        background-color: #031850 !important;
        color: #00ffff !important;
        border: 1px solid #00ffff!important;
        font-size: 14px;
        text-align: center;
    }
    ::v-deep .my-content{
        border: 1px solid #00ffff!important;
        background-color: #072578 !important;
        color: #21b1ee!important;
        text-align: center;
        font-size: 14px;
    }
    ::v-deep .el-radio__inner{
        width: 16px;
        height: 16px;
    }
  }
  .sports-position-info{
    display: flex;
    justify-content: space-between;
    .sports-info{
        width: 33%;
    }
    .position-info{
        width: 33%;
    }
    .control-info{
        width: 33%;
        .mode{
          color: #00ffff;
          font-size: 16px;
          display: flex;
          align-items: center;
          ::v-deep .el-button{
            border: 2px solid #57d6f6;
            background-color: #084aa0;
            color: #00ffff;
          }
        }
        .input-info{
          margin-top: 10px;
          span{
            color: #00ffff;
            font-size: 16px;
          }
          ::v-deep .el-input{
            width: 33% !important;
            height: 30px;
            .el-input__inner{
              border: 2px solid #57d6f6;
              background-color: #072578;
              color: #57d6f6;
              font-size: 16px;
            }
          }
        }
        .operation{
          margin-top: 10px;
          display: flex;
          align-items: center;
          span{
            color: #00ffff;
            font-size: 16px;
          }
          ::v-deep .el-button{
            border: 2px solid #57d6f6;
            background-color: #084aa0;
            color: #00ffff;
          }
        }
        .manual-stack{
          margin-top: 10px;
          display: flex;
          align-items: center;
          span{
            color: #00ffff;
            font-size: 16px;
          }
          .svg-icon{
            font-size: 20px;
            margin: 0 5px;
          }
          ::v-deep .el-input{
            width: 30% !important;
            height: 30px;
            .el-input__inner{
              border: 2px solid #57d6f6;
              background-color: #072578;
              color: #57d6f6;
              font-size: 16px;
            }
          }
          ::v-deep .el-button{
            border: 2px solid #57d6f6;
            background-color: #084aa0;
            color: #00ffff;
            margin-left: 5px;
          }
        }
    }
  }
  .table-wrapper{
    table {
        width: 100%;
        table-layout: auto;
        thead{
        th{
            white-space: nowrap;
        }
    }
    }
    tr{
        height: 25px;
    }
    th,td{
        font-size: 18px;
        text-align: center;
        ::v-deep .el-radio__inner{
            width: 16px;
            height: 16px;
            margin-left: 5px;
        }
    }
    th{
        color: #00ffff;
    }
    td{
        color: #fff;
    }
  }
  .footer{
    .control-btn{
      padding: 20px;
      width: 28%;
      background-color: #1b5dae;
      margin: 0 auto;
      border-radius: 3%;
      box-shadow: 0 0 30px rgba(0, 0, 0, 0.2);
      .el-button{
          font-size: 20px;
          margin: 5px 0;
      }
      .A{
          background: #346250;
      }
      .B{
          background: #ff0000;
      }
      .C,.D,.F,.H,.E,.G{
          border: 2px solid #57d6f6;
          background-color: #084aa0;
      }
      .E,.G{
          color: #ff0000;
      }
      .D,.E,.F,.G{
          margin-left: 20px;
      }
    }
  }
.light-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.light {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  border: 4px solid #ffffff;
}

.blue-light {
  background-color: #1eff00;
  box-shadow: 0 0 20px rgba(54, 255, 4, 0.8);
}

.gray-light {
  background-color: #084aa0;
  box-shadow: 0 0 10px rgb(29, 19, 167);
}
</style>

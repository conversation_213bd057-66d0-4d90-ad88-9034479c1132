// 核心模块的繁體中文翻譯

export default {
  // 這裡可以添加核心模塊特定的翻譯
  core: {
    // SECS/AIS配方管理
    secsais: {
      maintenance: 'SECS/AIS配方管理',
      downRecipeSuccess: '配方下發成功！',
      downRecipeAndSyncSuccess: '配方下發成功，同步到其他設備成功！',
      recipeSyncFailed: '配方同步失敗',
      modifyParameter: '是否修改參數',
      parameterCode: '參數編碼',
      parameterDes: '參數描述',
      parameterVal: '參數值',
      parameterInput: '輸入值',
      parameterUnit: '單位',
      parameterLimit: '上下限',
      noRecipeSelected: '未選擇配方或配方詳情加載中...',
      fetchRecipeDataFailed: '獲取配方數據失敗',
      valueTooLow: '輸入值低於下限',
      valueTooHigh: '輸入值高於上限',
      mqttNotConnected: '請先連接MQTT服務',
      modifyFailed: '修改失敗',
      modifySuccess: '修改成功',
      serviceCell: '無法獲取單元服務器信息',
      mqttConnectSuccess: 'MQTT連接成功',
      mqttConnectionFailed: 'MQTT連接失敗',
      getCellIPFailed: '獲取單元IP失敗',
      clientDes: '客戶端描述',
      clientCode: '客戶端編碼',
      search: '搜索',
      reset: '重置',
      clientList: '實例列表',
      refreshInstanceList: '刷新實例列表',
      online: '在線',
      offline: '離線',
      modelList: '配方列表',
      recipeDetails: '配方詳情',
      stationCodeMissing: '工站編碼缺失，無法查詢配方模型數據',
      fetchModelDataFailed: '獲取配方模型數據失敗',
      refreshingInstanceList: '正在刷新實例列表...',
      confirmDelete: '確認刪除選中的{0}條數據?',
      prompt: '提示',
      confirm: '確認',
      cancel: '取消'
    },

    // EAP配方管理
    recipe: {
      recipeDes: '配方描述',
      recipeName: '配方名稱',
      versionNo: '版本號',
      recipeType: '配方類型',
      enableFlag: '有效標識',
      deviceCode: '設備編碼',
      deviceDes: '設備描述',
      materialCode: '物料編碼',
      materialDes: '物料描述',
      isValid: '是否有效',
      operation: '操作',
      recipeTypeRequired: '請選擇配方類型',
      versionNoRequired: '請填寫版本號',
      recipeDesRequired: '請填寫配方描述',
      recipeNameRequired: '請填寫配方名稱',
      materialCodeRequired: '請填寫物料編碼',
      materialDesRequired: '請填寫物料描述',
      deviceCodeRequired: '請填寫設備編碼',
      deviceDesRequired: '請填寫設備描述',
      enableFlagRequired: '請選擇有效標識',
      copyRecipe: '複製配方',
      exportRecipe: '導出配方',
      downloadTemplate: '下載模板',
      importRecipe: '導入配方',
      batchDelete: '批量刪除',
      copyRecipeName: '複製配方名稱',
      selectRecipeToExport: '請選擇要導出的配方',
      selectOneRecipeToExport: '請選擇一條配方記錄進行導出',
      selectOneRecipeToCopy: '請選擇一條配方記錄進行複製',
      exportSuccess: '配方導出成功',
      exportFailed: '導出配方失敗，請重試',
      templateDownloadSuccess: '模板下載成功',
      templateDownloadFailed: '下載模板失敗，請重試',
      importSuccess: '配方導入成功',
      importFailed: '導入配方失敗，請檢查文件格式和內容',
      onlyXlsSupported: '僅支持 .xls 文件格式',
      confirmBatchDelete: '確認刪除選中的',
      dataItems: '條數據?',
      unit: '單位',
      minValue: '最小值',
      maxValue: '最大值',
      currentValue: '當前值',
      noLimit: '不限制'
    },

    // SCADA數據變化報表
    scadaReport: {
      scadaTagList: 'SCADA 標籤列表',
      searchTag: '搜索標籤...',
      instance: '實例',
      tagGroup: '標籤組',
      tag: '標籤',
      currentSelectedTag: '當前選擇標籤：',
      tagMissingUnitInfo: '該標籤缺少單元信息，請聯繫管理員',
      pleaseSelectTagFirst: '請先從左側列表選擇一個標籤'
    }
  }
}

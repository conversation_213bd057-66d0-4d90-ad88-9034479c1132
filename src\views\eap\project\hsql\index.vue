<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard header">
      <el-header>
        <div class="statuHead">
          <div>
            <el-popover placement="right" width="170" trigger="click">
              <el-button
                v-for="(item, index) in [
                  {
                    tag_key: 'PmPlc/PcStatus/Remote',
                    tag_value: '0',
                    label: '离线模式',
                  },
                  {
                    tag_key: 'PmPlc/PcStatus/Remote',
                    tag_value: '1',
                    label: '在线模式',
                  }
                ]"
                :key="index"
                size="medium"
                type="primary"
                style="font-size: 20px"
                :style="{ margin: index === 1 ? '10px 0' : '0px' }"
                @click="handleWrite(item.tag_key, item.tag_value)"
                >{{ item.label }}</el-button
              ><br />
              <el-button
                slot="reference"
                :class="
                  monitorData.ControlMode.value === '2'
                    ? 'btnone'
                    : 'btnone0'
                "
                >{{
                  controlData[monitorData.ControlMode.value] || "离线模式"
                }}</el-button
              >
            </el-popover>
          </div>
          <div>
            <div class="wrappstyle">
              <p>
                <span
                  :class="
                    monitorData.LightGreen.value === '1'
                      ? 'wholeline1 wholelinenormal1'
                      : monitorData.LightYellow.value === '1'
                      ? 'wholeline1 wholelineerror1'
                      : monitorData.LightRed.value === '1'
                      ? 'wholeline1 deviceRed'
                      : 'wholeline1 wholelinegray1'
                  "
                />
                <span class="statuText">{{
                  $t("lang_pack.vie.triColorLight")
                }}</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.PlcHeartBeat.value === '1'
                      ? 'wholeline wholelinenormal'
                      : monitorData.PlcHeartBeat.value === '0'
                      ? 'wholeline wholelineerror'
                      : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">PLC</span>
              </p>
            </div>
          </div>
        </div>
      </el-header>
    </el-card>
    <el-row
      :gutter="20"
      style="margin-right: 0px; padding: 0px; margin-top: 10px"
    >
      <el-col :span="16" style="padding-right: 0">
        <el-col :span="24" style="padding: 0">
          <!-- <el-col :span="12" style="padding: 0 5px 0 0;"> -->
          <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="80px">
              <div class="wrapElForm">
                <!-- 料号输入框 -->
                <div class="wrapElFormFirst col-md-3 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="料号：">
                      <el-input
                        ref="partNo"
                        v-model="partNo"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>

                <div class="wrapElFormFirst col-md-3 col-12">
                  <div class="formChild col-md-12 col-12 barcode">
                    <el-form-item label="工单批号：">
                      <el-input
                        ref="lot_no"
                        v-model="lot_no"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>

                <div class="wrapElFormFirst col-md-3 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="员工号：">
                      <el-input
                        ref="empId"
                        v-model="empId"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>

                <!-- 上机按钮与工单、员工号同一行 -->
                <div class="wrapElFormFirst col-md-3 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label=" ">
                      <el-button
                        type="primary"
                        style="width: 100%; height: 32px; font-size: 14px"
                        :loading="isStartingProduction"
                        :disabled="isStartingProduction || monitorData.RecipeUpd.value === '0'"
                        @click="startProduction()">
                        {{ isStartingProduction ? '上机中...' : (monitorData.RecipeUpd.value === '0' ? '设备生产中无法上机' : '上机') }}
                      </el-button>
                    </el-form-item>
                  </div>
                </div>

                <!-- 显示返回数据的文字标签 -->
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="料号：">
                      <span class="production-data-text">{{ productionData.PartNum || '-' }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="数量：">
                      <span class="production-data-text">{{ productionData.Qnty || '-' }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="配方名称：">
                      <span class="production-data-text">{{ productionData.RecipeName || '-' }}</span>
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="面次：">
                      <span class="production-data-text">{{ productionData.MC || '-' }}</span>
                    </el-form-item>
                  </div>
                </div>
              </div>
            </el-form>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>重要生产信息</span>
            </div>
            <el-table ref="table" border :data="productData" :height="height">
              <!-- 基础信息 -->
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="LotID"
                label="批号"
                width="120"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="PartNo"
                label="料号"
                width="120"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="PanelID"
                label="板号"
                width="100"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                align="center"
                prop="RecipeCode"
                label="配方名称"
                width="120"
              />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras86" label="塞孔1真空度设定" width="150" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras116" label="塞孔2真空度设定" width="150" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras76" label="塞孔1塞孔速度" width="150" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras106" label="塞孔2塞孔速度" width="150" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras79" label="塞孔1塞头压力" width="150" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras109" label="塞孔2塞头压力" width="150" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras80" label="塞孔1前墨桶压力" width="180" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras110" label="塞孔2前墨桶压力" width="180" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras97" label="刮平速度" width="120" />
              <el-table-column :show-overflow-tooltip="true" align="center" prop="Paras100" label="刮平压力" width="120" />
            </el-table>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <el-card shadow="never" class="wrapCard">
              <div slot="header" class="wrapTextSelect">
                <span>报警信息</span>
              </div>
              <el-table
                ref="table"
                border
                :data="alarmData"
                :row-key="(row) => row.id"
                height="200"
              >
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="station_code"
                  label="工位"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_code"
                  label="实例编号"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_des"
                  label="实例描述"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_code"
                  label="报警代码"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_level"
                  label="报警级别"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_des"
                  label="报警描述"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="item_date"
                  label="报警时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_date"
                  label="复位时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_flag"
                  label="是否复位"
                >
                  <template slot-scope="scope">
                    {{ scope.row.reset_flag === "Y" ? "已复位" : "待复位" }}
                  </template>
                </el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="simulated_flag"
                  label="是否模拟"
                >
                  <template slot-scope="scope">
                    {{ scope.row.simulated_flag === "Y" ? "是" : "否" }}
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-card></el-col
        >
      </el-col>
      <el-col :span="8" style="padding-right: 0">
        <el-card shadow="never" class="wrapCard">
          <div class="pieChart" v-show="false">
            <div id="capacityDom" />
            <!-- 产能 -->
            <div id="oeeDom" />
            <!-- oee -->
            <div id="readbitRateDom" />
            <!-- 读码率 -->
          </div>
          <!-- 生产监控项 -->
          <div class="production-monitoring">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="monitoring-item">
                  <span class="monitoring-label">任务总数:</span>
                  <span class="monitoring-value">{{ monitorData.TaskCount.value || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="monitoring-item">
                  <span class="monitoring-label">手动放行数量:</span>
                  <span class="monitoring-value">{{ monitorData.SkipCheckCount.value || 0 }}</span>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="monitoring-item">
                  <span class="monitoring-label">入板数量:</span>
                  <span class="monitoring-value">{{ monitorData.PanelOutNo.value || 0 }}</span>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="monitoring-item">
                  <span class="monitoring-label">剩余数量:</span>
                  <span class="monitoring-value">{{ remainingPieces }}</span>
                </div>
              </el-col>
            </el-row>
          </div>
          <el-table
            border
            :data="plcCraftData"
            :row-key="(row) => row.id"
            :height="gatherHeight"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              align="left"
              label="参数名称"
              prop="tag_des"
              min-width="150px"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="tag_value"
              label="值"
              min-width="120px"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- NG原因弹窗 -->
    <el-dialog
      title="NG原因维护"
      :visible.sync="ngReasonDialogVisible"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="closeNGReasonDialog">
      <el-form :model="ngReasonForm" label-width="120px" size="small">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="工单批号:">
              <el-input v-model="ngReasonForm.LotNum" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="剩余片数:">
              <el-input v-model="ngReasonForm.RemainingPieces" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="读码信息:">
              <el-input v-model="ngReasonForm.ReadInfo" readonly></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="读码面次:">
              <el-input v-model="ngReasonForm.ReadMC" readonly></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="时间:">
          <el-input v-model="ngReasonForm.Time" readonly></el-input>
        </el-form-item>

        <el-form-item label="错误信息:">
          <el-input
            v-model="ngReasonForm.Msg"
            type="textarea"
            :rows="3"
            readonly>
          </el-input>
        </el-form-item>

        <el-form-item label="员工号:" required>
          <el-input
            v-model="ngReasonForm.EmpId"
            placeholder="请输入员工号"
            maxlength="20"
            clearable>
          </el-input>
        </el-form-item>

        <el-form-item label="NG原因:" required>
          <el-input
            v-model="ngReasonForm.NGReason"
            type="textarea"
            :rows="3"
            placeholder="请填写NG原因"
            maxlength="500"
            show-word-limit>
          </el-input>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="closeNGReasonDialog">取消</el-button>
        <el-button
          type="primary"
          :loading="isSubmittingNGReason"
          :disabled="isSubmittingNGReason"
          @click="submitNGReason">
          {{ isSubmittingNGReason ? '提交中...' : '手動放行' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { sel as eapRecipeSel } from '@/api/eap/project/sfcom/eapRecipe'
import { startProduction, ngReasonSaved } from '@/api/eap/project/hsql/index'
import { selScadaTag } from '@/api/core/scada/tag'
import crudEapRecipeDetail, { sel as eapRecipeDetailSel} from '@/api/eap/project/sfcom/eapRecipeDetail'
import { scadaTagGroupTree } from '@/api/core/scada/tagGroup'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import axios from 'axios'
import { selCellIP } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { mapGetters } from 'vuex'
const defaultForm = {};
export default {
  name: "shRecipeMain",
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 608,
      gatherHeight: document.documentElement.clientHeight-70,
      lot_no: "",
      empId: "",
      partNo: "", // 新增料号输入框
      isStartingProduction: false, // 控制上机按钮的加载状态
      productionData: {
        PartNum: "",
        Qnty: "",
        RecipeName: "",
        MC: ""
      },
      // NG原因弹窗相关数据
      ngReasonDialogVisible: false,
      ngReasonForm: {
        LotNum: "",
        RemainingPieces: "",
        ReadInfo: "",
        ReadMC: "",
        Time: "",
        Msg: "",
        CcdCode: "",
        EmpId: "",
        NGReason: ""
      },
      isSubmittingNGReason: false, // 控制提交按钮的加载状态
      // CcdCode错误类型映射
      ccdCodeTypes: {
        1: 'OK',
        2: '读码超时或NG',
        3: '基板混料',
        4: '系统无数据下发'
      },
      recipeData: [],
      capacityDom: null,
      oeeDom: null,
      readbitRateDom: null,
      capacityOption: {
        title: {
          show: true,
          text: "100%",
          itemGap: 10,
          x: "center",
          y: "30%",
          subtext: "产能",
          textStyle: {
            fontSize: 24,
            color: "#999999",
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: "bold",
            color: "#333333",
          },
        },
        color: ["#6df320", "#d2e312"],
        tooltip: {
          backgroundColor: "#fff",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);",
          textStyle: {
            color: "#000",
          },
        },
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            radius: ["60%", "90%"],
            center: ["50%", "40%"],
            label: {
              // 鼠标悬浮具体数据显示
              show: false,
            },
            data: [
              { value: 335, name: "脱岗" },
              { value: 234, name: "在岗" },
            ],
          },
        ],
      },
      oeeOption: {
        title: {
          show: true,
          text: "100%",
          itemGap: 10,
          x: "center",
          y: "30%",
          subtext: "OEE",
          textStyle: {
            fontSize: 24,
            color: "#999999",
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: "bold",
            color: "#333333",
          },
        },
        color: ["#409EFF", "#40e2ff"],
        tooltip: {
          backgroundColor: "#fff",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);",
          textStyle: {
            color: "#000",
          },
        },
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            radius: ["60%", "90%"],
            center: ["50%", "40%"],
            label: {
              // 鼠标悬浮具体数据显示
              show: false,
            },
            data: [
              { value: 335, name: "脱岗" },
              { value: 234, name: "在岗" },
            ],
          },
        ],
      },
      readbitRateOption: {
        title: {
          show: true,
          text: "100%",
          itemGap: 10,
          x: "center",
          y: "30%",
          subtext: "读码率",
          textStyle: {
            fontSize: 24,
            color: "#999999",
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: "bold",
            color: "#333333",
          },
        },
        color: ["#9d9727", "#c25b1f"],
        tooltip: {
          backgroundColor: "#fff",
          extraCssText: "box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);",
          textStyle: {
            color: "#000",
          },
        },
        grid: {
          top: "0%",
          left: "0%",
          right: "0%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            type: "pie",
            radius: ["60%", "90%"],
            center: ["50%", "40%"],
            label: {
              // 鼠标悬浮具体数据显示
              show: false,
            },
            data: [
              { value: 335, name: "脱岗" },
              { value: 234, name: "在岗" },
            ],
          },
        ],
      },

      controlStatus: {
        light_status: "",
        device_plc_status: "",
        ais_status: "0",
        plc_status: "0",
        eap_status: "0",
      },
      controlData: {
        1: "离线模式",
        2: "在线模式"
      },
      monitorData: {
        PlcHeartBeat: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "PlcHeartBeat",
          tag_des: "[PlcStatus]PLC心跳",
          value: "0",
        },
        LightGreen: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "LightGreen",
          tag_des: "[PlcStatus]三色灯绿",
          value: "0",
        },
        LightYellow: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "LightYellow",
          tag_des: "[PlcStatus]三色灯黄",
          value: "0",
        },
        LightRed: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "LightRed",
          tag_des: "[PlcStatus]三色灯红",
          value: "0",
        },
        ControlMode: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "ControlMode",
          tag_des: "[PlcStatus]设备控制状态(1:离线,2:在线",
          value: "0",
        },
        RecipeUpd: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "RecipeUpd",
          tag_des: "[PlcStatus]配方修改",
          value: "0",
        },
        //批号
        LotID: {
          client_code: "PmPlc",
          group_code: "PlcCraft",
          tag_code: "LotID",
          tag_des: "[PlcCraft]批次号",
          value: "",
        },
        //料号
        PartNo: {
          client_code: "PmPlc",
          group_code: "PlcCraft",
          tag_code: "PartNo",
          tag_des: "[PlcCraft]料号",
          value: "",
        }
        //员工号
        ,CurrentUser: {
          client_code: "PmAis",
          group_code: "AisStatus",
          tag_code: "CurrentUser",
          tag_des: "[AisStatus]员工号",
          value: ""
        }//配方名称
        ,RecipeCode: {
          client_code: "PmPlc",
          group_code: "PlcCraft",
          tag_code: "RecipeCode",
          tag_des: "[PlcCraft]配方名称",
          value: ""
        }
        //生产下发数量
        ,TaskCount: {
          client_code: "PmPlc",
          group_code: "PcStatus",
          tag_code: "TaskCount",
          tag_des: "[PcStatus]生产下发数量",
          value: ""
        }//面次
        ,TaskType: {
          client_code: "PmAis",
          group_code: "AisStatus",
          tag_code: "TaskType",
          tag_des: "[PcStatus]面次",
          value: ""
        }
        //手动放行数量
        ,SkipCheckCount: {
          client_code: "PmAis",
          group_code: "AisStatus",
          tag_code: "SkipCheckCount",
          tag_des: "[AisStatus]手动放行数量",
          value: ""
        }
        //入板数量
        ,PanelOutNo: {
          client_code: "PmPlc",
          group_code: "PlcStatus",
          tag_code: "PanelOutNo",
          tag_des: "[PlcStatus]入板数量",
          value: ""
        }
      },
      cellIp: "", // 单元IP
      webapiPort: "", // 单元API端口号
      mqttPort: "", // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: "/mqtt",
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          "ScadaEsb_" +
          Cookies.get("userName") +
          "_" +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false,
      },
      alarmData: [],
      groupData: [],
      productObj: {},
      productData: [],
      ArrTag: [
        "LotID",        // 批号
        "PartNo",       // 料号
        "PanelID",      // 板号
        "RecipeCode",   // 配方名称
        "Paras1",       // 塞孔1加热开关
        "Paras2",       // 塞孔2加热开关
        "Paras3",       // 内外置供墨
        "Paras4",       // 大气真空模式
        "Paras5",       // 自动涂墨
        "Paras6",       // 单双面涂墨
        "Paras7",       // 联机
        "Paras8",       // EIP启用
        "Paras9",       // 照明开关
        "Paras10",      // 多段压力
        "Paras11",      // 油墨检查
        "Paras12",      // 仓温卡控开关
        "Paras13",      // 软硬板模式
        "Paras14",      // 初始化运行中
        "Paras15",      // 初始化完成
        "Paras16",      // 静音模式
        "Paras17",      // 总手自动
        "Paras18",      // 设备总报警中
        "Paras19",      // 设备总运行中
        "Paras20",      // 联机运行中
        "Paras21",      // 搬料总报警中
        "Paras22",      // 外部急停中
        "Paras23",      // 上下料急停中
        "Paras24",      // 总急停中
        "Paras25",      // 外围移栽光幕遮挡中
        "Paras26",      // 检板功能
        "Paras27",      // 塞1手自动
        "Paras28",      // 塞孔1急停中
        "Paras29",      // 塞孔1总运行中
        "Paras30",      // 塞孔1总忙碌中
        "Paras31",      // 塞孔1光幕遮挡中
        "Paras32",      // 塞孔1总报警中
        "Paras33",      // 塞孔1仓有板
        "Paras34",      // 脱泡总报警中
        "Paras35",      // 脱泡总运行中
        "Paras36",      // 塞头供墨_Busy
        "Paras37",      // 脱泡总忙碌中
        "Paras38",      // 脱泡破真空中
        "Paras39",      // A桶已脱泡标志
        "Paras40",      // B桶已脱泡标志
        "Paras41",      // 塞2手自动
        "Paras42",      // 塞孔2急停中
        "Paras43",      // 塞孔2总运行中
        "Paras44",      // 塞孔2总忙碌中
        "Paras45",      // 塞孔2光幕遮挡中
        "Paras46",      // 塞孔2总报警中
        "Paras47",      // 塞孔2仓有板
        "Paras48",      // 刮平手自动
        "Paras49",      // 刮平急停中
        "Paras50",      // 刮平总运行中
        "Paras51",      // 刮平总忙碌中
        "Paras52",      // 刮平光幕遮挡中
        "Paras53",      // 刮平总报警中
        "Paras54",      // 刮平仓有板
        "Paras55",      // 塞孔1自动流程
        "Paras56",      // 塞孔1塞孔次数设定
        "Paras57",      // 塞孔1预挤墨时间
        "Paras58",      // 脱泡自动流程
        "Paras59",      // 脱泡破真空流程
        "Paras60",      // 脱泡静刀时间
        "Paras61",      // 油墨A桶状态
        "Paras62",      // 油墨B桶状态
        "Paras63",      // 塞孔2自动流程
        "Paras64",      // 塞孔2塞孔次数设定
        "Paras65",      // 塞孔2预挤墨时间
        "Paras66",      // 刮平自动流程
        "Paras67",      // 刮平刮印次数设定
        "Paras68",      // 预涂墨设定时间
        "Paras69",      // 联机日产量
        "Paras70",      // 联机总产量
        "Paras71",      // 全自动一片周期CT
        "Paras72",      // 板宽
        "Paras73",      // 板长
        "Paras74",      // 塞孔1塞孔起点
        "Paras75",      // 塞孔1塞孔终点
        "Paras76",      // 塞孔1塞孔速度
        "Paras77",      // 塞孔1塞孔上行速度
        "Paras78",      // 塞孔1塞孔下行距离
        "Paras79",      // 塞孔1塞头压力
        "Paras80",      // 塞孔1前墨桶压力
        "Paras81",      // 塞孔1后墨桶压力
        "Paras82",      // 塞孔1前塞头温度
        "Paras83",      // 塞孔1后塞头温度
        "Paras84",      // 塞孔1前塞头当前温度
        "Paras85",      // 塞孔1后塞头当前温度
        "Paras86",      // 塞孔1真空度设定
        "Paras87",      // 塞孔1当前真空值
        "Paras88",      // 塞孔1运行CT
        "Paras89",      // 塞孔1日产量
        "Paras90",      // 塞孔1总产量
        "Paras91",      // 塞孔1挤墨扭矩设定
        "Paras92",      // 搅拌时间
        "Paras93",      // 搅拌旋转圈数
        "Paras94",      // 挤墨表层压力设定
        "Paras95",      // 刮平起点
        "Paras96",      // 刮平终点
        "Paras97",      // 刮平速度
        "Paras98",      // 刮平上行速度
        "Paras99",      // 刮完下行距离
        "Paras100",     // 刮平压力
        "Paras101",     // 刮平运行CT
        "Paras102",     // 刮平日产量
        "Paras103",     // 刮平总产量
        "Paras104",     // 塞孔2塞孔起点
        "Paras105",     // 塞孔2塞孔终点
        "Paras106",     // 塞孔2塞孔速度
        "Paras107",     // 塞孔2塞孔上行速度
        "Paras108",     // 塞孔2塞孔下行距离
        "Paras109",     // 塞孔2塞头压力
        "Paras110",     // 塞孔2前墨桶压力
        "Paras111",     // 塞孔2后墨桶压力
        "Paras112",     // 塞孔2前塞头温度
        "Paras113",     // 塞孔2后塞头温度
        "Paras114",     // 塞孔2前塞头当前温度
        "Paras115",     // 塞孔2后塞头当前温度
        "Paras116",     // 塞孔2真空度设定
        "Paras117",     // 塞孔2当前真空值
        "Paras118",     // 塞孔2运行CT
        "Paras119",     // 塞孔2日产量
        "Paras120",     // 塞孔2总产量
        "Paras121",     // 塞孔2挤墨扭矩设定
        "Paras122",     // 脱泡活塞剩余深度
        "Paras123",     // 塞孔1仓当前温度
        "Paras124",     // 塞孔2仓当前温度
        "Paras125",     // 塞孔1多段压力分界点(起终点之间)
        "Paras126",     // 塞孔1第1段塞头压力
        "Paras127",     // 塞孔1第1段供墨压力
        "Paras128",     // 塞孔1印刷1段速
        "Paras129",     // 塞孔1第2段塞头压力
        "Paras130",     // 塞孔1第2段供墨压力
        "Paras131",     // 塞孔1印刷2段速
        "Paras132",     // 塞孔2多段压力分界点(起终点之间)
        "Paras133",     // 塞孔2第1段塞头压力
        "Paras134",     // 塞孔2第1段供墨压力
        "Paras135",     // 塞孔2印刷1段速
        "Paras136",     // 塞孔2第2段塞头压力
        "Paras137",     // 塞孔2第2段供墨压力
        "Paras138",     // 塞孔2印刷2段速
        "Paras139",     // 刮平墨量预警设定值
        "Paras140",     // 刮平墨量当前值
        "Paras141",     // 仓温卡控设定值
      ],
      // ArrTag2: ['RecipeCode', 'Paras191', 'Paras192', 'Paras197', 'Paras198', 'Paras200'],
      // plcCraftObj: {},
      plcCraftData: [],
    };
  },
  cruds() {
    return CRUD({
      title: "配方维护",
      // 登录用户
      userName: Cookies.get("userName"),
      // 唯一字段
      idField: "recipe_detail_id ",
      // 排序
      sort: ["recipe_detail_id asc"],
      // CRUD Method
      crudMethod: { ...crudEapRecipeDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true,
      }
    });
  },
  dicts: ["PROJECT_PARAMS_WARNING"],
  computed: {
    ...mapGetters(['user']),
    // 计算剩余数量
    remainingPieces() {
      const taskCount = parseInt(this.monitorData.TaskCount.value) || 0;
      const skipCheckCount = parseInt(this.monitorData.SkipCheckCount.value) || 0;
      const panelOutNo = parseInt(this.monitorData.PanelOutNo.value) || 0;
      return taskCount + skipCheckCount - panelOutNo;
    }
  },
  mounted: function () {
    const query = {
      client_id: 1010,
      enable_flag: "Y",
      sort: "tag_group_id",
      user_name: Cookies.get("userName"),
    };
    scadaTagGroupTree(query).then((res) => {
      if (res.data.length > 0) {
        const Arr = ["101003", "101004"];
        Arr.forEach((temp) => {
          const data = res.data.find((item) => item.tag_group_id === temp);
          if (data && data.children.length > 0) {
            data.children.forEach((e) => {
              this.groupData.push({
                tag_key: `PmPlc/${e.tag_group_code}/${e.tag_code}`,
                value: "",
              });
            });
          }
        });
      }
    });

    // 等待字典加载完成后再初始化参数表格数据
    this.$nextTick(() => {
      this.initPlcCraftData();
    });
    const that = this;
    this.timer = setInterval(this.getAlarmData, 15000);
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 608;
      that.gatherHeight = document.documentElement.clientHeight - 424;
    };
    this.$nextTick(() => {
      this.getCapacity();
      this.getOee();
      this.getreadbitRate();
    });
 },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  created() {
    this.getCellIp();
  },
  methods: {
    // 初始化参数表格数据
    initPlcCraftData() {
      // 注释掉字典检查逻辑
      // if (!this.dict || !this.dict.PROJECT_PARAMS_WARNING) {
      //   console.warn('字典 PROJECT_PARAMS_WARNING 未加载，延迟初始化');
      //   setTimeout(() => {
      //     this.initPlcCraftData();
      //   }, 1000);
      //   return;
      // }

      const params = {
        tableOrder: "asc",
        tableOrderField: "opc_demo_addr,tag_id",
        tablePage: 1,
        tableSize: 1000,
        tag_group_id: 101003,
        user_name: Cookies.get("userName"),
      };

      selScadaTag(params).then((res) => {
        if (res.code === 0) {
          // 清空现有数据
          this.plcCraftData = [];
          res.data.forEach((item) => {
                // 注释掉字典过滤逻辑，直接添加所有数据
                const result = {
                  tag_des: item.tag_des,
                  tag_key: `PmPlc/${item.tag_attr}/${item.tag_code}`,
                  tag_value: "",
                  unit: item.opc_addr,
                  down_limit: item.down_limit,
                  upper_limit: item.upper_limit,
                  status: "",
                };
                this.plcCraftData.push(result);
          });
          console.log('参数表格数据初始化完成，共', this.plcCraftData.length, '条数据');
        }
      }).catch((error) => {
        console.error('初始化参数表格数据失败:', error);
      });
    },

    startProduction() {
      if (!this.partNo) {
        this.$message({ type: "warning", message: "请先输入料号" });
        return;
      }
      if (!this.lot_no) {
        this.$message({ type: "warning", message: "请先输入工单" });
        return;
      }
      if (!this.empId) {
        this.$message({ type: "warning", message: "请先输入员工号" });
        return;
      }

      // 检查RecipeUpd状态
      if (this.monitorData.RecipeUpd.value === '0') {
        this.$message({ type: "warning", message: "生产无法上机，设备状态不允许" });
        return;
      }

      // 防止重复点击
      if (this.isStartingProduction) {
        return;
      }

      // 开始加载状态
      this.isStartingProduction = true;

      // 先使用料号作为配方名称查询本地配方是否存在
      const recipeQuery = {
        page: 1,
        size: 1000,
        sort: 'recipe_id asc',
        enable_flag: 'Y',
        recipe_name_eq: this.partNo // 使用料号作为配方名称
      };

      eapRecipeSel(recipeQuery)
        .then((recipeRes) => {
          if (recipeRes.code === 0 && recipeRes.data && recipeRes.data.length > 0) {
            // 本地配方存在，继续调用startProduction接口
            const requestData = {
              EmpID: this.empId,
              LotNum: this.lot_no,
              InputPartNum: this.partNo, // 添加料号参数
              StationCode: this.$route.query.station_code
            };

            // 调用StartProduction接口
            startProduction(requestData)
              .then((res) => {
          console.log('StartProduction接口返回:', res);
          if (res.code === 0 && res.data && res.data.length > 0) {
            const data = res.data[0];
            console.log('获取到的生产数据:', data);

            // 在调用上机接口之前，先查询本地配方名称是否存在
            if (!data.RecipeName) {
              this.$message({ type: "error", message: "配方名称为空，无法上机"});
              this.isStartingProduction = false;
              return;
            }

            // 查询本地配方是否存在
            const recipeQuery = {
              page: 1,
              size: 1000,
              sort: 'recipe_id asc',
              enable_flag: 'Y',
              recipe_name_eq: data.RecipeName
            };

            eapRecipeSel(recipeQuery)
              .then((recipeRes) => {
                if (recipeRes.code === 0 && recipeRes.data && recipeRes.data.length > 0) {
                  // 本地配方存在，继续调用查询配方详情接口并下发配方数据
                  this.queryAndDownloadRecipe(data.RecipeName, data)
                    .then(() => {
                      // 配方下发成功后，填充生产数据
                      this.productionData = {
                        PartNum: data.PartNum || "",
                        Qnty: data.Qnty || "",
                        RecipeName: data.RecipeName || "",
                        MC: data.MC || ""
                      };
                      console.log('填充生产数据:', this.productionData);
                      this.$message({ type: 'success', message: '上机成功' })
                    })
                    .catch((error) => {
                      // 配方下发失败时，保持数据为空
                      console.error('配方下发失败:', error);
                      // 错误信息已在queryAndDownloadRecipe方法中显示，这里不再重复显示
                    })
                    .finally(() => {
                      // 结束加载状态
                      this.isStartingProduction = false;
                    })
                } else {
                  // 本地配方不存在，不调用上机接口
                  this.$message({ 
                    type: "error", 
                    message: `本地配方【${data.RecipeName}】不存在，无法上机`
                  });
                  this.isStartingProduction = false;
                }
              })
              .catch((error) => {
                this.$message({
                  message: "查询本地配方失败：" + (error.msg || error.message),
                  type: "error"
                });
                this.isStartingProduction = false;
              });
          } else {
            this.$message({ type: "error", message: res.msg || "获取配方名称失败，上机失败"});
            // 结束加载状态
            this.isStartingProduction = false;
          }
        })
        .catch((error) => {
          this.$message({
            message: "上机失败：" + (error.msg || error.message),
            type: "error",
            duration: 10000,
            showClose: true
          });
          // 结束加载状态
          this.isStartingProduction = false;
        });
          } else {
            // 本地配方不存在，提示用户先配置好配方
            this.$message({ 
              type: "warning", 
              message: `本地配方【${this.partNo}】不存在，请先在本地配置管理好配方`
            });
            this.isStartingProduction = false;
          }
        })
        .catch((error) => {
          this.$message({
            message: "查询本地配方失败：" + (error.msg || error.message),
            type: "error"
          });
          this.isStartingProduction = false;
        });
    },

    queryAndDownloadRecipe(recipeName, productionData = null) {
      return new Promise((resolve, reject) => {
        if (!recipeName) {
          const errorMsg = '配方名称为空，无法查询配方详情'
          this.$message({ type: 'warning', message: errorMsg})
          reject(new Error(errorMsg))
          return
        }

        // 查询配方详情
        const query = {
          page: 1,
          size: 1000,
          sort: 'recipe_id asc',
          enable_flag: 'Y',
          recipe_name_eq: recipeName
        }

        eapRecipeSel(query)
          .then((res) => {
            if (res.code === 0 && res.data && res.data.length > 0) {
              this.query.recipe_id = res.data[0].recipe_id
              this.query.size = 1000
              this.$nextTick(async () => {
                try {
                  eapRecipeDetailSel(this.query)
                 .then((res) => {
                    console.log("返回结果："+JSON.stringify(res));
                    this.crud.data=res.data;
                     // 自动下发配方数据，传递生产数据和配方名称
                    this.downloadRecipeToDevice(productionData, recipeName)
                      .then(() => {
                        resolve()
                      })
                      .catch((error) => {
                        reject(error)
                      })
                 });
                } catch (error) {
                  reject(error);
                }             
              })
            } else {
              const errorMsg = `未能根据配方名称【${recipeName}】查询到相关配方信息,请先维护配方`
              this.$message({
                type: 'error',
                message: errorMsg
              })
              reject(new Error(errorMsg))
            }
          })
          .catch((error) => {
            const errorMsg = '查询配方失败：' + (error.msg || error.message)
            this.$message({ type: 'error', message: errorMsg})
            reject(error)
          })
      })
    },

    downloadRecipeToDevice(productionData = null, recipeName = null) {
      return new Promise((resolve, reject) => {
        // 使用MQTT下发配方数据到设备
        if (!this.crud.data || this.crud.data.length === 0) {
          const errorMsg = recipeName ? `本地未维护 ${recipeName} 配方` : '本地未维护配方'
          this.$message({ type: 'warning', message: errorMsg})
          reject(new Error(errorMsg))
          return
        }
        // 2.读取plc修改配方，判断是否允许下发配方
        if (this.monitorData.RecipeUpd.value === '0') {
          const errorMsg = '设备运行中，不允许下发配方RecipeUpd=0'
          this.$message({ type: 'info', message: errorMsg})
          reject(new Error(errorMsg))
          return
        }
        // 点击上机按钮时先清空生产数据显示
        this.productionData = {
                PartNum: "",
                Qnty: "",
                RecipeName: "",
                MC: ""
              };
        // 3.合并所有需要下发的数据到一个数组中
        const allRowJson = []
        // 修复TODO：确保使用最新的配方数据

        // 3.1 处理配方参数数据
        this.crud.data.forEach((item) => {
          var tagValue = item.parameter_val
          // 根据TagKey后缀覆盖特定参数值
          if (item.tag_key.endsWith('/LotID')) {
            tagValue = this.lot_no || tagValue // 使用页面批次号
          } else if (item.tag_key.endsWith('/PartNo')) {
            tagValue = (productionData && productionData.PartNum) || tagValue // 使用接口返回的料号
          } else if (item.tag_key.endsWith('/RecipeCode')) {
            tagValue = (productionData && productionData.RecipeName) || tagValue // 使用接口返回的配方名称
          }
          var newRow = {
            TagKey: item.tag_key,
            TagValue: tagValue
          }
          //输出日志
          console.log(newRow)
          allRowJson.push(newRow)
        })
        // 3.2 添加数量到TaskCount标签
        if (productionData && productionData.Qnty) {
          allRowJson.push({
            TagKey: 'PmPlc/PcStatus/TaskCount',
            TagValue: productionData.Qnty.toString()
          })
        }
        // 3.4 添加下发完成信号
        allRowJson.push({
          TagKey: 'PmPlc/PcStatus/RecipeDownReq',
          TagValue: '1'
        })
        // 4.一次性发送所有数据
        const sendJson = {
          Data: allRowJson,
          ClientName: 'SCADA_WEB'
        }
        const sendStr = JSON.stringify(sendJson)
        const topic = 'SCADA_WRITE/PmPlc' // 统一发送到 PmPlc
        this.sendMessage(topic, sendStr)

        // 3.3 添加用户到CurrentUser标签
        if (productionData && this.empId) {
          const sendCurrentUserJson = {
            Data: {
              TagKey: 'PmAis/AisStatus/CurrentUser',
              TagValue: this.empId // 使用页面员工号
            },
            ClientName: 'SCADA_WEB'
          }
          const sendStr = JSON.stringify(sendCurrentUserJson)
          const topic = 'SCADA_WRITE/PmAis' // 统一发送到 PmAis
          this.sendMessage(topic, sendStr)
        }
        setTimeout(() => {
          resolve() // 成功完成
        }, 500)

        // 5.间隔4秒后复位RecipeDownReq=0
        var sendJson4 = {}
        var rowJson4 = []
        var newRow4 = {
          TagKey: `PmPlc/PcStatus/RecipeDownReq`,
          TagValue: '0'
        }
        rowJson4.push(newRow4)
        sendJson4.Data = rowJson4
        sendJson4.ClientName = 'SCADA_WEB'
        var sendStr4 = JSON.stringify(sendJson4)
        var topic4 = `SCADA_WRITE/PmPlc`
        setTimeout(() => {
          this.sendMessage(topic4, sendStr4)
        }, 4000)
      })
    },
    choiceLotNum() {
      this.dialogVisible = true;
    },
    handleDisabled(value) {
      if (this.disabled) return false;
      const Arr = ["null", "", "undefined", "0"];
      if (!value || Arr.includes(value)) return true;
    },
    handleWrite(key, value) {
      var sendJson = {};
      var rowJson = [];
      var newRow = {
        TagKey: key,
        TagValue: value,
      };
      rowJson.push(newRow);
      sendJson.Data = rowJson;
      sendJson.ClientName = "SCADA_WEB";
      var sendStr = JSON.stringify(sendJson);
      var topic = "SCADA_WRITE/" + key.split("/")[0];
      this.sendMessage(topic, sendStr);
    },

    getCapacity() {
      this.capacityDom = this.$echarts.init(
        document.getElementById("capacityDom")
      );
      var that = this;
      this.capacityDom.setOption(this.capacityOption);
      window.addEventListener("resize", function () {
        that.capacityDom.resize();
      });
    },
    getOee() {
      this.oeeDom = this.$echarts.init(document.getElementById("oeeDom"));
      var that = this;
      this.oeeDom.setOption(this.oeeOption);
      window.addEventListener("resize", function () {
        that.oeeDom.resize();
      });
    },
    getreadbitRate() {
      this.readbitRateDom = this.$echarts.init(
        document.getElementById("readbitRateDom")
      );
      var that = this;
      this.readbitRateDom.setOption(this.readbitRateOption);
      window.addEventListener("resize", function () {
        that.readbitRateDom.resize();
      });
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get("userName"),
        cell_id: 1,
        current_ip: window.location.hostname,
      };
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result);
            this.cellIp = ipInfo.ip;
            this.webapiPort = ipInfo.webapi_port;
            this.mqttPort = ipInfo.mqtt_port;
            setTimeout(() => {
              this.toStartWatch();
              this.getTagValue();
              this.getAlarmData();
            }, 1000);
          } else {
            this.$message({ message: defaultQuery.msg, type: "error" });
          }
        })
        .catch(() => {
          this.$message({ message: "查询异常", type: "error" });
        });
    },
    getAlarmData() {
      var method = "/cell/core/scada/CoreScadaAlarmSelect";
      var path = "";
      if (process.env.NODE_ENV === "development") {
        path = "http://localhost:" + this.webapiPort + method;
      } else {
        path = "http://" + this.cellIp + ":" + this.webapiPort + method;
      }
      var queryData = {
        tablePage: 1,
        tableSize: 10,
      };
      axios
        .post(path, queryData, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data;
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: "查询异常：" + ex, type: "error" });
        });
    },
    getTagValue() {
      var readTagArray = [];
      
      // 构建 monitorData 标签查询数组
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code;
        var group_code = this.monitorData[key].group_code;
        var tag_code = this.monitorData[key].tag_code;
        if (this.aisMonitorMode === "AIS-SERVER") {
          client_code = client_code + "_" + this.$route.query.station_code;
        }
        var readTag = {};
        readTag.tag_key = client_code + "/" + group_code + "/" + tag_code;
        readTagArray.push(readTag);
      });
      
      // 添加 ArrTag 标签查询
      this.ArrTag.forEach((item) => {
        readTagArray.push({
          tag_key: "PmPlc/PlcCraft" + "/" + item,
        });
      });
      
      // 添加 plcCraftData 标签查询
      this.plcCraftData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key,
        });
      });

      // 添加E2DCheckFaillResult标签查询
      readTagArray.push({
        tag_key: 'PmAis/AisStatus/E2DCheckFaillResult'
      });

      var method = "/cell/core/scada/CoreScadaReadTag";
      var path = "";
      if (process.env.NODE_ENV === "development") {
        path = "http://localhost:" + this.webapiPort + method;
      } else {
        path = "http://" + this.cellIp + ":" + this.webapiPort + method;
      }
      
      axios
        .post(path, readTagArray, {
          headers: {
            "Content-Type": "application/json",
          },
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data;
              
              // 更新 monitorData
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code;
                var group_code = this.monitorData[key].group_code;
                var tag_code = this.monitorData[key].tag_code;
                if (this.aisMonitorMode === "AIS-SERVER") {
                  client_code =
                    client_code + "_" + this.$route.query.station_code;
                }
                var tag_key = client_code + "/" + group_code + "/" + tag_code;
                const item = result.filter((item) => item.tag_key === tag_key);
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? "" : item[0].tag_value;
                }
              });
              
              // 使用 endsWith 方法处理所有标签数据
              result.forEach((e) => {
                const tagKey = e.tag_key;
                const tagValue = e.tag_value;
                
                // 跳过空值
                if (!tagValue || tagValue.toString().trim() === '') {
                  return;
                }
                
                // 使用 endsWith 判断标签类型并更新对应字段
                if (tagKey.endsWith('/LotID')) {
                  this.lot_no = tagValue.toString().trim();
                  console.log('更新批次号:', this.lot_no);
                } else if (tagKey.endsWith('/PartNo')) {
                  this.productionData.PartNum = tagValue.toString().trim();
                  console.log('更新料号:', this.productionData.PartNum);
                } else if (tagKey.endsWith('/RecipeCode')) {
                  this.productionData.RecipeName = tagValue.toString().trim();
                  console.log('更新配方名称:', this.productionData.RecipeName);
                } else if (tagKey.endsWith('/TaskType')) {
                  this.productionData.MC = tagValue.toString().trim();
                  console.log('更新面次:', this.productionData.MC);
                } else if (tagKey.endsWith('/CurrentUser')) {
                  this.empId = tagValue.toString().trim();
                  console.log('更新员工号:', this.empId);
                } else if (tagKey.endsWith('/TaskCount')) {
                  this.productionData.Qnty = tagValue.toString().trim();
                  console.log('更新加工数量:', this.productionData.Qnty);
                }
                
                // 更新 ArrTag 相关的 productObj
                this.ArrTag.forEach((item) => {
                  if ("PmPlc/PlcCraft" + "/" + item === e.tag_key) {
                    this.productObj[item] = e.tag_value;
                  }
                });
                
                // 更新 plcCraftData
                this.plcCraftData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.tag_value = e.tag_value;
                    if (
                      typeof +e.tag_value === "number" &&
                      +e.tag_value >= item.down_limit &&
                      +e.tag_value <= item.upper_limit
                    ) {
                      item.status = "OK";
                    } else {
                      item.status = "NG";
                    }
                  }
                });
              });
              
              // 更新 productData
              this.productData = [this.productObj];

              // 检查E2DCheckFaillResult标签值
              this.checkE2DCheckFailResultInGetTagValue(result);
              
              console.log('表单数据更新完成:', {
                lot_no: this.lot_no,
                empId: this.empId,
                productionData: this.productionData,
                productObj: this.productObj
              });
            }
          }
        })
        .catch((error) => {
          console.error('获取标签值失败:', error);
        });
    },
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end();
        this.mqttConnStatus = false;
      }
      var connectUrl = "ws://" + this.cellIp + ":" + this.mqttPort + "/mqtt";
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt); // 开启连接
      this.clientMqtt.on("connect", (e) => {
        this.mqttConnStatus = true;
        var aisClientCode = "PmAis";
        var plcClientCode = "PmPlc";
        var eapClientCode = "PmEap";
        this.topicSubscribe("SCADA_STATUS/" + aisClientCode);
        this.topicSubscribe("SCADA_BEAT/" + aisClientCode);
        this.topicSubscribe("SCADA_STATUS/" + plcClientCode);
        this.topicSubscribe("SCADA_BEAT/" + plcClientCode);
        this.topicSubscribe("SCADA_STATUS/" + eapClientCode);
        this.topicSubscribe("SCADA_BEAT/" + eapClientCode);
        // 订阅E2DCheckFaillResult变化
        this.topicSubscribe("SCADA_CHANGE/PmAis/AisStatus/E2DCheckFaillResult");
        this.groupData.forEach((item) => {
          this.topicSubscribe("SCADA_CHANGE/" + item.tag_key);
        });
        Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code;
          var group_code = this.monitorData[key].group_code;
          var tag_code = this.monitorData[key].tag_code;
          if (this.aisMonitorMode === "AIS-SERVER") {
            client_code = client_code + "_" + this.$route.query.station_code;
          }
          this.topicSubscribe(
            "SCADA_CHANGE/" + client_code + "/" + group_code + "/" + tag_code
          );
        });
        this.$message({
          message: "连接成功",
          type: "success",
        });
        //记录当前登录账户
        this.handleWrite("PmAis/AisStatus/CurrentUser", this.user.nickName);
      });

      // MQTT连接失败
      this.clientMqtt.on("error", () => {
        this.$message({
          message: "连接失败",
          type: "error",
        });
        this.clientMqtt.end();
      });
      // 断开发起重连(异常)
      this.clientMqtt.on("reconnect", () => {
        this.$message({
          message: "连接断开，正在重连。。。",
          type: "error",
        });
      });
      this.clientMqtt.on("disconnect", () => {});
      this.clientMqtt.on("close", () => {});
      // 接收消息处理
      this.clientMqtt.on("message", (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message);
          if (jsonData == null) return;
          if (topic.indexOf('SCADA_BEAT/') >= 0) {
            var heartBeatValue = jsonData.Beat
             if (topic.indexOf('SCADA_BEAT/PmPlc') >= 0) {
               if (this.controlStatus.plc_status !== '2') {
                 this.controlStatus.plc_status = heartBeatValue
               }
             }
          } else if (topic.indexOf("SCADA_CHANGE/") >= 0) {
            var tagKey=jsonData.TagKey;
            var tagNewValue=jsonData.TagNewValue;
            console.log(tagKey,tagNewValue);
            // 处理E2DCheckFaillResult变化
            if (jsonData.TagKey === "PmAis/AisStatus/E2DCheckFaillResult" && jsonData.TagNewValue) {
              this.handleE2DCheckFailResult(jsonData.TagNewValue);
            }else if (tagKey.includes('PmPlc/PlcCraft/LotID')) {
              this.lot_no = tagNewValue;
            }
            else if (tagKey.includes('PmPlc/PcStatus/TaskCount')) {
              this.productionData.Qnty = tagNewValue;
            }else if (tagKey.includes('PmAis/AisStatus/CurrentUser')) {
              this.empId = tagNewValue;
            }else if (tagKey.includes('PmPlc/PlcCraft/PartNo')) {
              this.productionData.PartNum = tagNewValue;
            }else if (tagKey.includes('PmPlc/PlcCraft/RecipeCode')) {
              this.productionData.RecipeName = tagNewValue;
            }else if (tagKey.includes('PmAis/AisStatus/TaskType')) {
              this.productionData.MC = tagNewValue;
            }
            const group_code = "PmPlc/PlcCraft";
            this.ArrTag.forEach((item) => {
              if (group_code + "/" + item === jsonData.TagKey) {
                // 强制更新数组
                this.productObj = {
                  ...this.productObj,
                  [item]: jsonData.TagNewValue,
                };
                this.productData = [this.productObj];
              }
            });
            this.plcCraftData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.tag_value = jsonData.TagNewValue;
                // 检查值是否为数字类型，或者是否可以成功转换为数字
                const isNumeric = !isNaN(parseFloat(jsonData.TagNewValue)) && isFinite(jsonData.TagNewValue);
                const currentValue = +jsonData.TagNewValue;
                const downLimit = item.down_limit;
                const upperLimit = item.upper_limit;

                if (downLimit == -1 && upperLimit == -1) {
                  item.status = "OK"; // 上下限都为-1，始终OK
                } else if (isNumeric && 
                           (downLimit == -1 || currentValue >= downLimit) && 
                           (upperLimit == -1 || currentValue <= upperLimit)) {
                  item.status = "OK";
                } else {
                  // 如果值不是数字，或者超出了有效的上下限范围，则为NG
                  // 但如果值不是数字，且上下限之一或全部为-1，需要特殊处理，这里暂时先统一标为NG，后续可根据具体需求调整
                  item.status = "OK";
                }
              }
            });
            Object.keys(this.monitorData).forEach((key) => {
              var client_code = this.monitorData[key].client_code;
              var group_code = this.monitorData[key].group_code;
              var tag_code = this.monitorData[key].tag_code;
              if (this.aisMonitorMode === "AIS-SERVER") {
                client_code =
                  client_code + "_" + this.$route.query.station_code;
              }
              var tag_key = client_code + "/" + group_code + "/" + tag_code;
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue;
              }
            });
          }
        } catch (e) {
          console.log(e);
        }
      });
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: "请启动监控",
          type: "error",
        });
        return;
      }
      console.log(topic, msg);
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn("MQTT发送成功：" + topic);
          // this.$message({ message: '写入成功', type: 'success' })
        } else {
          this.$message({ message: "操作失败！", type: "error" });
        }
      });
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: "请启动监控",
          type: "error",
        });
        return;
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: "请启动监控",
            type: "error",
          });
          return;
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          console.log("MQTT订阅失败:" + topic);
        }
      });
    },

    // 处理E2DCheckFailResult变化
    handleE2DCheckFailResult(failMsg) {
      try {
        // 解析失败信息JSON
        const failData = JSON.parse(failMsg);

        // 填充表单数据
        this.ngReasonForm = {
          LotNum: failData.LotNum || '',
          RemainingPieces: failData.RemainingPieces || '',
          ReadInfo: failData.ReadInfo || '',
          ReadMC: failData.ReadMC || '',
          Time: failData.Time || '',
          Msg: failData.Msg || '',
          CcdCode: failData.CcdCode || '',
          EmpId: this.empId || '', // 默认使用页面的员工号
          NGReason: '' // 清空NG原因，等待用户填写
        };

        // 显示弹窗
        this.ngReasonDialogVisible = true;
      } catch (error) {
        console.error('解析E2DCheckFailResult失败:', error);
        this.$message({ type: 'error', message: '解析失败信息失败' });
      }
    },

    // 提交NG原因并手动放行
    submitNGReason() {
      // 验证NG原因是否填写
      if (!this.ngReasonForm.NGReason.trim()) {
        this.$message({ type: 'warning', message: '请填写NG原因' });
        return;
      }

      // 验证员工号是否填写
      if (!this.ngReasonForm.EmpId.trim()) {
        this.$message({ type: 'warning', message: '请填写员工号' });
        return;
      }

      // 防止重复提交
      if (this.isSubmittingNGReason) {
        return;
      }

      this.isSubmittingNGReason = true;

      // 构建请求参数
      const params = {
        LotNum: this.ngReasonForm.LotNum,
        RemainingPieces: this.ngReasonForm.RemainingPieces,
        ReadInfo: this.ngReasonForm.ReadInfo,
        ReadMC: this.ngReasonForm.ReadMC,
        NGReason: this.ngReasonForm.NGReason,
        EmpID: this.ngReasonForm.EmpId,  // 接口需要EmpID参数
        StationCode: this.$route.query.station_code || 'HSQL'
      };

      // 调用手动放行接口
      ngReasonSaved(params)
        .then((res) => {
          if (res.code === 0 && res.data && res.data.length > 0) {
            const result = res.data[0];
            if (result.Success) {
              this.$message({ type: 'success', message: result.Messages || '手动放行成功' });
              // 关闭弹窗
              this.ngReasonDialogVisible = false;
              // 清空表单
              this.resetNGReasonForm();
            } else {
              this.$message({ type: 'error', message: result.Messages || '手动放行失败' });
            }
          } else {
            this.$message({ type: 'error', message: res.msg || '手动放行失败' });
          }
        })
        .catch((error) => {
          this.$message({ type: 'error', message: '手动放行请求失败：' + (error.msg || error.message) });
        })
        .finally(() => {
          this.isSubmittingNGReason = false;
        });
    },

    // 重置NG原因表单
    resetNGReasonForm() {
      this.ngReasonForm = {
        LotNum: '',
        RemainingPieces: '',
        ReadInfo: '',
        ReadMC: '',
        Time: '',
        Msg: '',
        CcdCode: '',
        EmpId: '',
        NGReason: ''
      };
    },

    // 关闭NG原因弹窗
    closeNGReasonDialog() {
      this.ngReasonDialogVisible = false;
      this.resetNGReasonForm();
      //清空掉plc的json点位数据。
      const sendStr = '{"Data":[{"TagKey":"PmAis/AisStatus/E2DCheckFaillResult","TagValue":""}],"ClientName":"SCADA_WEB"}';
      const topic = 'SCADA_WRITE/PmAis' // 统一发送到 PmAis
      this.sendMessage(topic, sendStr)
    },

    // 获取CcdCode对应的错误类型描述
    getCcdCodeType(ccdCode) {
      return this.ccdCodeTypes[ccdCode] || '未知错误类型';
    },

    // 在getTagValue中检查E2DCheckFaillResult标签值
    checkE2DCheckFailResultInGetTagValue(result) {
      // 如果已经有弹窗显示，则不再检查
      if (this.ngReasonDialogVisible) {
        return;
      }

      // 查找E2DCheckFaillResult标签的值
      const e2dCheckItem = result.find(item => item.tag_key === 'PmAis/AisStatus/E2DCheckFaillResult');
      if (e2dCheckItem) {
        const tagValue = e2dCheckItem.tag_value;

        // 如果标签值不为空且不是空字符串，则显示弹窗
        if (tagValue && tagValue.trim() !== '' && tagValue !== '0') {
          console.log('检测到E2DCheckFaillResult有值:', tagValue);
          this.handleE2DCheckFailResult(tagValue);
        } else {
          console.log('E2DCheckFaillResult为空，不显示弹窗');
        }
      }
    }
  },
};
</script>
<style lang="less" scoped>
.production-data-text {
  display: inline-block;
  padding: 4px 8px;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  color: #606266;
  font-size: 14px;
  min-width: 120px;
  text-align: left;
}

.app-container {
  padding: 10px;
  ::v-deep .el-header {
    padding: 0;
  }
  .header {
    ::v-deep .el-card__body {
      padding: 10px 15px 0 !important;
    }
  }
  .wrapTextSelect {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrapElForm {
    display: flex;
    ::v-deep .barcode {
      display: flex;
      .el-form-item--small {
        width: 90%;
        margin-right: 10px;
      }
    }
  }
  .pieChart {
    width: 100%;
    display: flex;
    div {
      width: 33%;
    }
    #capacityDom {
      height: 300px;
    }
    #capacityDom {
      height: 300px;
    }
    #readbitRateDom {
      height: 300px;
    }
  }
  .active {
    ::v-deep .el-input__inner {
      background-color: #ffff00;
    }
  }
  .dialog-footer {
    text-align: center;
  }
  .statuHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrappstyle {
    display: flex;
    align-items: center;
    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 12px;
        font-weight: 700;
      }
      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }

    p:last-child {
      margin-right: 0 !important;
    }

    .el-divider--vertical {
      width: 2px;
      height: 2em;
    }
  }
  .btnone {
    background: #50d475;
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
  }

  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
    font-size: 18px;
  }

  .btnone:active {
    background: #13887c;
  }
  .wholeline {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }
  .wholeline1 {
    width: 20px;
    height: 20px;
  }
  .wholelinenormal1,
  .deviceGreen {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .deviceRed {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelineerror1,
  .deviceYellow {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #eeff00;
    box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelinegray1 {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .dialogTable {
    ::v-deep .el-dialog {
      margin-top: 5vh !important;
    }
  }
}
/* 生产监控项样式 */
.production-monitoring {
  margin: 15px 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.monitoring-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 8px;
  background-color: #ffffff;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.monitoring-item:last-child {
  margin-bottom: 0;
}

.monitoring-label {
  font-weight: 500;
  color: #495057;
  font-size: 14px;
}

.monitoring-value {
  font-weight: bold;
  color: #007bff;
  font-size: 16px;
}

</style>

<!-- eslint-disable vue/valid-v-bind -->
<template>
  <div id="loadMonitor">
    <el-container style="height: 100%;width: 100%;">
      <el-header>
        <div class="statuHead">
          <div>
            <el-popover placement="right" width="170" trigger="click">
              <el-button
                v-for="(item,index) in sysModelData"
                :key="index"
                size="medium"
                type="primary"
                style="font-size:20px"
                :style="{'margin': index === 1 ? '10px 0' : '0'}"
                @click="handleWrite('LoadPlc0/PlcConfig/SysModel',item.id)"
              >
                {{ item.label }}
              </el-button>
              <el-button slot="reference" :class="(configData.scadaPoint[0].tags.tag_value === '1' || configData.scadaPoint[0].tags.tag_value === '2') ? 'btnone' : 'btnone0'">{{ sysModelData[configData.scadaPoint[0].tags.tag_value].label || $t('lang_pack.dy.offLine') }}</el-button>
            </el-popover>
            <el-button
              :class="configData.scadaPoint[1].tags.tag_value === '1' ? 'btnone' : 'btnone0'"
              type="success"
              @click="writeScadaValue(configData.scadaPoint[1].tags.tag_key, configData.scadaPoint[1].tags.tag_value === '1' ? '0' : '1')"
            >{{ configData.scadaPoint[1].tags.tag_value === '1' ? 'AGV' : 'MGV' }}</el-button>
          </div>
          <div>
            <div class="wrappstyle">
              <p><span
                   :class="unload_status === '1' ? 'wholeline wholelinenormal' :
                     (unload_status === '0' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">{{ $t('lang_pack.hmiMain.reOnline') }}</span>
              </p>
              <p v-for="(item,index) in configData.heartbeat" :key="index">
                <span
                  :class="item.tags.tag_value === '1' ? 'wholeline wholelinenormal' :
                    (item.tags.tag_value === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                />
                <span class="statuText">{{ item.tags.tag_des.indexOf('lang_pack') > -1 ? $t(item.tags.tag_des) : item.tags.tag_des }}</span>
              </p>
            </div>
          </div>
        </div>
      </el-header>
      <el-main>
        <div class="peopleInfo">
          <ul><li><span>{{ $t('lang_pack.hmiMain.employee') }}</span><span>{{ loginInfo.user_name }}</span></li>
            <li class="longString" />
            <li><span>{{ $t('lang_pack.hmiMain.name') }}</span><span> {{ loginInfo.nick_name }}</span></li>
            <li class="longString" />
            <li><span>{{ $t('lang_pack.hmiMain.department') }}</span><span>{{ loginInfo.dept_id }}</span></li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.status') }}</span>
              <span
                :class="`${DeviceStatus[configData.scadaPoint[2].tags.tag_value].class || 'deviceInit'}`"
                class="normal"
              >{{ DeviceStatus[configData.scadaPoint[2].tags.tag_value].label || 'deviceInit' }}</span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.production') }}</span>
              <span :class="configData.scadaPoint[0].tags.tag_value === '1' ? 'zaixian' : 'lixian'">
                {{ configData.scadaPoint[0].tags.tag_value === '1' ? $t('lang_pack.hmiMain.EAP') : $t('lang_pack.hmiMain.AIS') }}
              </span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.plate') }}</span>
              <span :class="configData.scadaPoint[3].tags.tag_value === '1' ? 'zaixian' : 'lixian'">
                {{ configData.scadaPoint[3].tags.tag_value === '1' ? $t('lang_pack.hmiMain.panel') : $t('lang_pack.hmiMain.NoPanel') }}
              </span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.work') }}</span>
              <span>{{ configData.scadaPoint[4].tags.tag_value || '0' }}</span>
            </li>
            <li class="lastLi">
              <el-button type="primary" @click="openUserLogin">{{ $t('lang_pack.hmiMain.Login') }}</el-button>
              <el-button type="danger" @click="handleUserLogout">{{ $t('lang_pack.hmiMain.LogOut') }}</el-button>
            </li>
          </ul>
        </div>
        <div class="wraptable NativeTable3">
          <div style="width: 49.9%;background-color: #83d1f5;">
            <span style="font-weight: 600;font-size: 30px;margin: 10px 0;display: flex;justify-content: center;">{{ projectCode === 'GZGH' ?  $t('lang_pack.hmiMain.SupportPunching1') : $t('lang_pack.hmiMain.SupportBrowning') }}</span>
          </div>
          <div style="width: 49.9%;background-color: #83d1f5;">
            <span style="font-weight: 600;font-size: 30px;margin: 10px 0;display: flex;justify-content: center;">{{ projectCode === 'GZGH' ?  $t('lang_pack.hmiMain.SupportPunching2') : $t('lang_pack.hmiMain.SupportPunching') }}</span>
          </div>
        </div>
        <div class="wraptable NativeTable3">
          <el-table
            border
            size="small"
            :data="moTableData1"
            style="width: 100%;margin-right: 5px;"
            height="545"
            :highlight-current-row="true"
          >
            <el-table-column prop="group_lot_num" :label="$t('lang_pack.hmiMain.fmale')" width="300" />
            <el-table-column prop="lot_num" :label="$t('lang_pack.taskTable.batchNumber')" width="300" />
            <el-table-column prop="port_code" :label="$t('lang_pack.hmiMain.blowOff')" width="70" />
            <el-table-column prop="plan_lot_count" :label="$t('lang_pack.hmiMain.plan')" width="70" />
            <el-table-column prop="finish_count" :label="$t('lang_pack.hmiMain.put')" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.finish_ok_count }}/{{ scope.row.finish_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="task_status" :label="$t('lang_pack.hmiMain.putStatus')" width="120">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.task_status == '生产中' ? '' : (scope.row.task_status == '正常完板' ? 'success' : 'danger'))"
                  class="elTag"
                >
                  {{ scope.row.task_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sb_finish_count" :label="$t('lang_pack.hmiMain.received')" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.sb_finish_ok_count }}/{{ scope.row.sb_finish_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="sb_port_code" :label="$t('lang_pack.hmiMain.close')" width="80" />
            <el-table-column prop="sb_task_status" :label="$t('lang_pack.hmiMain.receivingState')" width="120">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.sb_task_status == '未同步' ? 'info' : (scope.row.sb_task_status == '计划中' ? '' : (scope.row.sb_task_status == '生产中' ? '' : (scope.row.sb_task_status == '正常完板' ? 'success' : 'danger'))))"
                  class="elTag"
                >
                  {{ scope.row.sb_task_status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            border
            size="small"
            :data="moTableData2"
            style="width: 100%;"
            height="545"
            :highlight-current-row="true"
          >
            <el-table-column prop="group_lot_num" :label="$t('lang_pack.hmiMain.fmale')" width="300" />
            <el-table-column prop="lot_num" :label="$t('lang_pack.taskTable.batchNumber')" width="300" />
            <el-table-column prop="port_code" :label="$t('lang_pack.hmiMain.blowOff')" width="70" />
            <el-table-column prop="plan_lot_count" :label="$t('lang_pack.hmiMain.plan')" width="70" />
            <el-table-column prop="finish_count" :label="$t('lang_pack.hmiMain.put')" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.finish_ok_count }}/{{ scope.row.finish_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="task_status" :label="$t('lang_pack.hmiMain.putStatus')" width="120">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.task_status == '生产中' ? '' : (scope.row.task_status == '正常完板' ? 'success' : 'danger'))"
                  class="elTag"
                >
                  {{ scope.row.task_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sb_finish_count" :label="$t('lang_pack.hmiMain.received')" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.sb_finish_ok_count }}/{{ scope.row.sb_finish_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="sb_port_code" :label="$t('lang_pack.hmiMain.close')" width="80" />
            <el-table-column prop="sb_task_status" :label="$t('lang_pack.hmiMain.receivingState')" width="120">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.sb_task_status == '未同步' ? 'info' : (scope.row.sb_task_status == '计划中' ? '' : (scope.row.sb_task_status == '生产中' ? '' : (scope.row.sb_task_status == '正常完板' ? 'success' : 'danger'))))"
                  class="elTag"
                >
                  {{ scope.row.sb_task_status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="wraptable NativeTable3">
          <el-table
            border
            :data="tableData1"
            style="width: 100%;margin-right: 5px;"
            height="280"
          >
            <el-table-column prop="panel_index" width="80" :label="$t('lang_pack.hmiMain.serial')" />
            <el-table-column prop="panel_barcode" :label="$t('lang_pack.hmiMain.plateCode')" width="300" />
            <el-table-column prop="tray_barcode" :label="$t('lang_pack.hmiMain.Tray')" />
            <el-table-column prop="inspect_flag" :label="$t('lang_pack.hmiMain.firstPiece')" width="80" />
            <el-table-column prop="panel_status" :label="$t('lang_pack.hmiMain.panelStatus')">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.panel_status == 'NG' ? 'danger' : (scope.row.panel_status == 'OK' ? 'success' : 'warning'))"
                  class="elTag"
                >
                  {{ scope.row.panel_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="panel_ng_msg" :label="$t('lang_pack.hmiMain.abnormal')" width="120" />
            <el-table-column prop="item_date" :label="$t('lang_pack.hmiMain.time')" width="130" />
            <el-table-column prop="dummy_flag" :label="$t('lang_pack.hmiMain.sideboard')" width="80" style="border-right:1px solid red !important" />
          </el-table>
          <el-table
            border
            :data="tableData2"
            style="width: 100%;"
            height="280"
          >
            <el-table-column prop="panel_index" width="80" :label="$t('lang_pack.hmiMain.serial')" />
            <el-table-column prop="panel_barcode" :label="$t('lang_pack.hmiMain.plateCode')" width="300" />
            <el-table-column prop="tray_barcode" :label="$t('lang_pack.hmiMain.Tray')" />
            <el-table-column prop="inspect_flag" :label="$t('lang_pack.hmiMain.firstPiece')" width="80" />
            <el-table-column prop="panel_status" :label="$t('lang_pack.hmiMain.panelStatus')">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.panel_status == 'NG' ? 'danger' : (scope.row.panel_status == 'OK' ? 'success' : 'warning'))"
                  class="elTag"
                >
                  {{ scope.row.panel_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="panel_ng_msg" :label="$t('lang_pack.hmiMain.abnormal')" width="120" />
            <el-table-column prop="item_date" :label="$t('lang_pack.hmiMain.time')" width="130" />
            <el-table-column prop="dummy_flag" :label="$t('lang_pack.hmiMain.sideboard')" width="80" style="border-right:1px solid red !important" />
          </el-table>
        </div>
      </el-main>
    </el-container>
    <el-dialog :title="$t('lang_pack.vie.employeeLogin')" :visible.sync="userDialogVisible" width="650px" top="65px" class="elDialog dialog_hmi">
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.employee') }}
      <el-input ref="userId" v-model="userId" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.name') }}
      <el-input ref="nickName" v-model="nickName" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.department') }}
      <el-input ref="deptId" v-model="deptId" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('view.table.shift') }}
      <el-input ref="shiftId" v-model="shiftId" clearable size="mini" style="width:100%" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="userDialogVisible = false">{{ $t('lang_pack.vie.cancel') }}</el-button>
        <el-button type="primary" @click="handleUserLogin">{{ $t('lang_pack.SignOn') }}</el-button>
      </span>
    </el-dialog>
    <roleCheck
      v-if="roleCheckShow"
      ref="roleCheck"
      :role_user_id="userId"
      :role_func_code="role_func_code"
      @roleCheck="roleCheck"
    />
  </div>
</template>
<script>
import Paho from 'paho-mqtt'
import autofit from 'autofit.js'
import roleCheck from '@/views/core/hmi/roleCheck'
import { CoreScadaReadTag } from '@/api/hmi/mainIndex'
import axios from 'axios'
import Cookies from 'js-cookie'
import '@/assets/styles/trigger.scss'
import { selCellIP } from '@/api/core/center/cell'
import { carrierIDReport } from '@/api/eap/project/dy/eapDyApsPlan'
import { selLoginInfo, userLogin, userLogout } from '@/api/eap/project/dy/eapDyMeStationUser'
import { stationTaskPbjSel, loadCurrentTaskSel, unLoadStatusSelCore4, eapCimMsgShow, eapApsPlanCancel, unLoadCurrentTaskSel } from '@/api/eap/eapApsPlan'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import configData from '@/api/eap/core4/hmi/stationMonitorPbj/pbj_data.json'
import { error } from 'jquery'
export default {
  name: 'STATION_MONITOR_PBJ',
  components: { roleCheck },
  data() {
    return {
      timer: null,
      configData,
      projectCode: "",
      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: ''
      },
      moTableData1: [],
      moTableData2: [],
      tableData1: [],
      tableData2: [],
      userId: '',
      nickName: '',
      deptId: '',
      shiftId: '',
      delayCount: 0,
      tagValueSecond: 0,
      tagValueTimer: null,
      userDialogVisible: false,
      // 启用监听数据模式 AIS-PC=电脑模式,AIS-SERVER=服务器模式
      // AIS-SERVER模式，监听的Client Code需要拼接上工位编码,例如：LoadPlc0_OP1010
      aisMonitorMode: 'AIS-PC',
      unload_status: '',
      triggerMonitors: {},
      clients: {},
      clientPorts: {},
      tagKeysMap: {},
      cellIp: '',
      webapiPort: '',
      taskDirection: {
        '-1': [],
        '0': [],
        '1': [],
        '2': []
      },
      scadaClient: {},
      mqttClient: {},
      taskTimer: null,
      showMsg: false,
      statusTimer: null,
      role_func_code: '',
      roleCheckShow: false,
      taskFlag: true,
      writePoint: {},
      stationFlowTaskList: [], // 工位流程任务列表
      sysModelData: [
        { id: '0', label: this.$t('lang_pack.dy.offLine') },
        { id: '1', label: this.$t('lang_pack.dy.semiAuto') },
        { id: '2', label: this.$t('lang_pack.hmiMain.onLine') }
      ],
      DeviceStatus: [
        { class: 'deviceInit', label: this.$t('lang_pack.hmiMain.deviceInit') },
        { class: 'deviceRun', label: this.$t('lang_pack.hmiMain.deviceRun') },
        { class: 'deviceStop', label: this.$t('lang_pack.hmiMain.deviceStop') },
        { class: 'deviceIdle', label: this.$t('lang_pack.hmiMain.deviceIdle') },
        { class: 'deviceDown', label: this.$t('lang_pack.hmiMain.deviceDown') },
        { class: 'devicePm', label: this.$t('lang_pack.hmiMain.devicePm') }
      ]
    }
  },
  mounted: function() {    
    // 获取系统参数信息
    var queryParameter = {
      userName: Cookies.get("userName"),
      parameter_code: "ProjectCode",
      enable_flag: "Y",
    };
    selSysParameter(queryParameter)
      .then((res) => {
        const defaultQuery = JSON.parse(JSON.stringify(res));
        if (defaultQuery.code === 0 && defaultQuery.data !== "") {
          this.projectCode = defaultQuery.data[0].parameter_val;
        }
      })
      .catch(() => {
        this.$message({
          message: "查询异常",
          type: "error",
        });
      });
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#loadMonitor',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 340
    }
  },
  created() {
    this.init()
    this.getSystemParams()
  },
  beforeDestroy() {
    autofit.off()
    clearInterval(this.timer)
    clearInterval(this.taskTimer)
    for (const key in this.clients) {
      this.clients[key] && this.clients[key].disconnect()
    }
  },
  methods: {
    init() {
      // 获取scada IP
      this.getCellIp()
      // 获取登录信息
      this.getLoginInfo()
      this.getUnLoadStatusInfo()
      this.statusTimer = setInterval(() => {
        this.getUnLoadStatusInfo()
      }, 5000)
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.$route.query.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.getScadaPointdata(ipInfo)
            this.getFlowTaskData(ipInfo)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            // this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    // 打开登录操作
    openUserLogin() {
      this.userId = ''
      this.userDialogVisible = true
      this.$nextTick(x => {
        // 正确写法
        this.$refs.userId.focus()
      })
    },
    getLoginInfo() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      }
      selLoginInfo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            } else {
              this.loginInfo.user_name = '---'
              this.loginInfo.nick_name = '---'
              this.loginInfo.dept_id = '---'
              this.loginInfo.shift_id = '---'
            }
          } else {
            this.loginInfo.user_name = '---'
            this.loginInfo.nick_name = '---'
            this.loginInfo.dept_id = '---'
            this.loginInfo.shift_id = '---'
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.loginInfo.user_name = '---'
          this.loginInfo.nick_name = '---'
          this.loginInfo.dept_id = '---'
          this.loginInfo.shift_id = '---'
          this.$message({ message: this.$t('lang_pack.hmiMain.procedure'), type: 'error' })
        })
    },
    // 处理登录
    handleUserLogin() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        user_code: this.userId,
        dept_id: this.deptId,
        shift_id: this.shiftId,
        nick_name: this.nickName,
        check_out_flag: 'N'
      }
      // 倒计时功能
      this.delayCount = 15
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: '数据处理中（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = '数据处理中（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      userLogin(query)
        .then(res => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const loginInfo = defaultQuery.data[0]
            this.loginInfo.user_name = loginInfo.user_name
            this.loginInfo.nick_name = loginInfo.nick_name
            this.loginInfo.dept_id = loginInfo.dept_id
            this.loginInfo.shift_id = loginInfo.shift_id
            this.userDialogVisible = false
            this.$message({ message: this.$t('lang_pack.vie.loginSuccess'), type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
          if (defaultQuery.code === -1) {
            this.tagValueSecond = Number(configData.scadaPoint[5].tags.tag_value.value)
            if (this.tagValueSecond > 0) {
              this.tagValueTimer && clearInterval(this.tagValueTimer)
              this.warningMsgDialogVisible = true
              this.timer = setInterval(() => {
                this.tagValueSecond--
                if (this.tagValueSecond === 0) {
                  this.warningMsgDialogVisible = false
                  clearInterval(this.timer)
                  this.tagValueTimer = null
                }
              }, 1000)
              return
            }
          }
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 处理登出
    handleUserLogout() {
      this.$confirm(this.$t('lang_pack.vie.AreYouSureToLogOut'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          const query = {
            user_name: Cookies.get('userName'),
            station_id: this.$route.query.station_id,
            user_code: this.userId,
            dept_id: '',
            shift_id: '',
            nick_name: '',
            check_out_flag: 'Y'
          }
          // 倒计时功能
          this.delayCount = 15
          const ths = this
          const loading = this.$loading({
            lock: true,
            text: '数据处理中（' + this.delayCount + '）',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          this.timer3 = setInterval(function() {
            ths.delayCount--
            loading.text = '数据处理中（' + ths.delayCount + '）'
            if (ths.delayCount === 0) {
              loading.close()
              clearInterval(this.timer3)
            }
          }, 1000)

          userLogout(query)
            .then(res => {
              // 清除计时器
              this.delayCount = 0
              loading.close()
              clearInterval(this.timer3)

              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.loginInfo.user_name = ''
                this.loginInfo.nick_name = ''
                this.loginInfo.dept_id = ''
                this.loginInfo.shift_id = ''
                this.$message({ message: this.$t('lang_pack.hmiMain.logoutSuccess'), type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              // 清除计时器
              this.delayCount = 0
              loading.close()
              clearInterval(this.timer3)
              this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
            })
        })
        .catch(() => {
        })
    },
    getStationTaskInfo(work_port_index, i) {
      if (this.$route.query.station_id === '' || work_port_index.length === 0) {
        this[`tableData${i}`] = []
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        work_port_index,
        on_off_value: '' // 0离线,1在线
      }
      stationTaskPbjSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.result !== '') {
              const resultData = JSON.parse(defaultQuery.result)
              this[`tableData${i}`] = resultData.planDInfo
            }
          } else {
            this[`tableData${i}`] = []
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          // this.$message({ message: '获取工位当前任务计划异常', type: 'error' })
        })
    },
    getCurrentTaskInfo(port_code, i) {
      if (this.$route.query.station_id === ''||port_code.length === 0) {
        this[`moTableData${i}`] = []
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        port_code
      }
      loadCurrentTaskSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const resultData = defaultQuery.data
              this[`moTableData${i}`] = resultData
            } else {
              this[`moTableData${i}`] = []
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          // this.$message({ message: '获取工位当前任务计划异常', type: 'error' })
        })
    },
    // 获取离线状态
    getUnLoadStatusInfo() {
      if (this.$route.query.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      }
      unLoadStatusSelCore4(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.result !== '') {
              const resultData = JSON.parse(defaultQuery.result)
              var unload_onoff_value = resultData.unload_onoff_value
              if (unload_onoff_value !== '0' && unload_onoff_value) {
                this.unload_status = '1'
              } else {
                this.unload_status = '0'
              }
            }
          }
        })
        .catch(() => {
        })
    },
    getSystemParams() {
      var queryParameter = {
        userName: Cookies.get('userName'),
        cell_id: '0',
        parameter_code: 'AIS_MONITOR_MODE',
        enable_flag: 'Y'
      }
      selSysParameter(queryParameter)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              this.aisMonitorMode = defaultQuery.data[0].parameter_val
            }
          }
        }).catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    getScadaPointdata(ipInfo) {
      Object.keys(this.configData).forEach(temp => {
        this.configData[temp].forEach(item => {
          const triggerTopic = this.getTopic(item.tags.tag_key)
          const mqttPort = item.ports.mqtt
          const cellPort = item.ports.cell
          const scadaTopic = item.scadaClient
          this.triggerMonitors[triggerTopic] = item
          if (this.clientPorts[mqttPort] == null) {
            this.clientPorts[mqttPort] = true
          }
          if (this.tagKeysMap[cellPort] == null) {
            this.tagKeysMap[cellPort] = []
          }
          if (this.scadaClient[scadaTopic] == null) {
            this.scadaClient[scadaTopic] = []
          }
          this.scadaClient[scadaTopic].push({ 'tag_key': item.tags.tag_key, 'client': item.scadaClient })
          this.tagKeysMap[cellPort].push({ 'tag_key': item.tags.tag_key })
        })
      })
      // 把统一的端口放到一个对象里面去 ，例如8089的
      for (const key in this.tagKeysMap) {
        const cellUrl = `http://${ipInfo.ip}:${ipInfo.webapi_port}/`
        CoreScadaReadTag(cellUrl, this.tagKeysMap[key])
          .then(res => {
            if (res.code === 0 && res.data) {
              for (const i in res.data) {
                const item = res.data[i]
                const k = item.tag_key
                const v = item.tag_value
                this.setContent(k, v)
              }
            }
          })
      }
      for (const key in this.scadaClient) {
        const port = parseInt(ipInfo.mqtt_port)
        this.connectMQTT(ipInfo.ip, port, (c) => {
          this.clients[key] = c
          // 发布订阅
          this.scadaClient[key].forEach(item => {
            c.subscribe(`${item.client}/${item.tag_key}`, {
              onSuccess: () => {
                console.log('订阅成功：', item.client + '/' + item.tag_key)
              },
              onFailure: (responseObject) => {
                console.log('订阅失败：', item.client + '/' + item.tag_key, responseObject.errorMessage)
              }
            })
          })
        })
      }
    },
    getTopic(tagKey) {
      return tagKey.indexOf('/') > -1 ? `SCADA_CHANGE/${tagKey}` : `SCADA_BEAT/${tagKey}`
    },
    setContent(k, v) {
      this.taskDirection = {
        '-1': [],
        '0': [],
        '1': [],
        '2': []
      }
      if (v === undefined || v === null) return
      Object.keys(this.configData).forEach(temp => {
        this.configData[temp].forEach(item => {
          if (item.tags.tag_key === k) {
            item.tags.tag_value = v
          }
          if (item.tags.tag_value && item.tags.code) {
            // 去重
            this.taskDirection[item.tags.tag_value] = [...this.taskDirection[item.tags.tag_value], ...item.tags.code || []]
            if (this.taskFlag) {
              this.taskFlag = false
              this.taskTimer = setInterval(() => {
                for (const i in this.taskDirection) {
                  const work_port_index = this.taskDirection[i]
                  // // 查询放板机最近任务信息
                  if (i === '1' || i === '2') {
                    this.getCurrentTaskInfo(work_port_index, i) // 1配套棕化 // 2配套冲孔
                    this.getStationTaskInfo(work_port_index, i)
                  }
                }
              }, 5000)
            }
          }
        })
      })
    },
    handleWrite(tag_key, tag_value) {
      this.writePoint = { tag_key, tag_value }
      if (tag_value === '0') {
        this.role_func_code = 'handleOnOffLine'
        this.roleCheckShow = true
        return
      }
      this.handleOnOffLineSwitch()
    },
    // 权限验证共用方法
    roleCheck(roleFuncCode, status) {
      this.roleCheckShow = false
      if (roleFuncCode === 'handleOnOffLine') {
        if (status === 'OK') {
          this.handleOnOffLineSwitch()
        }
      }
    },
    getFlowTaskData(ipInfo) {
      var method = '/cell/core/flow/CoreFlowTaskListSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + ipInfo.webapi_port + method
      } else {
        path = 'http://' + ipInfo.ip + ':' + ipInfo.webapi_port + method
      }
      const data = {
        station_id: this.$route.query.station_id
      }
      axios.post(path, data, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0 && defaultQuery.data.data !== '') {
            const flowTaskList = defaultQuery.data.data
            if (flowTaskList.length > 0) {
              this.stationFlowTaskList = flowTaskList
            }
          }
        })
        .catch(ex => {
          this.stationFlowTaskList = []
          this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    },
    handleOnOffLineSwitch() {
      var client_code = this.writePoint.tag_key.split('/')[0]
      var group_code = this.writePoint.tag_key.split('/')[1]
      var tag_code = this.writePoint.tag_key.split('/')[2]
      var tag_value = this.writePoint.tag_value
      if (this.aisMonitorMode === 'AIS-SERVER') {
        client_code = client_code + '_' + this.$route.query.station_code
      }
      var tag_key = client_code + '/' + group_code + '/' + tag_code
      // 先做判断
      var DeviceOnReadyTag = client_code + '/PlcStatus/DeviceOnReady'// 设备启动就绪(设备线上模式且启动)
      var readTagArray = []
      var readTag = {}
      readTag.tag_key = DeviceOnReadyTag
      readTagArray.push(readTag)
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              var DeviceOnReadyValue = result[0].tag_value === undefined ? '' : result[0].tag_value
              this.writeScadaValue(this.writePoint.tag_key, this.writePoint.tag_value)
              // 如果切成离线模式，取消执行取消流程图的接口，根据工位号查询所有执行中的流程图，全部取消
              if (this.writePoint.value === '0') {
                // 1.取消任务与缓存
                const query = {
                  user_name: Cookies.get('userName'),
                  station_id: this.currentStation.station_id
                }
                eapApsPlanCancel(query)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                    } else {
                      this.$message({ message: defaultQuery.msg, type: 'error' })
                    }
                  })
                  .catch(() => {
                    this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
                  })

                // 2.取消流程图
                if (this.stationFlowTaskList != null && this.stationFlowTaskList.length > 0) {
                  this.stationFlowTaskList.forEach(val => {
                    var method = '/cell/core/flow/CoreFlowCancel'
                    var path = ''
                    if (process.env.NODE_ENV === 'development') {
                      path = 'http://localhost:' + this.webapiPort + method
                    } else {
                      path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                    }
                    const data = {
                      me_flow_task_id: val.me_flow_task_id,
                      flow_task_status: 'AUTO_CANCEL'
                    }
                    axios
                      .post(path, data, {
                        headers: {
                          'Content-Type': 'application/json'
                        }
                      })
                      .then(res => {
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.data.code === 0) {
                          this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                        }
                      })
                      .catch(ex => {
                        this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + '：' + ex, type: 'error' })
                      })
                  })
                }
              }
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('lang_pack.hmiMain.abnormalError') + '：' + ex, type: 'error' })
        })
    },
    writeScadaValue(tag_key, tag_value) {
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + tag_key.split('/')[0]
      this.mqttClient.send(topic, sendStr)
    },
    connectMQTT(host, port, onConnected) {
      const key = `${port}`
      if (port && this.clients[key]) {
        return
      }
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      // Object.keys(this.configData).forEach(item => {
      //   console.log(item)
      // })
      // mqttClient.subscribe('', { qos: 0 }, error => {
      //   console.log(error)
      // })
      this.mqttClient = mqttClient
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        // const topic = message.destinationName
        const payload = message.payloadString
        const data = JSON.parse(payload)
        if (data && data.TagNewValue && data.TagNewValue !== '') {
          this.setContent(data.TagKey, data.TagNewValue)
        }
        if (data.Beat !== null || data.Beat !== undefined) {
          for (const i in this.configData.heartbeat) {
            const item = this.configData.heartbeat[i]
            if (item.tags.tag_key === data.ClientCode) {
              item.tags.tag_value = data.Beat
            }
          }
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    }
  }
}
</script>
<style lang="scss" scoped>
@import '~@/assets/styles/eap_board/public.scss';
@import '~@/assets/styles/dy/dialog_hmi.scss';
::v-deep .el-message{
  width: 500px;
  .el-message__icon{
    font-size: 24px;
  }}
</style>

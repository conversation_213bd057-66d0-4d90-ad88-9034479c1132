<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.panelID') }}
        </template>
        <el-input ref="webPanelNum" v-model="webPanelNum" clearable size="mini" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo('retry')">{{ $t('lang_pack.dialogMain.scanAgain') }}</el-button>
      <el-button type="primary" @click="handleSendInfo('pass')">{{ $t('lang_pack.dialogMain.forcePass') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      webPanelNum: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webPanelNum.focus()
    })
  },
  methods: {
    handleSendInfo(type) {
      console.log(this.tag_key_list)
      var WebPanelletConfirmModel = '0'
      var checkFlag = false
      var panelBarCode = ''
      if (type === 'pass') {
        if (this.webPanelNum === '') {
          this.$message({ message: 'Please Input PanelID', type: 'info' })
          return
        }
        WebPanelletConfirmModel = '3'
        if (this.tag_key_list.WebCheckFlag === 'N') {
          checkFlag = false
        } else {
          checkFlag = true
        }
        panelBarCode = this.webPanelNum
      } else if (type === 'retry') {
        WebPanelletConfirmModel = '2'
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebPanelNum,
        TagValue: this.webPanelNum
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPanelletConfirmModel,
        TagValue: WebPanelletConfirmModel
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPanelInfoRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebPanelNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr, checkFlag, panelBarCode)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>

<template>
  <div class="app-contanier">
    <el-form class="userInfo" @submit.native.prevent>
      <h3 class="title">{{ $t('lang_pack.dialogMain.roleCheck') }}</h3>
      <el-form-item>
        <el-input v-model="userForm.newPw" placeholder="Password" type="password" auto-complete="off" show-password @keyup.enter.native="checkrole">
          <div slot="prefix" style="margin-left: 3px"><i class="el-icon-lock" /></div></el-input>
        <span class="jianpan" @click="showKeyboard"><img :src="keyboard"></span>
      </el-form-item>
      <el-form-item class="wrapbtn">
        <el-button
          size="medium"
          type="warning"
          @click="checkcancel"
        ><i class="el-icon-close" />{{ $t('lang_pack.dialogMain.cancel') }}</el-button>
        <el-button
          :loading="loading"
          size="medium"
          type="primary"
          style="width: 65%"
          @click="checkrole"
        ><i class="el-icon-check" />{{ $t('lang_pack.dialogMain.confirm') }}</el-button>
      </el-form-item>
    </el-form>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </div>
</template>

<script>
import keyboard from '@/assets/images/keyboard.png'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
import Cookies from 'js-cookie'
import { sel } from '@/api/core/system/sysParameter'
export default {
  name: 'roleCheck',
  components: {
    SimpleKeyboard
  },
  props: {
    role_user_id: {
      type: String,
      default: ''
    },
    role_func_code: {
      type: String,
      default: ''
    },
    paramsCode: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      input: '',
      isShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      userForm: {
        newPw: '',
        user: '',
        isCover: true
      },
      loading: false
    }
  },
  mounted() {
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  methods: {
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
        // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
        // 删除键的相应操作
      }
      if (button === '{bksp}') {
        // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
          }
        }
      }
    },
    // 验证权限
    checkrole() {
      if (this.userForm.newPw === '' || this.userForm.newPw === undefined) {
        this.$notify.warning({
          title: 'prompt',
          message: 'Please Input Password',
          duration: 1000
        })
        return
      }
      // 查询系统参数
      const query = {
        user_name: Cookies.get('userName'),
        parameter_code: this.paramsCode ? this.paramsCode : 'Hmi_CheckPwd',
        enable_flag: 'Y'
      }
      sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const parasInfo = defaultQuery.data[0]
              var hmiPwd = parasInfo.parameter_val
              if (hmiPwd !== this.userForm.newPw) {
                this.userForm.newPw = ''
                this.$notify.error({
                  title: 'prompt',
                  message: 'Password Error Or No Roles',
                  duration: 1000
                })
                return
              }
              setTimeout(() => {
                this.$notify.success({
                  title: 'Check Success',
                  duration: 1000
                })
                this.userForm.newPw = ''
                this.$emit('roleCheck', this.role_func_code, 'OK')
              }, 500)
            } else {
              this.userForm.newPw = ''
              this.$message({ message: 'No System HMI Password', type: 'error' })
            }
          } else {
            this.userForm.newPw = ''
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.userForm.newPw = ''
          this.$message({ message: 'Exception', type: 'error' })
        })
    },
    // 取消验证
    checkcancel() {
      this.$emit('roleCheck', this.role_func_code, 'CANCEL')
    }
  }
}
</script>

<style lang="less" scoped>
.app-contanier {
  height: 100%;
  width: 100%;
  background-repeat: no-repeat;
  background-attachment: fixed;
  background-position: center;
  background-color: rgba(0, 0, 0, 0.3);
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  overflow: auto;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1500;
  .userInfo{
    position: fixed;
    bottom: 15%;
    right: 10%;
    border-radius: 10px;
    background: #ffffff;
    padding: 30px 25px;
    width: 380px;
    z-index: 13;
  .title {
  margin: 0 auto 40px auto;
  text-align: center;
  color: #18245e;
  font-size: 24px;
}
  }
  .wrapbtn{
    margin-top: 20px;
  }
}
@keyframes bubble-animate-1 {
  from {
    top: -120px;
  }

  50% {
    top: -180px;
  }

  to {
    top: -120px;
  }
}

@keyframes bubble-animate-2 {
  from {
    top: 50px;
    left: 34%;
  }

  50% {
    top: 80px;
    left: 24%;
  }

  to {
    top: 50px;
    left: 34%;
  }
}

@keyframes bubble-animate-3 {
  from {
    top: 50px;
    left: 48%;
  }

  50% {
    top: 80px;
    left: 58%;
  }

  to {
    top: 50px;
    left: 48%;
  }
}
.keyboard-mask{
    position: fixed;
    bottom: 15%;
    left: 5%;
    z-index: 999;
}
::v-deep .hg-theme-default .hg-button.hg-standardBtn {
    width: 24px !important;
    // height: 51px;
}
.jianpan img{
    width: 35px;
    margin-top: 10px;
    margin-left: 10px;
    cursor: pointer;
}
</style>


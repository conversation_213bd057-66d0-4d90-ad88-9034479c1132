// stationMonitorTlwx project English translation

export default {
  // stationMonitorTlwx project specific translations
  stationMonitorTlwx: {
    // Batch number related
    batchInput: {
      lotNo: 'Lot No',
      scanInput: 'Scan Input',
      manualInput: 'Manual Input',
      placeholder: 'Please enter or scan lot number',
      batchCheck: 'Batch Check',
      currentBatch: 'Current Batch',
      switchToManual: 'Switched to manual input mode',
      switchToScan: 'Switched to scan mode',
      pleaseInputLotNo: 'Please enter or scan lot number',
      batchCheckSent: 'Batch check request sent'
    },
    // Recipe Parameters Internationalization
    recipeParams: {
      title: 'Recipe Parameters',
      wheelbase: 'Wheelbase',
      load250ShortAxisTension: 'Load 250 Short Axis Tension',
      load500LongAxisTension: 'Load 500 Long Axis Tension',
      unLoad250ShortAxisTension: 'Unload 250 Short Axis Tension',
      unLoad500LongAxisTension: 'Unload 500 Long Axis Tension'
    }
  }
}
<template>
  <div>
    <div class="tag-group" style="margin-bottom:15px;text-align:center;">
      <el-tag
        v-for="item in showItems"
        :key="item.label"
        :type="item.type"
        style="margin-left:20px;font-size:x-large;"
        size="medium"
        effect="dark"
      >
        {{ item.label }}
      </el-tag>
    </div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.lotID') }}
        </template>
        <el-input ref="webLotNum" v-model="webLotNum" clearable size="mini" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo()">{{ $t('lang_pack.dialogMain.confirm') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    },
    lot_num: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      webLotNum: '',
      showItems: [
        { type: 'success', label: 'No Mix|' },
        { type: 'success', label: 'Normal Out' }
      ]
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webLotNum.focus()
    })
    var ngPanelCount = this.tag_key_list.NgPanelCount
    var outErrorCode = this.tag_key_list.OutErrorCode
    if (ngPanelCount && outErrorCode) {
      this.showItems = []
      var jsonItem1 = {}
      var jsonItem2 = {}
      if (ngPanelCount !== '0') {
        jsonItem1.type = 'danger'
        jsonItem1.label = 'Exist Mix'
        this.showItems.push(jsonItem1)
      } else {
        jsonItem1.type = 'success'
        jsonItem1.label = 'No Mix'
        this.showItems.push(jsonItem1)
      }
      if (outErrorCode === '1') {
        jsonItem2.type = 'success'
        jsonItem2.label = 'Normal Out'
      } else if (outErrorCode === '2') {
        jsonItem2.type = 'danger'
        jsonItem2.label = 'Short Out'
        this.showItems.push(jsonItem2)
      } else if (outErrorCode === '3') {
        jsonItem2.type = 'danger'
        jsonItem2.label = 'Multy Out'
        this.showItems.push(jsonItem2)
      } else {
        jsonItem2.type = 'danger'
        jsonItem2.label = 'Force Out'
        this.showItems.push(jsonItem2)
      }
    }
  },
  methods: {
    handleSendInfo() {
      if (this.webLotNum === '') {
        this.$message({ message: 'Please Scan LotID', type: 'info' })
        return
      }
      if (this.webLotNum !== this.lot_num) {
        this.$message({ message: 'Scan LotID Compare Need LotID{' + this.lot_num + '}Inconsistent', type: 'info' })
        return
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebLotRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebLotRequest.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>

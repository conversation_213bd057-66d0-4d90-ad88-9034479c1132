<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard header">
      <el-header>
        <div class="statuHead">
          <div>
            <el-popover placement="right" width="170" trigger="click">
              <el-button
                v-for="(item, index) in [
                  {
                    tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                    tag_value: '0',
                    label: this.$t('zhcy.offlineMode'),
                  },
                  {
                    tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                    tag_value: '2',
                    label: this.$t('zhcy.onlineLocal'),
                  },
                  {
                    tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                    tag_value: '1',
                    label: $t('tlps.index.onlineRemote'),
                  },
                ]"
                :key="index"
                size="medium"
                type="primary"
                style="font-size: 20px"
                :style="{ margin: index === 1 ? '10px 0' : '0px' }"
                @click="handleWrite(item.tag_key, item.tag_value)"
              >{{ item && item.label }}</el-button><br>
              <el-button
                slot="reference"
                :class="
                  monitorData.ControlMode.value === '2' ||
                    monitorData.ControlMode.value === '3'
                    ? 'btnone'
                    : 'btnone0'
                "
              >{{
                controlData[monitorData.ControlMode.value] || $t('zhcy.offlineMode')
              }}</el-button>
            </el-popover>
            <el-popover placement="right" width="200" trigger="click">
              <el-button
                v-for="(item, index) in [
                  {
                    tag_key: `${stationAttr}Plc/PlcStatus/ProductMode`,
                    tag_value: '0',
                    label: $t('lang_pack.wx.productionMode'),
                  },
                  {
                    tag_key: `${stationAttr}Plc/PlcStatus/ProductMode`,
                    tag_value: '1',
                    label: $t('lang_pack.wx.firstArticleMode'),
                  },
                  {
                    tag_key: `${stationAttr}Plc/PlcStatus/ProductMode`,
                    tag_value: '2',
                    label: $t('lang_pack.wx.dummyMode'),
                  },
                ]"
                :key="index"
                size="medium"
                type="primary"
                style="font-size: 20px"
                :style="{ margin: index === 1 ? '10px 0' : '0px' }"
                @click="handleWrite(item.tag_key, item.tag_value)"
              >{{ item && item.label }}</el-button><br>
              <el-button
                slot="reference"
                :class="
                  monitorData.ProductMode.value === '1' ||
                    monitorData.ProductMode.value === '2'
                    ? 'btnone'
                    : 'btnone0'
                "
              >{{
                controlStatusData[monitorData.ProductMode.value] && controlStatusData[monitorData.ProductMode.value].label ||
                  $t('lang_pack.wx.productionMode')
              }}</el-button>
            </el-popover>
          </div>
          <div>
            <div class="wrappstyle">
              <p>
                <span
                  :class="
                    monitorData.LightGreen.value === '1'
                      ? 'wholeline1 wholelinenormal1'
                      : monitorData.LightYellow.value === '1'
                        ? 'wholeline1 wholelineerror1'
                        : monitorData.LightRed.value === '1'
                          ? 'wholeline1 deviceRed'
                          : 'wholeline1 wholelinegray1'
                  "
                />
                <span class="statuText">{{
                  $t("lang_pack.vie.triColorLight")
                }}</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.PlcHeartBeat.value === '1'
                      ? 'wholeline wholelinenormal'
                      : monitorData.PlcHeartBeat.value === '0'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">{{ $t('lang_pack.wx.plcHeartbeat') }}</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.UpLink.value === '0'
                      ? 'wholeline wholelinenormal'
                      : monitorData.UpLink.value === '1'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">{{ $t('lang_pack.wx.upstream') }}</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.DownLink.value === '0'
                      ? 'wholeline wholelinenormal'
                      : monitorData.DownLink.value === '1'
                        ? 'wholeline wholelineerror'
                        : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">{{ $t('lang_pack.wx.downstream') }}</span>
              </p>
              <p>
                <span style="font-size: 14px; margin-bottom: 4px">
                  {{
                    monitorData.ElecConSum.value
                      ? monitorData.ElecConSum.value + "%"
                      : "100%"
                  }}
                </span>
                <span class="statuText">{{ $t('lang_pack.wx.electricity') }}</span>
              </p>
            </div>
          </div>
        </div>
        <transition name="el-zoom-in-center">
          <span v-show="messageShow" :class="'message message-' + MessageLevel"><i
            :class="MessageLevel === 'warning' ? 'el-icon-warning' : MessageLevel === 'error' ? 'el-icon-error' : 'el-icon-info'"
          />&nbsp;{{
            messageContent }}</span>
        </transition>
      </el-header>
    </el-card>
    <el-row
      :gutter="20"
      style="margin-right: 0px; padding: 0px; margin-top: 10px"
    >
      <el-col :span="16" style="padding-right: 0">
        <el-col :span="24" style="padding: 0">
          <!-- <el-col :span="12" style="padding: 0 5px 0 0;"> -->
          <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="80px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12 barcode">
                    <el-form-item :label="$t('lang_pack.wx.lotNum') + ':'">
                      <el-input
                        ref="lot_no"
                        v-model="lot_no"
                        clearable
                        size="small"
                        @input="handleLotNoInput"
                      />
                    </el-form-item>
                    <el-button
                      type="primary"
                      style="width: 80px"
                      @click="manInput()"
                    >{{ $t('lang_pack.vie.scan') }}</el-button>
                  </div>
                </div>

                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item :label="$t('lang_pack.vie.quantity') + ':'">
                      <el-input
                        ref="lot_count"
                        v-model="lot_count"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>
                <!--料号-->
                <div class="wrapElFormFirst col-md-4 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                      <el-input
                        v-model="part_no"
                        clearable
                        size="small"
                      />
                    </el-form-item>
                  </div>
                </div>
                <!--板号-->
                <div class="wrapElFormFirst col-md-4 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item :label="$t('lang_pack.hmiMain.panelID') + ':'">
                      <el-input
                        v-model="panel_id"
                        clearable
                        size="small"
                        @input="handlePanelIdInput"
                      />
                    </el-form-item>
                  </div>
                </div>
                <!--员工号-->
                <div class="wrapElFormFirst col-md-4 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item :label="$t('lang_pack.hmiMain.employee') + ':'">
                      <el-input
                        ref="user.nickName"
                        v-model="user.nickName"
                        clearable
                        size="small"
                        @input="handleUserNameInput"
                      />
                    </el-form-item>
                  </div>
                </div>
              </div>
            </el-form>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>{{ $t('lang_pack.wx.productInfo') }}</span>
            </div>
            <div style="overflow-x: auto; min-width: 100%; width: 100%;">
              <el-table ref="table" :data="[tableData]" border height="140" :style="{ minWidth: tableMinWidth }">
                <el-table-column
                  v-for="(item,index) in groupData"
                  :key="index"
                  :show-overflow-tooltip="true"
                  align="center"
                  :width="item.tag_des.length >= 5 ? '190' : ''"
                  :prop="item.value"
                  :label="item.tag_des"
                >
                  <template slot-scope="scope">
                    {{ item.value }} <!-- 显示对应value -->
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <el-card shadow="never" class="wrapCard">
              <div slot="header" class="wrapTextSelect">
                <span>{{ $t('lang_pack.wx.alarmMsg') }}</span>
              </div>
              <el-table
                ref="table"
                border
                :data="alarmData"
                :row-key="(row) => row.id"
                :height="height"
              >
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="station_code"
                  :label="$t('lang_pack.wx.stationCode')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_code"
                  :label="$t('lang_pack.scadaAlarmReport.instanceCode')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_des"
                  :label="$t('lang_pack.scadaAlarmReport.instanceDesc')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_code"
                  :label="$t('lang_pack.wx.alarmCode')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_level"
                  :label="$t('lang_pack.wx.alarmLevel')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_des"
                  :label="$t('lang_pack.wx.alarmDesc')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="item_date"
                  :label="$t('lang_pack.wx.alarmTime')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_date"
                  :label="$t('lang_pack.scadaAlarmReport.resetTime')"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_flag"
                  :label="$t('lang_pack.scadaAlarmReport.resetFlag')"
                >
                  <template slot-scope="scope">
                    {{ scope.row.reset_flag === "Y" ? $t('lang_pack.scadaAlarmReport.resetYes') : $t('lang_pack.scadaAlarmReport.resetNo') }}
                  </template>
                </el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="simulated_flag"
                  :label="$t('lang_pack.scadaAlarmReport.isSimulated')"
                >
                  <template slot-scope="scope">
                    {{ scope.row.simulated_flag === "Y" ? $t('lang_pack.scadaAlarmReport.simulatedYes') : $t('lang_pack.scadaAlarmReport.simulatedNo') }}
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-card></el-col>
      </el-col>
      <el-col :span="8" style="padding-right: 0">
        <el-card shadow="never" class="wrapCard">
          <div class="pieChart">
            <div id="capacityDom" />
            <!-- 产能 -->
            <div id="oeeDom" />
            <!-- oee -->
            <div id="readbitRateDom" />
            <!-- 读码率 -->
          </div>
          <el-table
            ref="table"
            border
            :data="plcCraftData"
            :row-key="(row) => row.id"
            :height="gatherHeight"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              :label="$t('lang_pack.wx.projectName')"
              prop="tag_des"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="tag_value"
              :label="$t('lang_pack.wx.currentValue')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="unit"
              :label="$t('lang_pack.wx.unit')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="upper_limit"
              :label="$t('lang_pack.wx.upperLimit')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="down_limit"
              :label="$t('lang_pack.wx.lowerLimit')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="status"
              :label="$t('lang_pack.wx.state')"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :show-close="true"
      :title="$t('lang_pack.wx.modifyFormula')"
      width="80%"
      :visible.sync="dialogVisible"
      class="dialogTable"
    >
      <div
        style="
          margin-bottom: 10px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div v-if="$route.query.station_code !=='OP1000'" style="width: 50%;">
          <el-select v-model="recipeValue" @change="handleChange">
            <el-option v-for="item in recipeSelectData" :key="item.value" :label="item && item.label" :value="item.value" />
          </el-select>
          <span>{{ (thickness / 39.37).toFixed(2) }} +</span>
          <el-input v-model.number="surfaceValue" type="number" style="width: 150px;margin-left: 10px;" />
          <span style="margin: 0 10px;">=</span>
          <span>{{ parseFloat((thickness / 39.37).toFixed(2)) + surfaceValue }}  {{ 'mm' }}</span>
        </div>
        <div v-else style="width: 50%;">
          <span>{{ $t('lang_pack.wx.thickness') }}：</span>
          <span>{{ parseFloat((thickness / 39.37).toFixed(2)) }}  {{ 'mm' }}</span>
        </div>
        <div>
          <span>{{ $t('lang_pack.wx.modifyParams') }}：</span>
          <el-switch
            v-model="disabled"
            active-color="#13ce66"
            inactive-color="#ff4949"
          />
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="crud.loading"
        border
        size="small"
        :data="crud.data"
        style="width: 100%"
        :cell-style="crud.cellStyle"
        height="478"
        max-height="478"
        highlight-current-row
        @header-dragend="crud.tableHeaderDragend()"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="45" align="center" />
        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
        <el-table-column
          v-if="1 == 0"
          width="10"
          prop="recipe_detail_id"
          label="id"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="parameter_code"
          :label="$t('lang_pack.wx.parameterCode')"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_des"
          :label="$t('lang_pack.wx.parameterDesc')"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_val"
          :label="$t('lang_pack.wx.parameterValue')"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.parameter_val"
              :disabled="handleDisabled(scope.row.parameter_val)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('lang_pack.commonPage.validIdentificationt')"
          align="center"
          prop="enable_flag"
          width="100"
        >
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ scope.row.enable_flag === "Y" ? $t('lang_pack.vie.effective') : $t('lang_pack.vie.invalid') }}
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('lang_pack.wx.cancel') }}</el-button>
        <el-button type="primary" @click="handleOk">{{ $t('lang_pack.wx.confirm') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="$t('lang_pack.hmiMain.CIMMessage')" width="50%" top="20px" :visible.sync="dialogCIMMsgVisible" :close-on-click-modal="false" class="elDialog">
      <table class="table">
        <tr>
          <td class="label" style="width:100px;">{{ $t('lang_pack.messageReport.screen_code') + '：' }}</td>
          <td class="content">{{ screen_code }}</td>
        </tr>
        <tr>
          <td class="label">{{ $t('lang_pack.messageReport.cim_msg') + '：' }}</td>
          <td class="content">{{ cim_msg }}</td>
        </tr>
        <tr v-if="ConfirmBtnVisible">
          <td colspan="2" style="text-align:center;">
            <el-button
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-check"
              @click="handleConfirmCIMMsg"
            >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          </td>
        </tr>
      </table>
    </el-dialog>
  </div>
</template>
<script>

import { getWorkOrderInfo as selOrderInfo } from '@/api/eap/project/tlhs/eapRecipe'
import { selScadaTag } from '@/api/core/scada/tag'
import crudEapRecipeDetail from '@/api/eap/project/sfjd/eapRecipeDetail'
import { scadaTagGroupTree } from '@/api/core/scada/tagGroup'

import { eapCimMsgShow } from '@/api/eap/eapApsPlan'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import axios from 'axios'
import { selCellIP } from '@/api/core/center/cell'
import { sel as selStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { mapGetters } from 'vuex'
import { handleRecipeSyncAfterDispatch } from '@/api/eap/core/recipeSyncApi'
const defaultForm = {}
export default {
  name: 'shRecipeMain',
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 548,
      gatherHeight: document.documentElement.clientHeight - 424,
      lot_no: '',
      lot_count: '0',
      current_user: '',
      part_no: '',
      panel_id: '',
      recipeData: [],
      capacityDom: null,
      oeeDom: null,
      readbitRateDom: null,
      capacityOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: this.$t('lang_pack.wx.capacity'),
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#6df320', '#d2e312'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: this.$t('lang_pack.wx.leave') },
              { value: 234, name: this.$t('lang_pack.wx.onDuty') }
            ]
          }
        ]
      },
      oeeOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: 'OEE',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#409EFF', '#40e2ff'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: this.$t('lang_pack.wx.leave') },
              { value: 234, name: this.$t('lang_pack.wx.onDuty') }
            ]
          }
        ]
      },
      readbitRateOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: this.$t('lang_pack.wx.readBitRate'),
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#9d9727', '#c25b1f'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: this.$t('lang_pack.wx.leave') },
              { value: 234, name: this.$t('lang_pack.wx.onDuty') }
            ]
          }
        ]
      },
      dialogVisible: false,
      rules: {
      },
      disabled: false,
      controlStatus: {
        light_status: '',
        device_plc_status: '',
        ais_status: '0',
        plc_status: '0',
        eap_status: '0'
      },
      controlData: {
        1: this.$t('zhcy.offlineMode'),
        2: this.$t('zhcy.onlineLocal'),
        3: this.$t('zhcy.onlineRemote')
      },
      controlStatusData: [
        { id: '0', label: this.$t('lang_pack.wx.productionMode') },
        { id: '1', label: this.$t('lang_pack.wx.firstArticleMode') },
        { id: '2', label: this.$t('lang_pack.wx.dummyMode') }
      ],
      monitorData: {
        UpLink: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'UpLink',
          tag_des: '	[PlcStatus]上游设备连接',
          value: '0'
        },
        DownLink: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'DownLink',
          tag_des: '[PlcStatus]下游设备连接',
          value: '0'
        },
        PlcHeartBeat: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'HeartBeat',
          tag_des: '[PlcStatus]PLC心跳',
          value: '0'
        },
        ElecConSum: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'ElecConSum',
          tag_des: '[PlcStatus]设备用电量',
          value: '0'
        },
        LightGreen: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightGreen',
          tag_des: '[PlcStatus]三色灯绿',
          value: '0'
        },
        LightYellow: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightYellow',
          tag_des: '[PlcStatus]三色灯红',
          value: '0'
        },
        LightRed: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightRed',
          tag_des: '[PlcStatus]设备用电量',
          value: '0'
        },
        ControlMode: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'DeviceMode',
          tag_des: '[PlcStatus]设备控制状态(1:离线,2:在线/本地,3:在线/远程)',
          value: '0'
        },
        ProductMode: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'ProductMode',
          tag_des: '[PlcStatus]设备生产模式(0:量产,1:首件,2:Dummy)',
          value: '0'
        },
        RecipeUpd: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'RecipeUpd',
          tag_des: '[PlcStatus]配方修改',
          value: '0'
        },
        RecipeSelFinish: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'RecipeSelFinish',
          tag_des: '[PlcStatus]配方选择完成',
          value: '0'
        },
        PartNo: {
          client_code: `Ais`,
          group_code: 'AisStatus',
          tag_code: 'PartNo',
          tag_des: '[AisStatus]料号',
          value: ''
        }
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      alarmData: [],
      timer: null,
      groupData: [],
      productData: [],
      plcCraftData: [],
      stationAttr: '',
      tableData: {},
      thickness: 0,
      recipeValue: 7,
      surfaceValue: 7,
      recipeSelectData: [
        { label: '选择填孔+', value: 7 },
        { label: '选择表面+', value: 11 }
      ],
      // CIM消息相关
      queryCim: true,
      dialogCIMMsgVisible: false,
      ConfirmBtnVisible: false,
      screen_code: '',
      cim_msg: '',
      messageList: [],
      messageShow: false,
      messageContent: '',
      MessageLevel: 'info',
      // 员工号输入防抖定时器
      userNameInputTimer: null,
      // 板号输入防抖定时器
      panelIdInputTimer: null,
      // 批次号输入防抖定时器
      lotNoInputTimer: null
    }
  },
  cruds() {
    return CRUD({
      title: '配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_detail_id ',
      // 排序
      sort: ['recipe_detail_id asc'],
      // CRUD Method
      crudMethod: { ...crudEapRecipeDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  dicts: ['PROJECT_PARAMS_WARNING'],
  computed: {
    ...mapGetters(['user']),
    // 动态计算表格最小宽度
    tableMinWidth() {
      if (this.groupData.length === 0) return '800px'
      const totalWidth = this.groupData.reduce((total, item) => {
        const width = item.tag_des.length >= 5 ? 190 : 120
        return total + width
      }, 0)
      return Math.max(totalWidth, 800) + 'px'
    }
  },
  mounted: function() {
    // 首先更根据工位号查询，获取到所要查询的点位实例
    this.getStationAttr()
    const that = this

    this.timer = setInterval(this.getAlarmData, 15000)
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 548
      that.gatherHeight = document.documentElement.clientHeight - 424
    }
    this.timer = setInterval(this.messageScroll, 5000)
    this.$nextTick(() => {
      this.getCapacity()
      this.getOee()
      this.getreadbitRate()
    })
  },
  beforeDestroy() {
    clearInterval(this.timer)
    // 清理员工号输入防抖定时器
    if (this.userNameInputTimer) {
      clearTimeout(this.userNameInputTimer)
    }
    // 清理板号输入防抖定时器
    if (this.panelIdInputTimer) {
      clearTimeout(this.panelIdInputTimer)
    }
    // 清理批次号输入防抖定时器
    if (this.lotNoInputTimer) {
      clearTimeout(this.lotNoInputTimer)
    }
  },
  created() {
    this.getCellIp()
  },
  methods: {
    handleChange(val) {
      this.surfaceValue = val
    },
    // 员工号输入处理
    handleUserNameInput(value) {
      // 防抖处理，避免频繁触发
      if (this.userNameInputTimer) {
        clearTimeout(this.userNameInputTimer)
      }
      this.userNameInputTimer = setTimeout(() => {
        if (value) {
          // 触发写入方法
          this.handleWrite(`${this.stationAttr}Ais/AisStatus/CurrentUser`, value)
        }
      }, 1500) // 1500ms防抖
    },
    // 板号输入处理
    handlePanelIdInput(value) {
      // 防抖处理，避免频繁触发
      if (this.panelIdInputTimer) {
        clearTimeout(this.panelIdInputTimer)
      }
      this.panelIdInputTimer = setTimeout(() => {
        if (value) {
          // 触发写入方法
          this.handleWrite(`${this.stationAttr}Ais/AisStatus/PanelID`, value)
        }
      }, 1500) // 1500ms防抖
    },
    // 批次号输入处理
    handleLotNoInput(value) {
      // 防抖处理，避免频繁触发
      if (this.lotNoInputTimer) {
        clearTimeout(this.lotNoInputTimer)
      }
      this.lotNoInputTimer = setTimeout(() => {
        if (value) {
          // 触发写入方法
          this.handleWrite(`${this.stationAttr}Ais/AisStatus/LotNo`, value)
        }
      }, 1500) // 1500ms防抖
    },
    getStationAttr() {
      const query = {
        stationCodeDes: this.$route.query.station_code,
        user_name: Cookies.get('userName')
      }
      selStation(query).then(res => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.stationAttr = res.data[0].station_attr
            Object.keys(this.monitorData).forEach((key) => {
              this.monitorData[key].client_code = `${this.stationAttr}` + this.monitorData[key].client_code
            })
            this.getScadaData()
            return
          }
          this.stationAttr = ''
        }
      })
    },
    getScadaData() {
      const query = {
        client_id: 1010,
        enable_flag: 'Y',
        sort: 'tag_group_id',
        user_name: Cookies.get('userName')
      }
      scadaTagGroupTree(query).then((res) => {
        if (res.data.length > 0) {
          const data = res.data.find((item) => item.tag_group_code === 'PcRecipe')
          if (data && data.children.length > 0) {
            data.children.forEach((e) => {
              this.groupData.push({
                tag_key: `${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`,
                tag_des: e.tag_code + "|" + e.tag_des ,
                value: ''
              })
            })
            this.tableData = data.children.reduce((acc, e) => {
              acc[`${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`] = ''
              return acc
            }, {})
            // 确保表格在数据更新后重新计算布局
            this.$nextTick(() => {
              if (this.$refs.table) {
                this.$refs.table.doLayout()
              }
            })
          }
        }
      })
      const params = {
        tableOrder: 'asc',
        tableOrderField: 'tag_id',
        tablePage: 1,
        tableSize: 1000,
        tag_group_id: 101003,
        user_name: Cookies.get('userName')
      }
      selScadaTag(params).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              this.dict.PROJECT_PARAMS_WARNING.forEach((e) => {
                if (`${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}` === e.value) {
                  const result = {
                    tag_des: item.tag_code,
                    tag_key: `${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}`,
                    tag_value: '',
                    unit: item.opc_addr,
                    down_limit: item.down_limit,
                    upper_limit: item.upper_limit,
                    status: ''
                  }
                  this.plcCraftData.push(result)
                }
              })
            })
          }
        }
      })
    },
    // CIM消息处理功能
    handleConfirmCIMMsg() {
      this.queryCim = true
      this.dialogCIMMsgVisible = false
    },
    messageScroll() {
      if (this.messageList.length > 0) {
        this.messageShow = false
        setTimeout(() => {
          this.messageShow = true
          this.messageList.push(this.messageList[0]) // 将数组的第一个元素追加到数组最后面
          this.messageList.shift() // 然后删除数组的第一个元素
          this.messageContent = this.messageList[0].content
          this.MessageLevel = this.messageList[0].level
        }, 300)
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      }
      if (this.queryCim) {
        eapCimMsgShow(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.count !== 0) {
                var msgInfo = defaultQuery.data[0]
                this.screen_code = msgInfo.screen_code
                this.cim_msg = msgInfo.cim_msg
                var interval_second_time = msgInfo.interval_second_time
                if (msgInfo.screen_control === '0') {
                  this.ConfirmBtnVisible = false
                  var _time = setTimeout(() => {
                    this.queryCim = true
                    this.dialogCIMMsgVisible = false
                    clearTimeout(_time)
                  }, interval_second_time * 1000)
                } else {
                  this.ConfirmBtnVisible = true
                }
                this.queryCim = false
                this.dialogCIMMsgVisible = true
              }
            }
          })
          .catch(() => {
          })
      }
      // this.getLoginInfo()
    },


    manInput() {
      if (!this.lot_no) {
        this.$message({ type: 'warning', message: this.$t('lang_pack.wx.pleaseScan') })
        return
      }
      var queryParameter = {
        user_name: Cookies.get('userName'),
        station_code: this.$route.query.station_code,
        make_order: this.lot_no,
        user_id: this.user.nickName
      }
      selOrderInfo(queryParameter)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              this.crud.data = defaultQuery.data
              const pointVal = this.$route.query.station_code === 'OP1000' ? 'Paras41' : 'Paras25'
              const data = defaultQuery.data.filter(item => item.tag_key === `${this.stationAttr}Plc/PcRecipe/${pointVal}`)
              if (data.length > 0) {
                this.thickness = Number(data[0].parameter_val)
              }
            }
            this.dialogVisible = true
          } else {
            // 处理API返回的错误信息
            this.thickness = 0
            const errorMsg = defaultQuery.msg || defaultQuery.error || this.$t('lang_pack.vie.queryException')
            this.$message({
              message: errorMsg,
              type: 'error',
              showClose: true,
              duration: 5000
            })
            console.error('GetWorkOrderInfo API错误:', defaultQuery)
          }
        })
        .catch((error) => {
          this.thickness = 0
          // 处理网络异常或其他错误
          let errorMsg = this.$t('lang_pack.vie.queryException')
          if (error && error.response && error.response.data) {
            const errorData = error.response.data
            errorMsg = errorData.msg || errorData.error || errorMsg
          } else if (error && error.message) {
            errorMsg = error.message
          }
          this.$message({
            message: errorMsg,
            type: 'error',
            showClose: true,
            duration: 5000
          })
          console.error('GetWorkOrderInfo网络错误:', error)
        })
    },
    choiceLotNum() {
      this.dialogVisible = true
    },
    handleDisabled(value) {
      if (this.disabled) return false
      const Arr = ['null', '', 'undefined', '0']
      if (!value || Arr.includes(value)) return true
    },
    handleWrite(key, value) {
      console.log(this.monitorData)
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: key,
        TagValue: value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + key.split('/')[0]
      this.sendMessage(topic, sendStr)
    },
    handleOk() {
      var rowJson = []
      var sendJson = {}
      var sendStr = {}
      var topic = ''
      // 1.读取plc修改配方，判断是否允许下发配方
      if (this.monitorData.RecipeUpd.value === '0') {
        this.dialogVisible = false
        this.$message({ type: 'warning', message: this.$t('tlps.index.deviceRunning') })
        var newRow = {
          TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
          TagValue: '0'
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        sendStr = JSON.stringify(sendJson)
        topic = `SCADA_WRITE/${this.stationAttr}Plc`
        this.sendMessage(topic, sendStr)
        return
      }

      const writeValue = (item) => {
        const code = this.$route.query.station_code
        const pointVal = code === 'OP1000' ? 'Paras41' : 'Paras25'
        if (code === 'OP1000' && item.tag_key === `${this.stationAttr}Plc/PcRecipe/${pointVal}`) {
          return parseFloat((this.thickness / 39.37).toFixed(2))
        } else if (code !== 'OP1000' && item.tag_key === `${this.stationAttr}Plc/PcRecipe/${pointVal}`) {
          return (parseFloat((this.thickness / 39.37).toFixed(2)) + this.surfaceValue)
        } else {
          return item.parameter_val
        }
      }
      // 2.请求下发配方
      this.crud.data.forEach((item) => {
        var newRow2 = {
          TagKey: item.tag_key,
          TagValue: writeValue(item)
        }
        rowJson.push(newRow2)
      })
      var newRow1 = {
        TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
        TagValue: '1'
      }

      rowJson.push(newRow1)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      sendStr = JSON.stringify(sendJson)
      topic = `SCADA_WRITE/${this.stationAttr}Plc`
      this.sendMessage(topic, sendStr)

      // 保存配方数据用于后续同步
      const recipeDataForSync = [...rowJson]

      // 3.间隔3秒后进行复位
      rowJson = []
      sendJson = {}
      sendStr = {}
      var newRow3 = {
        TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
        TagValue: '0'
      }
      rowJson.push(newRow3)
      // 写入到网板间距点位里面去

      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      sendStr = JSON.stringify(sendJson)
      topic = `SCADA_WRITE/${this.stationAttr}Plc`
      setTimeout(() => {
        this.dialogVisible = false
        this.sendMessage(topic, sendStr)
        // 配方下发成功后，检查是否需要同步到其他设备
        this.handleRecipeSync(recipeDataForSync)
      }, 3000)
    },
    // 处理配方同步到其他设备
    async handleRecipeSync(recipeData) {
      try {
        const stationCode = this.$route.query.station_code
        const userName = Cookies.get('userName')

        // 调用配方同步工具函数
        const syncResult = await handleRecipeSyncAfterDispatch(stationCode, recipeData, userName)

        // 根据同步结果显示不同的提示信息
        if (!syncResult.needSync) {
          // 不需要同步，显示普通的配方下发成功提示
          this.$message({ type: 'success', message: this.$t('core.secsais.downRecipeSuccess') })
        } else if (syncResult.success) {
          // 需要同步且同步成功
          this.$message({ type: 'success', message: this.$t('core.secsais.downRecipeAndSyncSuccess') })
        } else {
          // 需要同步但同步失败
          this.$message({ type: 'success', message: this.$t('core.secsais.downRecipeSuccess') })
          // 延迟显示同步失败提示，避免弹窗重叠
          setTimeout(() => {
            this.$message({ type: 'warning', message: this.$t('core.secsais.recipeSyncFailed') + ': ' + (syncResult.error || '') })
          }, 1500)
        }
      } catch (error) {
        console.error('配方同步处理失败:', error)
        // 显示配方下发成功，但同步处理异常
        this.$message({ type: 'success', message: this.$t('core.secsais.downRecipeSuccess') })
        // 延迟显示同步失败提示，避免弹窗重叠
        setTimeout(() => {
          this.$message({ type: 'error', message: this.$t('core.secsais.recipeSyncFailed') })
        }, 1500)
      }
    },
    getCapacity() {
      this.capacityDom = this.$echarts.init(
        document.getElementById('capacityDom')
      )
      var that = this
      this.capacityDom.setOption(this.capacityOption)
      window.addEventListener('resize', function() {
        that.capacityDom.resize()
      })
    },
    getOee() {
      this.oeeDom = this.$echarts.init(document.getElementById('oeeDom'))
      var that = this
      this.oeeDom.setOption(this.oeeOption)
      window.addEventListener('resize', function() {
        that.oeeDom.resize()
      })
    },
    getreadbitRate() {
      this.readbitRateDom = this.$echarts.init(
        document.getElementById('readbitRateDom')
      )
      var that = this
      this.readbitRateDom.setOption(this.readbitRateOption)
      window.addEventListener('resize', function() {
        that.readbitRateDom.resize()
      })
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            setTimeout(() => {
              this.toStartWatch()
              this.getTagValue()
              this.getAlarmData()
            }, 1000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.wx.queryFailed'), type: 'error' })
        })
    },
    getAlarmData() {
      var method = '/cell/core/scada/CoreScadaAlarmSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      var queryData = {
        tablePage: 1,
        tableSize: 10
      }
      axios
        .post(path, queryData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: this.$t('lang_pack.wx.queryFailed') + '：' + ex, type: 'error' })
        })
    },
    getTagValue() {
      var readTagArray = []
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.$route.query.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      this.groupData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        })
      })
      this.plcCraftData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        })
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code =
                    client_code + '_' + this.$route.query.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter((item) => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
              result.forEach((e) => {
                this.plcCraftData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.tag_value = e.tag_value
                    if (
                      typeof +e.tag_value === 'number' &&
                      +e.tag_value >= item.down_limit &&
                      +e.tag_value <= item.upper_limit
                    ) {
                      item.status = 'OK'
                    } else {
                      item.status = 'NG'
                    }
                  }
                })
                this.groupData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.value = e.tag_value
                  }
                })
              })
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: this.$t('lang_pack.wx.queryFailed') + '：' + ex, type: 'error' })
        })
    },
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', (e) => {
        this.mqttConnStatus = true
        var aisClientCode = `${this.stationAttr}Ais`
        var plcClientCode = `${this.stationAttr}Plc`
        var eapClientCode = `${this.stationAttr}Eap`
        this.topicSubscribe('SCADA_STATUS/' + aisClientCode)
        this.topicSubscribe('SCADA_BEAT/' + aisClientCode)
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        this.topicSubscribe('SCADA_STATUS/' + eapClientCode)
        this.topicSubscribe('SCADA_BEAT/' + eapClientCode)
        this.groupData.forEach((item) => {
          this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
        })
        Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.$route.query.station_code
          }
          this.topicSubscribe(
            'SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code
          )
        })
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
        // 记录当前登录账户
        this.handleWrite(`${this.stationAttr}Ais/AisStatus/CurrentUser`, this.user.nickName)
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {})
      this.clientMqtt.on('close', () => {})
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            this.plcCraftData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.tag_value = jsonData.TagNewValue
                if (
                  typeof +jsonData.TagNewValue === 'number' &&
                  +jsonData.TagNewValue >= item.down_limit &&
                  +jsonData.TagNewValue <= item.upper_limit
                ) {
                  item.status = 'OK'
                } else {
                  item.status = 'NG'
                }
              }
            })

            Object.keys(this.monitorData).forEach((key) => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              if (this.aisMonitorMode === 'AIS-SERVER') {
                client_code =
                  client_code + '_' + this.$route.query.station_code
              }
              var tag_key = client_code + '/' + group_code + '/' + tag_code
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue
                // 特殊处理：当接收到PartNo的值时，填充到part_no输入框
                if (key === 'PartNo') {
                  this.part_no = jsonData.TagNewValue
                }
              }
            })
            this.groupData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.value = jsonData.TagNewValue
              }
            })
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
          // this.$message({ message: '写入成功', type: 'success' })
        } else {
          this.$message({ message: this.$t('view.dialog.operationFailed'), type: 'error' })
        }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.app-container {
  padding: 10px;
  ::v-deep .el-header {
    padding: 0;
  }
  .header {
    ::v-deep .el-card__body {
      padding: 10px 15px 0 !important;
    }
  }
  .wrapTextSelect {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrapElForm {
    display: flex;
    ::v-deep .barcode {
      display: flex;
      .el-form-item--small {
        width: 90%;
        margin-right: 10px;
      }
    }
  }
  .pieChart {
    width: 100%;
    display: flex;
    div {
      width: 33%;
    }
    #capacityDom {
      height: 300px;
    }
    #capacityDom {
      height: 300px;
    }
    #readbitRateDom {
      height: 300px;
    }
  }
  .active {
    ::v-deep .el-input__inner {
      background-color: #ffff00;
    }
  }
  .dialog-footer {
    text-align: center;
  }
  .statuHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrappstyle {
    display: flex;
    align-items: center;
    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 12px;
        font-weight: 700;
      }
      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }

    p:last-child {
      margin-right: 0 !important;
    }

    .el-divider--vertical {
      width: 2px;
      height: 2em;
    }
  }
  .btnone {
    background: #50d475;
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
  }

  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
    font-size: 18px;
  }

  .btnone:active {
    background: #13887c;
  }
  .wholeline {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }
  .wholeline1 {
    width: 20px;
    height: 20px;
  }
  .wholelinenormal1,
  .deviceGreen {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .deviceRed {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelineerror1,
  .deviceYellow {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #eeff00;
    box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelinegray1 {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .dialogTable {
    ::v-deep .el-dialog {
      margin-top: 5vh !important;
    }
  }
}
</style>
<style lang="scss" scoped>
@import '~@/assets/styles/dy/dialog_hmi.scss';
</style>

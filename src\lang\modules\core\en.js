// English translations for the core module

export default {
  // Add core module specific translations here
  core: {
    // SECS/AIS Recipe Management
    secsais: {
      maintenance: 'SECS/AIS Recipe Management',
      downRecipeSuccess: 'Recipe deployed successfully!',
      downRecipeAndSyncSuccess: 'Recipe deployed and synced to other devices successfully!',
      recipeSyncFailed: 'Recipe sync failed',
      modifyParameter: 'Modify Parameters',
      parameterCode: 'Parameter Code',
      parameterDes: 'Parameter Description',
      parameterVal: 'Parameter Value',
      parameterInput: 'Input Value',
      parameterUnit: 'Unit',
      parameterLimit: 'Limits',
      noRecipeSelected: 'No recipe selected or recipe details loading...',
      fetchRecipeDataFailed: 'Failed to fetch recipe data',
      valueTooLow: 'Input value is below the lower limit',
      valueTooHigh: 'Input value is above the upper limit',
      mqttNotConnected: 'Please connect to MQTT service first',
      modifyFailed: 'Modify failed',
      modifySuccess: 'Modify successful',
      serviceCell: 'Unable to get cell server information',
      mqttConnectSuccess: 'MQTT connection successful',
      mqttConnectionFailed: 'MQTT connection failed',
      getCellIPFailed: 'Failed to get cell IP',
      clientDes: 'Client Description',
      clientCode: 'Client Code',
      search: 'Search',
      reset: 'Reset',
      clientList: 'Instance List',
      refreshInstanceList: 'Refresh Instance List',
      online: 'Online',
      offline: 'Offline',
      modelList: 'Recipe List',
      recipeDetails: 'Recipe Details',
      stationCodeMissing: 'Station code missing, unable to query recipe model data',
      fetchModelDataFailed: 'Failed to fetch recipe model data',
      refreshingInstanceList: 'Refreshing instance list...',
      confirmDelete: 'Confirm deletion of {0} selected items?',
      prompt: 'Prompt',
      confirm: 'Confirm',
      cancel: 'Cancel'
    },

    // EAP Recipe Management
    recipe: {
      recipeDes: 'Recipe Description',
      recipeName: 'Recipe Name',
      versionNo: 'Version No.',
      recipeType: 'Recipe Type',
      enableFlag: 'Enable Flag',
      deviceCode: 'Device Code',
      deviceDes: 'Device Description',
      materialCode: 'Material Code',
      materialDes: 'Material Description',
      isValid: 'Is Valid',
      operation: 'Operation',
      recipeTypeRequired: 'Please select recipe type',
      versionNoRequired: 'Please enter version number',
      recipeDesRequired: 'Please enter recipe description',
      recipeNameRequired: 'Please enter recipe name',
      materialCodeRequired: 'Please enter material code',
      materialDesRequired: 'Please enter material description',
      deviceCodeRequired: 'Please enter device code',
      deviceDesRequired: 'Please enter device description',
      enableFlagRequired: 'Please select enable flag',
      copyRecipe: 'Copy Recipe',
      exportRecipe: 'Export Recipe',
      downloadTemplate: 'Download Template',
      importRecipe: 'Import Recipe',
      batchDelete: 'Batch Delete',
      copyRecipeName: 'Copy Recipe Name',
      selectRecipeToExport: 'Please select recipe to export',
      selectOneRecipeToExport: 'Please select one recipe to export',
      selectOneRecipeToCopy: 'Please select one recipe to copy',
      exportSuccess: 'Recipe exported successfully',
      exportFailed: 'Export recipe failed, please try again',
      templateDownloadSuccess: 'Template downloaded successfully',
      templateDownloadFailed: 'Download template failed, please try again',
      importSuccess: 'Recipe imported successfully',
      importFailed: 'Import recipe failed, please check file format and content',
      onlyXlsSupported: 'Only .xls file format is supported',
      confirmBatchDelete: 'Confirm to delete selected',
      dataItems: 'data items?',
      unit: 'Unit',
      minValue: 'Min Value',
      maxValue: 'Max Value',
      currentValue: 'Current Value',
      noLimit: 'No Limit'
    },

    // SCADA Data Change Report
    scadaReport: {
      scadaTagList: 'SCADA Tag List',
      searchTag: 'Search tags...',
      instance: 'Instance',
      tagGroup: 'Tag Group',
      tag: 'Tag',
      currentSelectedTag: 'Current Selected Tag:',
      tagMissingUnitInfo: 'This tag is missing unit information, please contact administrator',
      pleaseSelectTagFirst: 'Please select a tag from the left list first'
    }
  }
}

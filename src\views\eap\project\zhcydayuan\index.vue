<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard header">
      <el-header>
        <div class="statuHead">
          <div style="display: flex; align-items: center; flex-wrap: nowrap; width: 100%; justify-content: space-between;">
            <!-- 左侧元素组 -->
            <div style="display: flex; align-items: center;">
              <!-- 离线模式按钮 -->
              <el-popover placement="bottom" width="170" trigger="click">
                <el-button
                  v-for="(item, index) in [
                    {
                      tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                      tag_value: '0',
                      label: this.$t('zhcy.offlineMode'),
                    },
                    {
                      tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                      tag_value: '1',
                      label: this.$t('zhcy.onlineRemote'),
                    },
                    {
                      tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                      tag_value: '2',
                      label: this.$t('zhcy.onlineLocal'),
                    }
                  ]"
                  :key="index"
                  size="medium"
                  type="primary"
                  style="font-size: 20px"
                  :style="{ margin: index === 1 ? '10px 0' : '0px' }"
                  @click="handleWrite(item.tag_key, item.tag_value)"
                >{{ item.label }}</el-button><br>
                <el-button
                  slot="reference"
                  :class="monitorData.ControlMode.value === '1' ? 'btnone-remote' :
                          monitorData.ControlMode.value === '2' ? 'btnone-local' : 'btnone0'"
                  style="margin-right: 10px; font-size: 20px;"
                >{{ controlData[monitorData.ControlMode.value] || $t('zhcy.offlineMode') }}</el-button>
              </el-popover>

              <!-- 是否允许生产按钮 -->
              <el-button
                :class="monitorData.AllowWork.value === '1' ? 'btnone' : 'btnone0'"
                style="margin-right: 10px; font-size: 20px;"
                disabled
              >{{ monitorData.AllowWork.value === '1' ? $t('zhcy.productionAllowed') : $t('zhcy.productionNotAllowed') }}</el-button>

              <!-- 是否允许下发配方按钮 -->
              <el-button
                :class="monitorData.ClearLine.value === '1' ? 'btnone' : 'btnone0'"
                style="margin-right: 10px; font-size: 20px;"
                disabled
              >{{ monitorData.ClearLine.value === '1' ? $t('zhcy.recipeUpdateAllowed') : $t('zhcy.recipeUpdateNotAllowed') }}
            </el-button>
              <!-- 同步账户信息按钮 -->
              <el-button
                type="primary"
                size="medium"
                class="sync-account-btn"
                style="margin-right: 10px; font-size: 20px;"
                @click="requestAccountInfo"
              >{{ $t('zhcy.syncAccountInfo') }}</el-button>

              <!-- 员工信息 -->
              <div class="peopleInfo" style="margin-right: 10px; display: flex; align-items: center;">
                <div style="display: flex; align-items: center; font-size: 16px;">
                  <span style="font-weight: bold;">{{ $t('zhcy.employeeId') }}：</span>
                  <span style="margin-right: 15px;">{{ loginInfo.user_name }}</span>
                  <span style="margin-right: 5px; color: #909399;">|</span>
                  <span style="font-weight: bold; margin-right: 5px;">{{ $t('zhcy.userName') }}：</span>
                  <span style="margin-right: 15px;">{{ loginInfo.nick_name }}</span>
                  <span style="margin-right: 5px; color: #909399;">|</span>
                  <span style="font-weight: bold; margin-right: 5px;">{{ $t('zhcy.permission') }}：</span>
                  <span>{{ loginInfo.permission === '1' ? $t('zhcy.employee') : loginInfo.permission === '2' ? $t('zhcy.engineer') : loginInfo.permission === '3' ? $t('zhcy.admin') : loginInfo.permission }}</span>
                </div>
              </div>
            </div>

            <!-- 右侧元素组 -->
            <div style="display: flex; align-items: center; margin-left: auto;">
              <!-- 登录/登出按钮 -->
              <div style="margin-right: 15px;">
                <el-button type="primary" style="margin-right: 5px;" @click="openUserLogin">{{ $t('zhcy.login') }}</el-button>
                <el-button type="danger" @click="handleUserLogout">{{ $t('zhcy.logout') }}</el-button>
              </div>

              <!-- 三色灯状态 -->
              <div class="status-indicator">
                <span
                  :class="monitorData.LightGreen.value === '1' ? 'wholeline1 wholelinenormal1' :
                          monitorData.LightYellow.value === '1' ? 'wholeline1 wholelineerror1' :
                          monitorData.LightRed.value === '1' ? 'wholeline1 deviceRed' :
                          monitorData.LightBlue.value === '1' ? 'wholeline1 deviceBlue' :
                          'wholeline1 wholelinegray1'"
                />
                <span class="statuText">{{ $t('zhcy.fourColorLight') }}</span>
              </div>

              <!-- PLC心跳状态 -->
              <div class="status-indicator" style="margin-left: 15px;">
                <span
                  :class="controlStatus.plc_status === '1' ? 'wholeline wholelinenormal' : controlStatus.plc_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('zhcy.plcHeartbeat') }}</span>
              </div>

              <!-- EAP状态 -->
              <div class="status-indicator" style="margin-left: 15px;">
                <span
                  :class="controlStatus.eap_status === '1' ? 'wholeline wholelinenormal' : controlStatus.eap_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('zhcy.eapStatus') }}</span>
              </div>

              <!-- 读头通讯状态 -->
              <div class="status-indicator" style="margin-left: 15px;">
                <span
                  :class="controlStatus.ccd_status === '1' ? 'wholeline wholelinenormal' : controlStatus.ccd_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray'"
                />
                <span class="statuText">{{ $t('zhcy.ccdStatus') }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-header>
    </el-card>
    <el-row :gutter="20" style="margin-right: 0px; padding: 0px; margin-top: 10px">
      <el-col :span="16" style="padding-right: 0">
        <el-col :span="24" style="padding: 0">
          <!-- <el-col :span="12" style="padding: 0 5px 0 0;"> -->
          <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="150px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-12 col-12">
                  <div class="formChild col-md-12 col-12 barcode">
                    <el-form-item :label="$t('zhcy.workOrderNumber') + '：'" class="pnl-input-item">
                      <div class="input-with-button">
                        <el-input
                          ref="qr_code"
                          v-model="qr_code"
                          clearable
                          size="large"
                          style="font-size: 20px;"
                          @keyup.enter.native="downloadRecipe"
                          @keydown.enter.prevent
                        />
                        <el-button
                          type="primary"
                          class="load-button"
                          style="width: 120px; font-size: 20px;"
                          :disabled="!monitorData.CurrentUser.value || monitorData.ClearLine.value !== '1'"
                          @click="downloadRecipe()"
                        >{{ $t('zhcy.downloadRecipe') }}</el-button>
                      </div>
                    </el-form-item>
                  </div>
                </div>
                <!--
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="料号：">
                      <el-select
                        v-model="query.client_code"
                        filterable
                        size="mini"
                        style="width: 200px"
                        :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                        @change="clientCodeSelect"
                      >
                        <el-option
                          v-for="(item, index) in materialData"
                          :key="index"
                          :label="item.material_code"
                          :value="item.material_code"
                        >
                          <span
                            style="float: left; color: #8492a6; font-size: 13px"
                          >{{ item.material_code }}</span>
                          <span style="float: right">{{
                            item.material_des
                          }}</span>
                        </el-option>
                      </el-select>
                    </el-form-item>
                  </div>
                </div>
                -->
              </div>
            </el-form>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>{{ $t('zhcy.taskList') }}</span>
              <div class="task-filter">
                <div class="status-checkboxes">
                  <el-checkbox v-model="statusFilters.PLAN" :label="$t('zhcy.planning')" size="medium" class="plan-checkbox" style="margin-right: 30px; font-size: 20px;" @change="() => handleStatusFilterChange('PLAN')"></el-checkbox>
                  <el-checkbox v-model="statusFilters.WORK" :label="$t('zhcy.inProduction')" size="medium" class="work-checkbox" style="margin-right: 30px; font-size: 20px;" @change="() => handleStatusFilterChange('WORK')"></el-checkbox>
                  <el-checkbox v-model="statusFilters.FINISH" :label="$t('zhcy.completed')" size="medium" class="finish-checkbox" style="margin-right: 30px; font-size: 20px;" @change="() => handleStatusFilterChange('FINISH')"></el-checkbox>
                  <el-checkbox v-model="statusFilters.CANCEL" :label="$t('zhcy.cancelled')" size="medium" class="cancel-checkbox" style="margin-right: 30px; font-size: 20px;" @change="() => handleStatusFilterChange('CANCEL')"></el-checkbox>
                </div>
                <el-button size="medium" type="primary" style="font-size: 20px; margin-left: 10px;" @click="fetchTaskList">{{ $t('zhcy.query') }}</el-button>
              </div>
            </div>
            <!-- 任务列表表格 -->
            <el-table
              class="task-list-table"
              border
              :data="apsPlanList"
              style="width: 100%;"
              height="225"
              v-loading="taskListLoading"
            >
              <el-table-column prop="plan_id" :label="$t('zhcy.taskName')" width="150">
                <template slot-scope="scope">
                  <span>{{ scope.row.plan_id }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="lot_status" :label="$t('zhcy.status')" width="100">
                <template slot-scope="scope">
                  <el-tag
                    :type="getStatusType(scope.row.lot_status)"
                    :style="{ color: scope.row.lot_status === 'WORK' ? '#67C23A' : scope.row.lot_status === 'FINISH' ? '#909399' : scope.row.lot_status === 'CANCEL' ? '#F56C6C' : '', fontWeight: 'bold', fontSize: '20px' }"
                  >
                    {{ getStatusText(scope.row.lot_status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="eqp_id" label="设备ID" width="150" /> -->
              <el-table-column prop="pn" :label="$t('zhcy.productNumber')" width="120" />
              <!-- <el-table-column prop="port_id" label="设备端口ID" width="150">
                <template slot-scope="scope">
                  <span :title="getPortIdDescription(scope.row.port_id)">{{ scope.row.port_id }}</span>
                </template>
              </el-table-column> -->
              <el-table-column prop="total_panel_count" :label="$t('zhcy.taskQuantity')" width="70" />
              <el-table-column prop="create_time" :label="$t('zhcy.createTime')" width="150">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.create_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="plan_start_time" :label="$t('zhcy.startTime')" width="150">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.plan_start_time) }}
                </template>
              </el-table-column>
              <el-table-column prop="plan_end_time" :label="$t('zhcy.endTime')" width="200">
                <template slot-scope="scope">
                  <div>{{ formatDateTime(scope.row.plan_end_time) }}</div>
                  <div v-if="scope.row.msg" class="task-message">
                    <i class="el-icon-warning-outline" style="color: #E6A23C; margin-right: 5px;"></i>
                    <span>{{ scope.row.msg }}</span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column :label="$t('zhcy.operations')" width="240" fixed="right">
                <template slot-scope="scope">
                  <!-- 启动任务按钮 - 仅在PLAN状态显示 -->
                  <el-button
                    v-if="scope.row.lot_status === 'PLAN'"
                    size="mini"
                    type="success"
                    @click.stop="handleStartTask(scope.row)"
                  >{{ $t('zhcy.start') }}</el-button>

                  <!-- 结束任务按钮 - 仅在WORK状态显示 -->
                  <el-button
                    v-if="scope.row.lot_status === 'WORK'"
                    size="mini"
                    type="info"
                    @click.stop="handleEndTask(scope.row)"
                  >{{ $t('zhcy.end') }}</el-button>

                  <!-- 取消任务按钮 -->
                  <el-button
                    v-if="scope.row.lot_status !== 'WORK' && scope.row.lot_status !== 'CANCEL' && scope.row.lot_status !== 'FINISH'"
                    size="mini"
                    type="warning"
                    @click.stop="handleCancelTask(scope.row)"
                  >{{ $t('zhcy.cancel') }}</el-button>

                  <!-- 删除任务按钮 -->
                  <el-button
                    v-if="scope.row.lot_status !== 'WORK' && scope.row.lot_status !== 'CANCEL' && scope.row.lot_status !== 'FINISH'"
                    size="mini"
                    type="danger"
                    @click.stop="handleDeleteTask(scope.row)"
                  >{{ $t('zhcy.delete') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-row :gutter="20">
              <el-col :span="24">
                <div style="margin-top: 5px;text-align:right;">
                  <el-button-group>
                    <el-button
                      type="primary"
                    >
                      {{ $t('zhcy.pagination.total') }}: {{ pagination.total }}</el-button>
                    <el-button
                      type="primary"
                    >
                      {{ $t('zhcy.pagination.current') }}{{ pagination.currentPage }}{{ $t('zhcy.pagination.unit') }}</el-button>
                    <el-button
                      type="primary"
                      @click="handlePageChange('prev')"
                      :disabled="pagination.currentPage <= 1"
                    >
                      &lt;&nbsp;{{ $t('zhcy.pagination.previous') }}</el-button>
                    <el-button
                      type="primary"
                      @click="handlePageChange('next')"
                      :disabled="pagination.currentPage >= pagination.totalPages"
                    >
                      {{ $t('zhcy.pagination.next') }}&nbsp;&gt;</el-button>
                  </el-button-group>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>{{ $t('zhcy.alarmInfo') }}</span>
            </div>
            <el-table
              ref="table"
              border
              :data="alarmData"
              :row-key="(row) => row.id"
              height="200"
            >
              <el-table-column
                :show-overflow-tooltip="true"
                prop="station_code"
                :label="$t('zhcy.station')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="client_code"
                :label="$t('zhcy.instanceCode')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="client_des"
                :label="$t('zhcy.instanceDesc')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="alarm_code"
                :label="$t('zhcy.alarmCode')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="alarm_level"
                :label="$t('zhcy.alarmLevel')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="alarm_des"
                :label="$t('zhcy.alarmDesc')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="item_date"
                :label="$t('zhcy.alarmTime')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="reset_date"
                :label="$t('zhcy.resetTime')"
              />
              <el-table-column
                :show-overflow-tooltip="true"
                prop="reset_flag"
                :label="$t('zhcy.isReset')"
              >
                <template slot-scope="scope">
                  {{ scope.row.reset_flag === "Y" ? $t('zhcy.resetYes') : $t('zhcy.resetNo') }}
                </template>
              </el-table-column>
              <el-table-column
                :show-overflow-tooltip="true"
                prop="simulated_flag"
                :label="$t('zhcy.isSimulated')"
              >
                <template slot-scope="scope">
                  {{ scope.row.simulated_flag === "Y" ? $t('zhcy.yes') : $t('zhcy.no') }}
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-col>
      <el-col :span="8" style="padding-right: 0">
        <el-card shadow="never" class="wrapCard">
          <div class="pieChart">
            <div id="capacityDom" />
            <div id="oeeDom" />
            <div id="readbitRateDom" />
          </div>
          <el-table
            ref="table"
            border
            :data="plcCraftData"
            :row-key="(row) => row.id"
            :height="gatherHeight"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              :label="$t('zhcy.projectName')"
              prop="tag_des"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="tag_value"
              :label="$t('zhcy.currentValue')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="unit"
              :label="$t('zhcy.unit')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="upper_limit"
              :label="$t('zhcy.upperLimit')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="down_limit"
              :label="$t('zhcy.lowerLimit')"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="status"
              :label="$t('zhcy.statusText')"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :show-close="true"
      :title="$t('zhcy.modifyRecipe')"
      width="80%"
      :visible.sync="dialogVisible"
      class="dialogTable"
    >
      <div style="margin-bottom: 10px; display: flex; justify-content: right; align-items: center;">
        <span>{{ $t('zhcy.isModifyParameters') }}：</span>
        <el-switch
          v-model="disabled"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </div>
      <el-table
        ref="table"
        v-loading="crud.loading"
        border
        size="small"
        :data="crud.data"
        style="width: 100%"
        :cell-style="crud.cellStyle"
        height="478"
        max-height="478"
        highlight-current-row
        @header-dragend="crud.tableHeaderDragend"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="45" align="center" />
        <el-table-column
          v-if="false"
          width="10"
          prop="recipe_detail_id"
          label="id"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="parameter_code"
          :label="$t('zhcy.parameterCodeLabel')"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_des"
          :label="$t('zhcy.parameterDescLabel')"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_val"
          :label="$t('zhcy.parameterValueLabel')"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.parameter_val"
              :disabled="handleDisabled(scope.row.parameter_val)"
            />
          </template>
        </el-table-column>
        <el-table-column
          :label="$t('zhcy.validFlagLabel')"
          align="center"
          prop="enable_flag"
          width="100"
        >
          <template slot-scope="scope">
            {{ scope.row.enable_flag === "Y" ? $t('zhcy.valid') : $t('zhcy.invalid') }}
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">{{ $t('zhcy.cancel') }}</el-button>
        <el-button type="primary" @click="handleOk">{{ $t('zhcy.confirmAndIssue') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog
      :title="$t('zhcy.cimMessage')"
      width="50%"
      top="20px"
      :visible.sync="dialogCIMMsgVisible"
      :close-on-click-modal="false"
      class="elDialog"
      @close="resetCIMDialog"
    >
      <table class="table">
        <tr>
          <td class="label" style="width:100px;">{{ $t('zhcy.code') }}：</td>
          <td class="content">{{ screen_code }}</td>
        </tr>
        <tr>
          <td class="label">{{ $t('zhcy.message') }}：</td>
          <td class="content">{{ cim_msg }}</td>
        </tr>
        <tr v-if="ConfirmBtnVisible">
          <td colspan="2" style="text-align:center;">
            <el-button
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-check"
              @click="handleConfirmCIMMsg"
            >{{ $t('zhcy.confirm') }}</el-button>
          </td>
        </tr>
      </table>
    </el-dialog>
    <el-dialog
      :title="$t('zhcy.employeeLogin')"
      :visible.sync="userDialogVisible"
      width="650px"
      top="65px"
      class="elDialog"
      :close-on-click-modal="false"
    >
      <el-form :model="loginForm" label-width="120px">
        <el-form-item :label="$t('zhcy.employeeId') + '：'">
          <el-input
            ref="userId"
            v-model="loginForm.userId"
            clearable
            size="large"
            :placeholder="$t('zhcy.pleaseEnterEmployeeId')"
            style="width: 100%; font-size: 24px;"
            @keyup.enter.native="handleUserLogin"
          />
        </el-form-item>
        <el-form-item :label="$t('zhcy.password') + '：'">
          <el-input
            ref="password"
            v-model="loginForm.password"
            type="password"
            clearable
            size="large"
            :placeholder="$t('zhcy.pleaseEnterPassword')"
            style="width: 100%; font-size: 24px;"
            @keyup.enter.native="handleUserLogin"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCloseDialog">{{ $t('zhcy.cancel') }}</el-button>
        <el-button type="primary" @click="handleUserLogin">{{ $t('zhcy.login') }}</el-button>
      </span>
    </el-dialog>

    <!-- 重新上机提示弹窗 -->
    <el-dialog
      :title="$t('zhcy.reloadMachineRequired')"
      :visible.sync="reloadMachineDialogVisible"
      width="400px"
      :close-on-click-modal="false"
      :show-close="false"
    >
      <div style="text-align: center; font-size: 18px; margin-bottom: 20px;">
        <i class="el-icon-warning" style="color: #E6A23C; font-size: 24px; margin-right: 10px;"></i>
        <span>{{ $t('zhcy.reloadMachineToContinue') }}</span>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleReloadMachine">{{ $t('zhcy.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import crudEapRecipeDetail from '@/api/eap/project/sfjd/eapRecipeDetail';
import { eapCimMsgShow } from '@/api/eap/eapApsPlan';
import { selScadaTag } from '@/api/core/scada/tag';
import { scadaTagGroupTree } from '@/api/core/scada/tagGroup';
import CRUD, { presenter, header, form, crud } from '@crud/crud';
import axios from 'axios';
import { selCellIP } from '@/api/core/center/cell';
import { sel as selStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie';
import mqtt from 'mqtt';
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js';
import { mapGetters } from 'vuex';
import { userLogin, userLogout, fetchTaskList, cancelTask, deleteTask, syncIcCodeReport, downloadRecipe, manuallySendPlan, manuallyEndWorkPlan } from '@/api/eap/project/zhcydayuan/eapZhcyDayuanMeStationUser';
import * as echarts from 'echarts';

const defaultForm = {};

export default {
  name: 'shRecipeMain',
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 608,
      gatherHeight: document.documentElement.clientHeight - 424,
      qr_code: '',
      material_code: '',
      lot_count: '0',
      current_user: '',
      recipeData: [],
      queryCim: true,
      dialogCIMMsgVisible: false,
      screen_code: '',
      cim_msg: '',
      ConfirmBtnVisible: false,
      cimTimer: null,
      userDialogVisible: false,
      loginForm: {
        userId: '',
        password: ''
      },
      reloadMachineDialogVisible: false,
      loginInfo: {
        user_name: '---',
        nick_name: '---',
        dept_id: '---',
        shift_id: '---',
        permission: '---'
      },
      apsPlanList: [],
      taskListLoading: false,
      taskStatusFilter: ['PLAN', 'WORK'],
      statusFilters: {
        PLAN: true,
        WORK: true,
        FINISH: false,
        CANCEL: false
      },
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0,
        totalPages: 1
      },
      capacityChart: null,
      oeeChart: null,
      readbitRateChart: null,
      capacityOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: this.$t('zhcy.capacity'),
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#6df320', '#d2e312'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              show: false
            },
            data: [
              { value: 335, name: this.$t('zhcy.offDuty') },
              { value: 234, name: this.$t('zhcy.onDuty') }
            ]
          }
        ]
      },
      oeeOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: 'OEE',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#409EFF', '#40e2ff'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              show: false
            },
            data: [
              { value: 335, name: this.$t('zhcy.offDuty') },
              { value: 234, name: this.$t('zhcy.onDuty') }
            ]
          }
        ]
      },
      readbitRateOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: this.$t('zhcy.readRate'),
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#9d9727', '#c25b1f'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              show: false
            },
            data: [
              { value: 335, name: this.$t('zhcy.offDuty') },
              { value: 234, name: this.$t('zhcy.onDuty') }
            ]
          }
        ]
      },
      dialogVisible: false,
      rules: {
        material_code: [
          { required: true, message: this.$t('zhcy.enterMaterialCode'), trigger: 'blur' }
        ],
        material_des: [
          { required: true, message: this.$t('zhcy.enterMaterialDesc'), trigger: 'blur' }
        ]
      },
      disabled: false,
      controlStatus: {
        eap_status: '0',
        plc_status: '0',
        ccd_status: '0',
        panel_status: '0',
        unload_status: '0'
      },
      eapMonitorData: {
        EapOnOffLine: {
          client_code: 'TmjAis',
          group_code: 'AisStatus',
          tag_code: 'EapOnOffLine',
          tag_des: '[AisStatus]EAP在线离线(0离线,1在线)',
          value: '0'
        }
      },
      controlData: {},
      controlStatusData: [
        { id: '0', label: this.$t('zhcy.productionMode') },
        { id: '1', label: this.$t('zhcy.firstPieceMode') },
        { id: '2', label: this.$t('zhcy.dummyMode') }
      ],
      monitorData: {
        LightGreen: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightGreen',
          tag_des: '[PlcStatus]三色灯绿',
          value: '0'
        },
        HmiScanDryFilmCode: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'HmiScanDryFilmCode',
          tag_des: '[PlcStatus]干膜物料编码',
          value: ''
        },
        LightYellow: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightYellow',
          tag_des: '[PlcStatus]三色灯黄',
          value: '0'
        },
        LightRed: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightRed',
          tag_des: '[PlcStatus]三色灯红',
          value: '0'
        },
        LightBlue: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightBlue',
          tag_des: '[PlcStatus]三色灯蓝',
          value: '0'
        },
        ControlMode: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'DeviceMode',
          tag_des: '[PlcStatus]设备控制状态(0:离线,1:在线/远程,2:在线/本地)',
          value: '0'
        },
        AllowWork: {
          client_code: `Plc`,
          group_code: 'PcStatus',
          tag_code: 'AllowWork',
          tag_des: '[PcStatus]允许设备生产使能(0不允许,1允许)',
          value: '0'
        },
        ClearLine: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'ClearLine',
          tag_des: '[PlcStatus]允许配方下发使能(0禁止,1允许)',
          value: '0'
        },
        ReLoadMachineFlag: {
          client_code: `Plc`,
          group_code: 'PcStatus',
          tag_code: 'ReLoadMachineFlag',
          tag_des: '[PcStatus]需要重新上机标志(0不需要,1需要)',
          value: '0'
        },
        ToDayCount: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'ToDayCount',
          tag_des: '[PlcStatus]当日产量',
          value: '0'
        },
        QrSucessRate: {
          client_code: 'Ais',
          group_code: 'AisStatus',
          tag_code: 'QrSucessRate',
          tag_des: '[AisStatus]读码率',
          value: '0'
        },
        OEE: {
          client_code: 'Ais',
          group_code: 'AisStatus',
          tag_code: 'OEE',
          tag_des: '[AisStatus]设备OEE',
          value: '0'
        },
        EapOnOffLine: { client_code: 'Ais', group_code: 'AisStatus', tag_code: 'EapOnOffLine', tag_des: '[AisStatus]EAP在线离线(0离线,1在线)', value: '' },
        CurrentUser: {
          client_code: 'Ais',
          group_code: 'AisStatus',
          tag_code: 'CurrentUser',
          tag_des: '[AisStatus]人员工号',
          value: ''
        }
      },
      cellIp: '',
      webapiPort: '',
      mqttPort: '',
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false,
      mqttChangeStatus: false,
      aisMonitorMode: 'DEFAULT', // Added missing property
      optionsMqtt: {
        endpoint: '/mqtt',
        clean: true,
        connectTimeout: 5000,
        clientId: 'ScadaEsb_' + Cookies.get('userName') + '_' + Math.random().toString(16).substring(2, 10),
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        cleansession: false
      },
      alarmData: [],
      materialData: [],
      timer: null,
      groupData: [],
      productData: [],
      plcCraftData: [],
      stationAttr: '',
      tableData: {},
      autoRefreshTimer: null,
      alarmRefreshTimer: null
    };
  },
  cruds() {
    return CRUD({
      title: this.crudTitle, // 用data变量
      userName: Cookies.get('userName'),
      idField: 'recipe_detail_id',
      sort: ['recipe_detail_id asc'],
      crudMethod: { ...crudEapRecipeDetail },
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true
      }
    });
  },
  dicts: ['PROJECT_PARAMS_WARNING'],
  computed: {
    ...mapGetters(['user'])
  },
  mounted() {
    this.getStationAttr();
    this.getMaterialInfo();
    this.timer = setInterval(this.getCIMMessages, 5000);
    window.addEventListener('resize', this.handleResize);

    // 添加全局回车键监听器，阻止默认的表单提交行为
    document.addEventListener('keydown', this.handleGlobalKeyDown);

    // 设置30秒自动刷新任务列表
    this.autoRefreshTimer = setInterval(this.fetchTaskList, 30000);

    // 设置30秒自动刷新报警信息
    this.alarmRefreshTimer = setInterval(this.getAlarmData, 30000);

    // 初始化国际化文本
    this.initI18nText();

    this.$nextTick(() => {
      this.initCharts();
      this.getLoginInfo();
      this.fetchTaskList();

      // 注意: 初始状态值获取已移至getCellIp方法中，确保在MQTT连接建立后获取
    });

    this.controlData = {
      0: this.$t('zhcy.offlineMode'),
      1: this.$t('zhcy.onlineRemote'),
      2: this.$t('zhcy.onlineLocal')
    };
    // ...其它国际化赋值...
  },
  beforeDestroy() {
    clearInterval(this.timer);
    if (this.cimTimer) {
      clearTimeout(this.cimTimer);
    }
    if (this.autoRefreshTimer) {
      clearInterval(this.autoRefreshTimer);
    }
    if (this.alarmRefreshTimer) {
      clearInterval(this.alarmRefreshTimer);
    }
    window.removeEventListener('resize', this.handleResize);
    // 移除全局回车键监听器
    document.removeEventListener('keydown', this.handleGlobalKeyDown);
    if (this.capacityChart) {
      this.capacityChart.dispose();
    }
    if (this.oeeChart) {
      this.oeeChart.dispose();
    }
    if (this.readbitRateChart) {
      this.readbitRateChart.dispose();
    }
    if (this.clientMqtt) {
      this.clientMqtt.end();
    }
  },
  created() {
    this.getCellIp();
  },
  methods: {
    handleResize() {
      this.height = document.documentElement.clientHeight - 608;
      this.gatherHeight = document.documentElement.clientHeight - 424;
      if (this.capacityChart) {
        this.capacityChart.resize();
      }
      if (this.oeeChart) {
        this.oeeChart.resize();
      }
      if (this.readbitRateChart) {
        this.readbitRateChart.resize();
      }
    },
    initCharts() {
      const capacityDom = document.getElementById('capacityDom');
      const oeeDom = document.getElementById('oeeDom');
      const readbitRateDom = document.getElementById('readbitRateDom');
      if (capacityDom) {
        this.capacityChart = echarts.init(capacityDom);
        this.capacityChart.setOption(this.capacityOption);
        // 初始化时更新产能图表
        this.updateCapacityChart(this.monitorData.ToDayCount.value);
      }
      if (oeeDom) {
        this.oeeChart = echarts.init(oeeDom);
        this.oeeChart.setOption(this.oeeOption);
        // 初始化时更新OEE图表
        this.updateOeeChart(this.monitorData.OEE.value);
      }
      if (readbitRateDom) {
        this.readbitRateChart = echarts.init(readbitRateDom);
        this.readbitRateChart.setOption(this.readbitRateOption);
        // 初始化时更新读码率图表
        this.updateReadbitRateChart(this.monitorData.QrSucessRate.value);
      }
    },
    requestAccountInfo() {
      if (!this.$route.query.station_code) {
        this.$message({
          message: this.$t('zhcy.noStationSelectedOrIncompleteInfo'),
          type: 'warning'
        });
        return;
      }
      this.$message({
        message: this.$t('zhcy.requestingSyncAccountInfo'),
        type: 'info'
      });
      syncIcCodeReport(this.$route.query.station_id)
        .then(res => {
          if (res.data && res.data.result === 'success' && res.data.data && res.data.data.AccountList) {
            const accountList = res.data.data.AccountList;
            this.$message({
              message: `${this.$t('zhcy.successfullySynced')}${accountList.length}${this.$t('zhcy.permissionAccountInfo')}`,
              type: 'success'
            });
          } else {
            this.$message({
              message: this.$t('zhcy.requestPermissionAccountInfoFailed'),
              type: 'error'
            });
          }
        })
        .catch(error => {
          this.$message({
            message: this.$t('zhcy.requestPermissionAccountInfoException'),
            type: 'error'
          });
          console.error('Request account info error:', error);
        });
    },
    openUserLogin() {
      this.loginForm.userId = '';
      this.loginForm.password = '';
      this.userDialogVisible = true;
      this.$nextTick(() => {
        if (this.$refs.userId) {
          this.$refs.userId.focus();
        }
      });
    },
    handleUserLogin() {
      if (!this.loginForm.userId.trim()) {
        this.$message({ message: this.$t('zhcy.enterEmployeeId'), type: 'warning' });
        return;
      }
      if (!this.loginForm.password.trim()) {
        this.$message({ message: this.$t('zhcy.pleaseEnterPassword'), type: 'warning' });
        return;
      }
      this.$message({ message: this.$t('zhcy.loggingIn'), type: 'info' });
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id,
        user_code: this.loginForm.userId,
        Password: this.loginForm.password
      };
      userLogin(query)
        .then(res => {
          if (res.code === 0 && res.data && res.data.length > 0) {
            const loginInfo = res.data[0];
            this.loginInfo = {
              user_name: loginInfo.user_name,
              nick_name: loginInfo.nick_name,
              dept_id: loginInfo.dept_id,
              shift_id: loginInfo.shift_id,
              permission: loginInfo.permission || '---'
            };
            try {
              localStorage.setItem('zhcy_user_code', this.loginForm.userId);
              localStorage.setItem('zhcy_station_id', this.$route.query.station_id || '');
              localStorage.setItem('zhcy_login_info', JSON.stringify(this.loginInfo));
              console.log('用户信息已保存到本地存储');
            } catch (e) {
              console.error('保存用户信息到本地存储失败:', e);
            }
            this.userDialogVisible = false;
            this.$message({ message: this.$t('zhcy.loginSuccess'), type: 'success' });
            
            // 登录成功后写入CurrentUser点位
            this.writeCurrentUser(this.loginInfo.user_name);
          } else {
            this.$message({ message: res.msg || this.$t('zhcy.loginFailed'), type: 'error' });
          }
        })
        .catch(err => {
          console.error('登录异常:', err);
          this.$message({ message: this.$t('zhcy.loginError'), type: 'error' });
        });
    },
    handleUserLogout() {
      if (!this.loginInfo.user_name || this.loginInfo.user_name === '---') {
        this.$message({ message: this.$t('zhcy.noUserLoggedIn'), type: 'warning' });
        return;
      }
      this.$confirm(this.$t('zhcy.confirmLogout'), this.$t('zhcy.warning'), {
        confirmButtonText: this.$t('zhcy.confirm'),
        cancelButtonText: this.$t('zhcy.cancel'),
        type: 'warning'
      })
        .then(() => {
          const query = {
            user_name: Cookies.get('userName'),
            station_id: this.$route.query.station_id
          };
          userLogout(query)
            .then(res => {
              if (res.code === 0) {
                this.loginInfo = {
                  user_name: '---',
                  nick_name: '---',
                  dept_id: '---',
                  shift_id: '---',
                  permission: '---'
                };
                try {
                  localStorage.removeItem('zhcy_login_info');
                  console.log('已清除本地存储中的登录信息');
                } catch (e) {
                  console.error('清除本地存储失败:', e);
                }
                this.$message({ message: this.$t('zhcy.logoutSuccess'), type: 'success' });
                
                // 登出成功后清空CurrentUser点位
                this.writeCurrentUser('');
              } else {
                this.$message({ message: res.msg || this.$t('zhcy.logoutFailed'), type: 'error' });
              }
            })
            .catch(err => {
              console.error('登出异常:', err);
              this.$message({ message: this.$t('zhcy.logoutError'), type: 'error' });
            });
        })
        .catch(() => {});
    },
    handleCloseDialog() {
      this.userDialogVisible = false;
      this.loginForm.userId = '';
      this.loginForm.password = '';
    },
    getCIMMessages() {
      if (!this.queryCim) return;
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      };
      eapCimMsgShow(query)
        .then(res => {
          if (res.code === 0 && res.count > 0) {
            const msgInfo = res.data[0];
            this.screen_code = msgInfo.screen_code;
            this.cim_msg = msgInfo.cim_msg;
            this.ConfirmBtnVisible = msgInfo.screen_control === '1';
            this.dialogCIMMsgVisible = true;
            this.queryCim = false;
            if (!this.ConfirmBtnVisible) {
              const timeoutMs = msgInfo.interval_second_time ? parseInt(msgInfo.interval_second_time) * 1000 : 8000;
              this.cimTimer = setTimeout(() => {
                this.resetCIMDialog();
              }, timeoutMs);
            }
          }
        })
        .catch(err => {
          console.error('获取CIM消息失败:', err);
        });
    },
    handleConfirmCIMMsg() {
      this.resetCIMDialog();
    },
    resetCIMDialog() {
      this.dialogCIMMsgVisible = false;
      this.screen_code = '';
      this.cim_msg = '';
      this.ConfirmBtnVisible = false;
      this.queryCim = true;
      if (this.cimTimer) {
        clearTimeout(this.cimTimer);
        this.cimTimer = null;
      }
    },
    handleStatusFilterChange(status) {
      console.log(`状态过滤器变化: ${status} => ${this.statusFilters[status]}`);
    },
    getStatusType(status) {
      switch (status) {
        case 'PLAN': return 'info';
        case 'WORK': return 'success';
        case 'FINISH': return '';
        case 'CANCEL': return 'danger';
        default: return '';
      }
    },
    getStatusText(status) {
      switch (status) {
        case 'PLAN': return this.$t('zhcy.planning');
        case 'WORK': return this.$t('zhcy.inProduction');
        case 'FINISH': return this.$t('zhcy.completed');
        case 'CANCEL': return this.$t('zhcy.cancelled');
        default: return status;
      }
    },
    getPortIdDescription(portId) {
      const portDescriptions = {
        'L01': '左侧端口01',
        'U01': '上侧端口01',
        'PP': '主端口'
      };
      return portDescriptions[portId] || portId;
    },
    formatDateTime(dateTimeStr) {
      if (!dateTimeStr) return '';
      const date = new Date(dateTimeStr);
      if (isNaN(date.getTime())) return dateTimeStr;
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },
    getLoginInfo() {
      if (!this.$route.query.station_id) {
        console.warn('未选择工位或工位信息不完整');
        return;
      }
      try {
        const storedUserCode = localStorage.getItem('zhcy_user_code');
        const storedStationId = localStorage.getItem('zhcy_station_id');
        const storedLoginInfo = localStorage.getItem('zhcy_login_info');
        if (storedUserCode && storedStationId === this.$route.query.station_id && storedLoginInfo) {
          try {
            this.loginInfo = JSON.parse(storedLoginInfo);
            console.log('从本地存储中恢复用户登录状态');
            return;
          } catch (e) {
            console.error('解析本地存储的登录信息失败:', e);
          }
        }
      } catch (e) {
        console.error('读取本地存储失败:', e);
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      };
      const storedUserCode = localStorage.getItem('zhcy_user_code');
      if (storedUserCode) {
        query.user_code = storedUserCode;
      }
      userLogin(query)
        .then(res => {
          if (res.code === 0 && res.data && res.data.length > 0) {
            const loginInfo = res.data[0];
            this.loginInfo = {
              user_name: loginInfo.user_name,
              nick_name: loginInfo.nick_name,
              dept_id: loginInfo.dept_id,
              shift_id: loginInfo.shift_id,
              permission: loginInfo.permission || '---'
            };
            try {
              localStorage.setItem('zhcy_login_info', JSON.stringify(this.loginInfo));
              if (storedUserCode) {
                localStorage.setItem('zhcy_user_code', storedUserCode);
              }
              localStorage.setItem('zhcy_station_id', this.$route.query.station_id || '');
            } catch (e) {
              console.error('保存登录信息到本地存储失败:', e);
            }
          }
        })
        .catch(err => {
          console.error('获取登录信息异常:', err);
        });
    },
    fetchTaskList() {
      if (!this.$route.query.station_code) {
        this.$message({
          message: this.$t('zhcy.noStationSelectedOrIncompleteInfo'),
          type: 'warning'
        });
        return;
      }
      const statusList = [];
      Object.keys(this.statusFilters).forEach(status => {
        if (this.statusFilters[status]) {
          statusList.push(status);
        }
      });
      if (statusList.length === 0) {
        this.$message({
          message: this.$t('zhcy.selectAtLeastOneStatusForQuery'),
          type: 'warning'
        });
        return;
      }
      this.taskListLoading = true;
      const params = {
        eqpId: this.$route.query.station_code,
        statusList: statusList,
        pageSize: this.pagination.pageSize,
        currentPage: this.pagination.currentPage
      };
      fetchTaskList(params)
        .then(res => {
          this.taskListLoading = false;
          if (res.data && res.data.code === 0) {
            this.apsPlanList = res.data.data || [];
            this.pagination.total = res.data.count || 0;
            this.pagination.totalPages = Math.ceil(this.pagination.total / this.pagination.pageSize) || 1;
          } else {
            this.$message({
              message: (res.data && res.data.msg) ? res.data.msg : this.$t('zhcy.failedToGetTaskList'),
              type: 'error'
            });
            this.apsPlanList = [];
            this.pagination.total = 0;
            this.pagination.totalPages = 1;
          }
        })
        .catch(error => {
          this.taskListLoading = false;
          console.error('获取任务列表异常:', error);
          this.$message({
            message: this.$t('zhcy.taskListException'),
            type: 'error'
          });
          this.apsPlanList = [];
          this.pagination.total = 0;
          this.pagination.totalPages = 1;
        });
    },
    handlePageChange(direction) {
      if (direction === 'prev' && this.pagination.currentPage > 1) {
        this.pagination.currentPage--;
      } else if (direction === 'next' && this.pagination.currentPage < this.pagination.totalPages) {
        this.pagination.currentPage++;
      }
      this.fetchTaskList();
    },
    handleCancelTask(task) {
      this.$confirm(`${this.$t('zhcy.confirmCancelTask')} ${task.plan_id} ?`, this.$t('zhcy.prompt'), {
        confirmButtonText: this.$t('zhcy.confirm'),
        cancelButtonText: this.$t('zhcy.cancel'),
        type: 'warning'
      }).then(() => {
        cancelTask({ jobId: task.plan_id })
          .then(res => {
            if (res.data && res.data.code === 0) {
              this.$message({
                message: this.$t('zhcy.taskCancellationSuccessful'),
                type: 'success'
              });
              this.fetchTaskList();
            } else {
              this.$message({
                message: (res.data && res.data.msg) ? res.data.msg : this.$t('zhcy.taskCancellationFailed'),
                type: 'error'
              });
            }
          })
          .catch(error => {
            console.error('取消任务异常:', error);
            this.$message({
              message: this.$t('zhcy.taskCancellationException'),
              type: 'error'
            });
          });
      }).catch(() => {});
    },
    handleDeleteTask(task) {
      this.$confirm(`${this.$t('zhcy.confirmDeleteTask')} ${task.plan_id}`, this.$t('zhcy.warning'), {
        confirmButtonText: this.$t('zhcy.confirm'),
        cancelButtonText: this.$t('zhcy.cancel'),
        type: 'danger'
      }).then(() => {
        deleteTask({ jobId: task.plan_id })
          .then(res => {
            if (res.data && res.data.code === 0) {
              this.$message({
                message: this.$t('zhcy.taskDeletionSuccessful'),
                type: 'success'
              });
              this.fetchTaskList();
            } else {
              this.$message({
                message: (res.data && res.data.msg) ? res.data.msg : this.$t('zhcy.taskDeletionFailed'),
                type: 'error'
              });
            }
          })
          .catch(error => {
            console.error('删除任务异常:', error);
            this.$message({
              message: this.$t('zhcy.taskDeletionException'),
              type: 'error'
            });
          });
      }).catch(() => {});
    },

    // 处理启动任务
    handleStartTask(task) {
      this.$confirm(this.$t('zhcy.confirmStartTask', [task.plan_id]), this.$t('zhcy.warning'), {
        confirmButtonText: this.$t('zhcy.confirm'),
        cancelButtonText: this.$t('zhcy.cancel'),
        type: 'warning'
      }).then(() => {
        // 显示加载中提示
        const loading = this.$loading({
          lock: true,
          text: this.$t('zhcy.startingTask'),
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 准备请求数据
        const requestData = {
          stationId: this.$route.query.station_id,
          planId: task.plan_id
        };

        // 调用启动任务API
        manuallySendPlan(requestData)
          .then(response => {
            loading.close();

            if (response.data && response.data.Success === true) {
              // 启动成功
              this.$message({
                message: this.$t('zhcy.taskStartSuccessful'),
                type: 'success'
              });

              // 刷新任务列表
              this.fetchTaskList();
            } else {
              // 启动失败 - 使用确认对话框需要人工确认
              const errorMsg = (response.data && response.data.Msg) ? response.data.Msg : this.$t('zhcy.taskStartFailedCheckDeviceStatus');
              this.$confirm(errorMsg, this.$t('zhcy.taskStartFailed'), {
                confirmButtonText: this.$t('zhcy.confirm'),
                showCancelButton: false,
                type: 'error',
                closeOnClickModal: false,
                closeOnPressEscape: false
              }).catch(() => {});
            }
          })
          .catch(error => {
            loading.close();
            console.error('启动任务异常:', error);
            this.$confirm(this.$t('zhcy.taskStartFailedCheckDeviceStatus'), this.$t('zhcy.taskStartFailed'), {
              confirmButtonText: this.$t('zhcy.confirm'),
              showCancelButton: false,
              type: 'error',
              closeOnClickModal: false,
              closeOnPressEscape: false
            }).catch(() => {});
          });
      }).catch(() => {});
    },

    // 处理结束任务
    handleEndTask(task) {
      this.$confirm(this.$t('zhcy.confirmEndTask', [task.plan_id]), this.$t('zhcy.warning'), {
        confirmButtonText: this.$t('zhcy.confirm'),
        cancelButtonText: this.$t('zhcy.cancel'),
        type: 'warning'
      }).then(() => {
        // 显示加载中提示
        const loading = this.$loading({
          lock: true,
          text: this.$t('zhcy.endingTask'),
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });

        // 准备请求数据
        const requestData = {
          stationId: this.$route.query.station_id,
          jobId: task.plan_id
        };

        // 调用结束任务API
        manuallyEndWorkPlan(requestData)
          .then(response => {
            loading.close();

            if (response.data && response.data.Success === true) {
              // 结束成功 - 显示不会自动关闭的消息
              this.$message({
                message: this.$t('zhcy.endRequestSentWaitForDeviceToCompleteRemainingMaterial'),
                type: 'success',
                showClose: true//duration: 0
              });

              // 刷新任务列表
              this.fetchTaskList();
            } else {
              // 结束失败 - 使用确认对话框需要人工确认
              const errorMsg = (response.data && response.data.Msg) ? response.data.Msg : this.$t('zhcy.taskEndFailedCheckDeviceStatus');
              this.$confirm(errorMsg, this.$t('zhcy.taskEndFailed'), {
                confirmButtonText: this.$t('zhcy.confirm'),
                showCancelButton: false,
                type: 'error',
                closeOnClickModal: false,
                closeOnPressEscape: false
              }).catch(() => {});
            }
          })
          .catch(error => {
            loading.close();
            console.error('结束任务异常:', error);
            this.$confirm('任务结束失败，请检查设备状态', '任务结束失败', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'error',
              closeOnClickModal: false,
              closeOnPressEscape: false
            }).catch(() => {});
          });
      }).catch(() => {});
    },
    clientCodeSelect(value) {
      console.log('选择料号:', value);
    },
    downloadRecipe() {
      console.log('下载配方，工单号:', this.qr_code);
      if (!this.qr_code) {
        this.$message({
          message: '请输入或扫码工单号',
          type: 'warning'
        });
        return;
      }

      // 检查是否已登录
      if (!this.loginInfo.user_name || this.loginInfo.user_name === '---') {
        this.$message({
          message: this.$t('zhcy.pleaseLoginFirst'),
          type: 'warning'
        });
        return;
      }

      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: this.$t('zhcy.downloadingRecipe'),
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });

      // 调用下载配方 API
      downloadRecipe({ 
        qrCode: this.qr_code, 
        stationId: this.$route.query.station_id 
      })
        .then(response => {
          loading.close();

          if (response.data && response.data.Success === true) {
            // 下载配方成功
            this.$message({
              message: this.$t('zhcy.downloadRecipeSuccess'),
              type: 'success',
              duration: 3000
            });

            // 清空输入框
            this.qr_code = '';

            // 刷新任务列表
            setTimeout(() => {
              this.fetchTaskList();
            }, 500);
          } else {
            // 下载配方失败
            const errorMsg = (response.data && response.data.Msg) ? response.data.Msg : this.$t('zhcy.downloadRecipeFailed');
            this.$confirm(errorMsg, this.$t('zhcy.downloadRecipeFailed'), {
              confirmButtonText: this.$t('zhcy.confirm'),
              showCancelButton: false,
              type: 'error',
              closeOnClickModal: false,
              closeOnPressEscape: false
            }).catch(() => {});
          }
        })
        .catch(error => {
          loading.close();
          console.error('下载配方异常:', error);
          this.$confirm(this.$t('zhcy.downloadRecipeFailed'), this.$t('zhcy.downloadRecipeFailed'), {
            confirmButtonText: this.$t('zhcy.confirm'),
            showCancelButton: false,
            type: 'error',
            closeOnClickModal: false,
            closeOnPressEscape: false
          }).catch(() => {});
        });
    },
    handleOk() {
      console.log('确认下发配方');
      if (this.crud.data.length === 0) {
        this.$message({
          message: this.$t('zhcy.noRecipeDataToIssue'),
          type: 'warning'
        });
        return;
      }
      this.dialogVisible = false;
      this.$message({
        message: this.$t('zhcy.recipeIssuedSuccessfully'),
        type: 'success'
      });
    },
    handleDisabled(value) {
      return value !== '1' && !this.disabled;
    },
    getStationAttr() {
      const query = {
        stationCodeDes: this.$route.query.station_code,
        user_name: Cookies.get('userName')
      }
      selStation(query).then(res => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.stationAttr = res.data[0].station_attr
            Object.keys(this.monitorData).forEach((key) => {
              this.monitorData[key].client_code = `${this.stationAttr}` + this.monitorData[key].client_code
            })
            this.getScadaData()
            return
          }
          this.stationAttr = ''
        }
      })
    },
    getScadaData() {
      const query = {
        client_id: 1010,
        enable_flag: 'Y',
        sort: 'tag_group_id',
        user_name: Cookies.get('userName')
      }
      scadaTagGroupTree(query).then((res) => {
        if (res.data.length > 0) {
          const data = res.data.find((item) => item.tag_group_code === 'PcRecipe')
          if (data && data.children.length > 0) {
            data.children.forEach((e) => {
              this.groupData.push({
                tag_key: `${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`,
                tag_des: e.tag_des,
                value: ''
              })
            })
            this.tableData = data.children.reduce((acc, e) => {
              acc[`${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`] = ''
              return acc
            }, {})
          }
        }
      })
      const params = {
        tableOrder: 'asc',
        tableOrderField: 'tag_id',
        tablePage: 1,
        tableSize: 1000,
        tag_group_id: 101003,
        user_name: Cookies.get('userName')
      }
      selScadaTag(params).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              this.dict.PROJECT_PARAMS_WARNING.forEach((e) => {
                if (`${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}` === e.value) {
                  const result = {
                    tag_des: item.tag_des,
                    tag_key: `${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}`,
                    tag_value: '',
                    unit: '',
                    down_limit: item.down_limit,
                    upper_limit: item.upper_limit,
                    status: ''
                  }
                  this.plcCraftData.push(result)
                }
              })
            })
          }
        }
      })
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      };
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res));
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result);
            this.cellIp = ipInfo.ip;
            this.webapiPort = ipInfo.webapi_port;
            this.mqttPort = ipInfo.mqtt_port;
            setTimeout(() => {
              this.toStartWatch();
              this.getTagValue();
              this.getAlarmData();
            }, 1000);
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' });
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' });
        });
    },
    getAlarmData() {
      const method = '/cell/core/scada/CoreScadaAlarmSelect';
      const path = process.env.NODE_ENV === 'development' ? `http://localhost:${this.webapiPort}${method}` : `http://${this.cellIp}:${this.webapiPort}${method}`;
      const queryData = {
        tablePage: 1,
        tableSize: 10
      };
      axios
        .post(path, queryData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data;
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: '查询异常：' + ex, type: 'error' });
        });
    },
    getTagValue() {
      const readTagArray = [];
      Object.keys(this.monitorData).forEach((key) => {
        let client_code = this.monitorData[key].client_code;
        const group_code = this.monitorData[key].group_code;
        const tag_code = this.monitorData[key].tag_code;
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.$route.query.station_code;
        }
        const readTag = {
          tag_key: `${client_code}/${group_code}/${tag_code}`
        };
        readTagArray.push(readTag);
      });
      this.groupData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        });
      });
      this.plcCraftData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        });
      });
      const method = '/cell/core/scada/CoreScadaReadTag';
      const path = process.env.NODE_ENV === 'development' ? `http://localhost:${this.webapiPort}${method}` : `http://${this.cellIp}:${this.webapiPort}${method}`;
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data));
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              const result = defaultQuery.data;
              Object.keys(this.monitorData).forEach((key) => {
                let client_code = this.monitorData[key].client_code;
                const group_code = this.monitorData[key].group_code;
                const tag_code = this.monitorData[key].tag_code;
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code = client_code + '_' + this.$route.query.station_code;
                }
                const tag_key = `${client_code}/${group_code}/${tag_code}`;
                const item = result.filter((item) => item.tag_key === tag_key);
                if (item.length > 0) {
                  const tagValue = item[0].tag_value === undefined ? '' : item[0].tag_value;
                  this.monitorData[key].value = tagValue;

                  // 特殊处理 HmiScanDryFilmCode 标签，更新输入框值
                  if (key === 'HmiScanDryFilmCode' && tagValue) {
                    console.log('初始化干膜物料编码:', tagValue);
                    this.qr_code = String(tagValue);
                  }

                  // 更新图表数据
                  if (key === 'ToDayCount') {
                    this.$nextTick(() => {
                      this.updateCapacityChart(this.monitorData.ToDayCount.value);
                    });
                  } else if (key === 'QrSucessRate') {
                    this.$nextTick(() => {
                      this.updateReadbitRateChart(this.monitorData.QrSucessRate.value);
                    });
                  } else if (key === 'OEE') {
                    this.$nextTick(() => {
                      this.updateOeeChart(this.monitorData.OEE.value);
                    });
                  }
                }
              });
              result.forEach((e) => {
                this.plcCraftData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.tag_value = e.tag_value;
                    if (
                      typeof +e.tag_value === 'number' &&
                      +e.tag_value >= item.down_limit &&
                      +e.tag_value <= item.upper_limit
                    ) {
                      item.status = 'OK';
                    } else {
                      item.status = 'NG';
                    }
                  }
                });
                this.groupData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.value = e.tag_value;
                  }
                });
              });
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: '查询异常：' + ex, type: 'error' });
        });
    },
    toStartWatch() {
      if (this.mqttConnStatus && this.clientMqtt) {
        this.clientMqtt.end();
        this.mqttConnStatus = false;
      }
      if (!this.cellIp || !this.mqttPort) {
        console.warn('MQTT连接信息不完整，无法初始化MQTT');
        return;
      }
      const connectUrl = `ws://${this.cellIp}:${this.mqttPort}/mqtt`;
      console.log('MQTT连接URL:', connectUrl);
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt);
      this.clientMqtt.on('connect', () => {
        console.log('MQTT连接成功');
        this.mqttConnStatus = true;
        this.topicSubscribe(`SCADA_BEAT/${this.stationAttr}Plc`);
        this.topicSubscribe(`SCADA_STATUS/${this.stationAttr}Plc`);
        this.topicSubscribe(`SCADA_STATUS/${this.stationAttr}Ais`);
        this.topicSubscribe(`SCADA_BEAT/${this.stationAttr}Ais`);
        this.topicSubscribe(`SCADA_BEAT/${this.stationAttr}Ccd`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PlcStatus/LightGreen`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PlcStatus/LightYellow`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PlcStatus/LightRed`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PlcStatus/LightBlue`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PlcStatus/DeviceMode`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PlcStatus/ToDayCount`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PlcStatus/ClearLine`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PcStatus/AllowWork`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Plc/PcStatus/ReLoadMachineFlag`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Ais/AisStatus/QrSucessRate`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Ais/AisStatus/OEE`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Ais/AisStatus/EapOnOffLine`);
        this.topicSubscribe(`SCADA_CHANGE/${this.stationAttr}Ais/AisStatus/CurrentUser`);
        if (this.$route.query.station_code) {
          this.topicSubscribe(`AISWEB_MSG/${this.$route.query.station_code}`);
        }
        this.groupData.forEach((item) => {
          this.topicSubscribe('SCADA_CHANGE/' + item.tag_key);
        });
        Object.keys(this.monitorData).forEach((key) => {
          let client_code = this.monitorData[key].client_code;
          const group_code = this.monitorData[key].group_code;
          const tag_code = this.monitorData[key].tag_code;
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.$route.query.station_code;
          }
          this.topicSubscribe(`SCADA_CHANGE/${client_code}/${group_code}/${tag_code}`);
        });
        this.$message({
          message: this.$t('zhcy.mqttConnSuccess'),
          type: 'success'
        });
        this.getAlarmData();
      });
      this.clientMqtt.on('error', (error) => {
        console.error('MQTT连接错误:', error);
        this.mqttConnStatus = false;
        this.$message({
          message: this.$t('zhcy.mqttConnFailed'),
          type: 'error'
        });
      });
      this.clientMqtt.on('reconnect', () => {
        console.warn('MQTT正在重连...');
        this.$message({
          message: this.$t('zhcy.mqttReconnecting'),
          type: 'warning'
        });
      });
      this.clientMqtt.on('message', (topic, message) => {
        try {
          const jsonData = JSON.parse(message.toString());
          console.log('收到MQTT消息:', topic, jsonData);
          if (topic.includes('SCADA_BEAT/')) {
            const heartBeatValue = jsonData.Beat;
            if (topic.includes(`SCADA_BEAT/${this.stationAttr}Plc`)) {
              if (this.controlStatus.plc_status !== '2') {
                this.controlStatus.plc_status = heartBeatValue;
              }
            } else if (topic.includes(`SCADA_BEAT/${this.stationAttr}Ccd`)) {
              if (this.controlStatus.ccd_status !== '2') {
                this.controlStatus.ccd_status = heartBeatValue;
              }
            } else if (topic.includes(`SCADA_BEAT/${this.stationAttr}Ais`)) {
              // 可以在这里处理AIS心跳
            }
          } else if (topic.includes('SCADA_STATUS/')) {
            const statusValue = jsonData.Status;
            if (topic.includes(`SCADA_STATUS/${this.stationAttr}Plc`)) {
              if (statusValue === '0') {
                this.controlStatus.plc_status = '2';
                this.$message({ message: this.$t('zhcy.plcCommError'), type: 'error' });
              } else {
                if (this.controlStatus.plc_status === '2') {
                  this.controlStatus.plc_status = '1';
                }
              }
            } else if (topic.includes(`SCADA_STATUS/${this.stationAttr}Ais`)) {
              if (statusValue === '0') {

                this.$message({ message: this.$t('zhcy.eapCommError'), type: 'error' });
              } else {
              }
            }
          } else if (topic.includes('SCADA_CHANGE/')) {
            const tagKey = jsonData.TagKey;
            const tagNewValue = jsonData.TagNewValue;
            if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/LightGreen`)) {
              this.monitorData.LightGreen.value = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/LightYellow`)) {
              this.monitorData.LightYellow.value = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/LightRed`)) {
              this.monitorData.LightRed.value = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/LightBlue`)) {
              this.monitorData.LightBlue.value = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/DeviceMode`)) {
              this.monitorData.ControlMode.value = tagNewValue;
              console.log('设备模式已更新:', tagNewValue, this.controlData[tagNewValue]);
            } else if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/ToDayCount`)) {
              this.monitorData.ToDayCount.value = tagNewValue;
              this.updateCapacityChart(tagNewValue);
            } else if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/ClearLine`)) {
              this.monitorData.ClearLine.value = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Plc/PcStatus/AllowWork`)) {
              this.monitorData.AllowWork.value = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Plc/PcStatus/ReLoadMachineFlag`)) {
              this.monitorData.ReLoadMachineFlag.value = tagNewValue;
              // 当ReLoadMachineFlag为1时，显示需要重新上机的提示弹窗
              if (tagNewValue === '1') {
                this.showReloadMachineDialog();
              }
            } else if (tagKey.includes(`${this.stationAttr}Plc/PlcStatus/HmiScanDryFilmCode`)) {
              this.monitorData.HmiScanDryFilmCode.value = tagNewValue;
              this.qr_code = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Ais/AisStatus/QrSucessRate`)) {
              this.monitorData.QrSucessRate.value = tagNewValue;
              this.updateReadbitRateChart(tagNewValue);
            } else if (tagKey.includes(`${this.stationAttr}Ais/AisStatus/OEE`)) {
              this.monitorData.OEE.value = tagNewValue;
              this.updateOeeChart(tagNewValue);
            } else if (tagKey.includes(`${this.stationAttr}Ais/AisStatus/EapOnOffLine`)) {
              this.controlStatus.eap_status = tagNewValue;
            } else if (tagKey.includes(`${this.stationAttr}Ais/AisStatus/CurrentUser`)) {
              this.monitorData.CurrentUser.value = tagNewValue;
              console.log('CurrentUser点位已更新:', tagNewValue);
            }
            this.groupData.forEach((item) => {
              if (tagKey === item.tag_key) {
                item.value = tagNewValue;
              }
            });
          } else if (topic.includes('AISWEB_MSG/') && this.$route.query.station_code && topic.includes(this.$route.query.station_code)) {
            this.handleMessage(jsonData);
          }
        } catch (error) {
          console.error('处理MQTT消息异常:', error);
        }
      });
    },
    initMqtt() {
      if (!this.cellIp || !this.mqttPort) {
        console.warn('MQTT连接信息不完整，无法初始化MQTT');
        return;
      }
      this.toStartWatch();
    },
    topicSubscribe(topic) {
      if (!this.mqttConnStatus || !this.clientMqtt) {
        console.warn(`MQTT未连接，无法订阅主题: ${topic}`);
        return;
      }
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (error) {
          console.error(`订阅主题失败: ${topic}`, error);
        } else {
          console.log(`成功订阅主题: ${topic}`);
        }
      });
    },
    sendMessage(topic, message) {
      if (!this.mqttConnStatus || !this.clientMqtt) {
        console.warn(`MQTT未连接，无法发送消息到主题: ${topic}`);
        return;
      }
      this.clientMqtt.publish(topic, message, { qos: 0 }, (error) => {
        if (error) {
          console.error(`发送消息失败: ${topic}`, error);
        } else {
          console.log(`成功发送消息到主题: ${topic}`);
        }
      });
    },
    handleMessage(message) {
      console.log('处理工位特定消息:', message);
    },

    // 显示重新上机提示弹窗
    showReloadMachineDialog() {
      this.reloadMachineDialogVisible = true;
    },

    // 处理重新上机确认
    handleReloadMachine() {
      this.reloadMachineDialogVisible = false;

      // 清空输入框，准备重新上机
      this.qr_code = '';

      // 聚焦到输入框，方便用户立即扫码
      this.$nextTick(() => {
        if (this.$refs.qr_code) {
          this.$refs.qr_code.focus();
        }
      });

      // 显示提示信息
      this.$message({
        message: this.$t('zhcy.rescanRequired'),
        type: 'warning',
        duration: 5000
      });
    },

    // 更新产能图表
    updateCapacityChart(value) {
      if (!this.capacityChart) return;

      // 将值转换为数字
      const count = parseInt(value) || 0;

      // 更新图表标题文本
      this.capacityOption.title.text = count.toString();

      // 更新图表数据
      this.capacityOption.series[0].data = [
        { value: count, name: this.$t('zhcy.dailyProduction') },
        { value: 0, name: '' } // 保持环形图的形状
      ];

      // 应用更新后的配置
      this.capacityChart.setOption(this.capacityOption);
    },

    // 更新读码率图表
    updateReadbitRateChart(value) {
      if (!this.readbitRateChart) return;

      // 将值转换为数字，确保是百分比格式
      let rate = parseFloat(value) || 0;

      // 如果值大于1且小于等于100，认为是百分比形式（如：95.5）
      if (rate > 1 && rate <= 100) {
        // 保留一位小数
        rate = Math.round(rate * 10) / 10;
      }
      // 如果值小于等于1，认为是小数形式（如：0.955）
      else if (rate <= 1) {
        // 转换为百分比并保留一位小数
        rate = Math.round(rate * 1000) / 10;
      }

      // 更新图表标题文本（添加百分号）
      this.readbitRateOption.title.text = rate.toString() + '%';

      // 更新图表数据
      this.readbitRateOption.series[0].data = [
        { value: rate, name: this.$t('zhcy.readRate') },
        { value: rate < 100 ? 100 - rate : 0, name: '' } // 保持环形图的形状
      ];

      // 应用更新后的配置
      this.readbitRateChart.setOption(this.readbitRateOption);
    },

    // 更新OEE图表
    updateOeeChart(value) {
      if (!this.oeeChart) return;

      // 将值转换为数字，确保是百分比格式
      let rate = parseFloat(value) || 0;

      // 如果值大于1且小于等于100，认为是百分比形式（如：85.5）
      if (rate > 1 && rate <= 100) {
        // 保留一位小数
        rate = Math.round(rate * 10) / 10;
      }
      // 如果值小于等于1，认为是小数形式（如：0.855）
      else if (rate <= 1) {
        // 转换为百分比并保留一位小数
        rate = Math.round(rate * 1000) / 10;
      }

      // 更新图表标题文本（添加百分号）
      this.oeeOption.title.text = rate.toString() + '%';

      // 更新图表数据
      this.oeeOption.series[0].data = [
        { value: rate, name: 'OEE' },
        { value: rate < 100 ? 100 - rate : 0, name: '' } // 保持环形图的形状
      ];

      // 应用更新后的配置
      this.oeeChart.setOption(this.oeeOption);
    },



    // 处理全局回车键事件
    handleGlobalKeyDown(event) {
      // 如果按下的是回车键
      if (event.key === 'Enter') {
        // 阻止默认的表单提交行为
        event.preventDefault();

        // 获取当前焦点元素
        const activeElement = document.activeElement;

        // 如果当前焦点在输入框上，不做额外处理，因为输入框已经有自己的回车键处理逻辑
        if (activeElement && (activeElement.tagName === 'INPUT' || activeElement.tagName === 'TEXTAREA')) {
          return;
        }

        // 如果当前没有焦点在输入框上，则触发下载配方按钮的点击事件
        if (this.qr_code) {
          console.log('全局回车键触发下载配方按钮点击');
          this.downloadRecipe();
        }
      }
    },

    getMaterialInfo() {
      // 实现获取物料信息的逻辑
    },
    // 切在线离线时权限验证
    handleOnOffLineRoleCheck() {
      if (this.monitorData.ControlMode.value === '1' || this.monitorData.ControlMode.value === '2') {
        // 当前是在线状态，切换到离线状态
        this.handleOnOffLineSwitch('0');
      } else {
        // 当前是离线状态，切换到在线/本地状态
        this.handleOnOffLineSwitch('2');
      }
    },

    // 处理在线离线模式切换
    handleOnOffLineSwitch(targetValue) {
      // 使用PcStatus/Remote路径进行写入操作
      const tag_key = `${this.stationAttr}Plc/PcStatus/Remote`;
      console.log('切换设备模式:', tag_key, targetValue, this.controlData[targetValue]);

      const sendJson = {
        Data: [
          {
            TagKey: tag_key,
            TagValue: targetValue
          }
        ],
        ClientName: 'SCADA_WEB'
      };
      const sendStr = JSON.stringify(sendJson);
      const topic = 'SCADA_WRITE/' + tag_key.split('/')[0];
      this.sendMessage(topic, sendStr);
      // 显示模式切换消息
      this.$message({
        message: this.$t('zhcy.switchingTo') + this.controlData[targetValue] + '……',
        type: 'info'
      });
    },

    handleWrite(key, value) {
      console.log('写入标签值:', key, value);

      // 如果是设备模式切换，显示提示消息
      if (key.includes('PcStatus/Remote')) {
        this.$message({
          message: this.$t('zhcy.switchingTo') + this.controlData[value] + '……',
          type: 'info'
        });
      }

      const sendJson = {
        Data: [
          {
            TagKey: key,
            TagValue: value
          }
        ],
        ClientName: 'SCADA_WEB'
      };
      const sendStr = JSON.stringify(sendJson);
      const topic = 'SCADA_WRITE/' + key.split('/')[0];
      this.sendMessage(topic, sendStr);
    },

    // 写入CurrentUser点位
    writeCurrentUser(userCode) {
      if (!this.mqttConnStatus || !this.clientMqtt) {
        console.warn('MQTT未连接，无法写入CurrentUser点位');
        return;
      }
      
      const sendJson = {
        Data: [
          {
            TagKey: `${this.stationAttr}Ais/AisStatus/CurrentUser`,
            TagValue: userCode || ''
          }
        ],
        ClientName: 'SCADA_WEB'
      };
      
      const sendStr = JSON.stringify(sendJson);
      const topic = `SCADA_WRITE/${this.stationAttr}Ais`;
      
      this.sendMessage(topic, sendStr);
      
      // 更新本地状态
      this.monitorData.CurrentUser.value = userCode || '';
      
      console.log('写入CurrentUser点位:', userCode);
    },
    
    // 初始化国际化文本
    initI18nText() {
      // 初始化设备模式文本
      this.controlData = {
        0: this.$t('zhcy.offlineMode'),
        1: this.$t('zhcy.onlineRemote'),
        2: this.$t('zhcy.onlineLocal')
      };
    }
  }
};
</script>

<style lang="less" scoped>
.app-container {
  padding: 10px;
  ::v-deep .el-header {
    padding: 0;
  }
  .header {
    ::v-deep .el-card__body {
      padding: 10px 15px 0 !important;
    }
  }
  .wrapTextSelect {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrapElForm {
    display: flex;
    ::v-deep .barcode {
      display: flex;
      width: 100%;
      .el-form-item--small {
        width: 90%;
        margin-right: 10px;
      }
      .pnl-input-item {
        width: 100%;
        .el-form-item__label {
          font-size: 20px;
          font-weight: bold;
          color: #333;
        }
        .input-with-button {
          display: flex;
          align-items: center;
          width: 100%;
          .el-input {
            flex: 1;
            margin-right: 10px;
          }
          .load-button {
            flex-shrink: 0;
            height: 45px;
          }
        }
        .el-input__inner {
          height: 45px;
          font-size: 20px;
          font-weight: bold;
          color: #333;
          background-color: #f8f8f8;
          border: 2px solid #79a0f1;
          border-radius: 4px;
        }
      }
    }
  }
  .pieChart {
    width: 100%;
    display: flex;
    div {
      width: 33%;
    }
    #capacityDom {
      height: 300px;
    }
    #oeeDom {
      height: 300px;
    }
    #readbitRateDom {
      height: 300px;
    }
  }
  .active {
    ::v-deep .el-input__inner {
      background-color: #ffff00;
    }
  }
  .dialog-footer {
    text-align: center;
  }
  .statuHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrappstyle {
    display: flex;
    align-items: center;
    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 12px;
        font-weight: 700;
      }
      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }
    p:last-child {
      margin-right: 0 !important;
    }
    .el-divider--vertical {
      width: 2px;
      height: 2em;
    }
  }
  .btnone-remote {
    background: #2ecc71; /* 亮绿色 - 在线/远程模式 */
    border-color: #2ecc71;
    color: #fff;
    font-size: 18px;
    font-weight: bold; /* 加粗文字 */
  }
  .btnone-local {
    background: #50d475; /* 普通绿色 - 在线/本地模式 */
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
    font-weight: bold; /* 加粗文字 */
  }
  .btnone {
    background: #50d475; /* 普通绿色 - 用于其他按钮 */
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
    font-weight: bold; /* 加粗文字 */
  }
  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
    font-size: 18px;
  }
  .btnone:active {
    background: #13887c;
  }
  .wholeline {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }
  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholeline1 {
    width: 20px;
    height: 20px;
  }
  .wholelinenormal1,
  .deviceGreen {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  }
  .deviceRed {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  }
  .deviceBlue {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #1890ff;
    box-shadow: 0 0 0.75em #1890ff, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  }
  .wholelineerror1,
  .deviceYellow {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #eeff00;
    box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  }
  .wholelinegray1 {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
  }
  .dialogTable {
    ::v-deep .el-dialog {
      margin-top: 5vh !important;
    }
  }
  .status-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 5px;
  }
  .statuHead {
    padding: 5px 0;
  }
  .statuHead > div {
    display: flex;
    align-items: center;
    flex-wrap: nowrap;
    width: 100%;
    overflow-x: auto;
    padding: 5px 0;
  }
  .peopleInfo {
    font-size: 16px !important;
    color: #333 !important;
  }
  .peopleInfo ul {
    margin: 0;
    padding: 0;
  }
  .peopleInfo li {
    text-align: center;
  }
  .peopleInfo li span:first-child {
    margin-bottom: 2px;
    color: #666 !important;
  }
  .task-list-table {
    width: 100%;
    margin-bottom: 15px;
  }
  .task-filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .status-checkboxes {
    display: flex;
    align-items: center;
  }
  .sync-account-btn {
    height: 40px !important;
    line-height: 40px !important;
    padding: 0 15px !important;
  }
  .el-checkbox__label {
    font-size: 20px !important;
  }

  .task-message {
    margin-top: 5px;
    color: #E6A23C;
    font-size: 14px;
    line-height: 1.2;
  }
}
</style>

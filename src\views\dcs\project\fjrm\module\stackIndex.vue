<template>
  <el-dialog :append-to-body="true" modal-append-to-body :title="modalTitle" :visible.sync="dialogVisible" :width="modalWidth" :before-close="handleClose">
    <div class="stock-info">
      <div v-for="(item,index) in node.stackData" :key="index" class="entirety">
        <div class="column"><span>{{ layer[index] + '层垛位：' }}</span><div class="box">{{ item.stock || '--' }}</div></div>
        <div class="column"><span>料框号：</span><div class="box">{{ item.waste_box_code || '--' }}</div></div>
        <div class="column"><span>料种：</span><div class="box">{{ item.material_code || '--' }}</div></div>
        <div class="column"><span>批次号：</span><div class="box">{{ item.lot_num || '--' }}</div></div>
        <div class="column"><span>合金牌号：</span><div class="box">{{ item.grade || '--' }}</div></div>
        <div class="column"><span>重量：</span><div class="box">{{ item.stock_width || '--' }}</div></div>
        <div class="column"><span>X坐标：</span><div class="box">{{ node.location_x || '--' }}</div></div>
        <div class="column"><span>Y坐标：</span><div class="box">{{ node.location_y || '--' }}</div></div>
        <div class="column"><span>Z坐标：</span><div class="box">{{ item.location_z || '--' }}</div></div>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'SELECTMODAL',
  props: {
    dict: {
      type: [Object, Array],
      default: () => ({})
    }
  },
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      dialogVisible: false,
      modalTitle: '查看明细',
      modalWidth: '60%',
      layer: {
        '0': '一',
        '1': '二',
        '2': '三',
        '3': '四'
      },
      node: {
        location_x: '',
        location_y: '',
        stackData: []
      }
    }
  },
  methods: {
    open(data) {
      this.node = {
        location_x: data.location_x,
        location_y: data.location_y,
        stackData: data.item !== '' && data.item.length > 0 ? data.item : []
      }
      this.$nextTick(() => {
        this.dialogVisible = true
      })
    },
    handleClose(done) {
      done()
    }
  }

}

</script>

<style scoped lang="less">
.stock-info{
  display: flex;
  .entirety{
    .column{
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      span{
        font-size: 18px;
        color: #fff;
        font-weight: 600;
        display: block;
        min-width: 100px;
        text-align: right;
      }
      svg{
        font-size: 30px;
        margin-left: 30px;
      }
      .box{
        font-size: 18px;
        border: 2px solid #57d6f6;
        padding: 5px;
        border-radius: 5px;
        background-color: #084aa0;
        color: #fff;
        width: 150px;
        text-align: center;
      }
      .del{
        margin-left: 5px;
        cursor: pointer;
      }
    }
  }
}
</style>

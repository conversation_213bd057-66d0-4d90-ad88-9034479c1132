import request from '@/utils/request'

// 查询通用配方维护信息
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeSel',
    method: 'post',
    data
  })
}

// 新增通用配方维护信息
export function add(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeIns',
    method: 'post',
    data
  })
}

// 修改通用配方维护信息
export function edit(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeUpd',
    method: 'post',
    data  
  })
}

// 删除通用配方维护信息
export function del(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeDel',
    method: 'post',
    data
  })
}

// 导出通用配方
export function EapRecipeCommonExport(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeExport',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 导入通用配方
export function EapRecipeCommonImport(data) {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeImport',
    method: 'post',
    data,
    responseType: 'blob'
  })
}

// 下载通用配方模板
export function EapRecipeCommonExportTemplate() {
  return request({
    url: 'aisEsbWeb/eap/core/common/EapRecipeExportTemplate',
    method: 'get',
    responseType: 'blob'
  })
}

export default { sel, add, edit, del }
# 弹窗功能使用说明

## 概述

本文档介绍了增强后的弹窗功能，支持更灵活的弹窗位置和类型配置。

## 后端接口改进

### EapCoreHmiMessage 接口

接口地址：`/eap/core/interf/recv/EapCoreHmiMessage`

#### 新增参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| popup_position | String | 否 | top-right | 弹窗位置 |
| popup_type | String | 否 | info | 弹窗类型 |

#### 弹窗位置选项

- `top-right`: 右上角（默认）
- `top-left`: 左上角
- `bottom-right`: 右下角
- `bottom-left`: 左下角
- `center`: 正中间（使用对话框形式）

#### 弹窗类型选项

- `success`: 成功（绿色）
- `warning`: 警告（橙色）
- `info`: 信息（蓝色，默认）
- `error`: 错误（红色）

#### 请求示例

```json
{
  "station_code": "ST001",
  "msg": "这是一条测试消息",
  "screen_control": "1",
  "msg_code": 100,
  "interval_second_time": 10,
  "popup_position": "top-left",
  "popup_type": "warning"
}
```

#### 响应示例

```json
{
  "result": "",
  "msg": "",
  "code": 0,
  "data": [
    {
      "hmi_show_id": "cde19a96733248e4986da07b5bef2d33",
      "screen_control": "1",
      "interval_second_time": 10,
      "popup_position": "center",
      "screen_code": "0",
      "popup_type": "info",
      "cim_msg": "当前下发配方板厚：0 ，模式：硬板 ，请点击确认",
      "cim_from": "AIS"
    }
  ],
  "count": 1
}
```

#### 重要字段说明

- **hmi_show_id**: 消息唯一标识，由后端自动生成（UUID格式，如：cde19a96733248e4986da07b5bef2d33）
- **popup_position**: 弹窗位置，支持 top-right、top-left、bottom-right、bottom-left、center
- **popup_type**: 弹窗类型，支持 success、warning、info、error

#### 兼容性说明

- 如果不传递 `popup_position` 参数，默认使用 `top-right`
- 如果不传递 `popup_type` 参数，默认使用 `info`
- 旧版本的调用方式完全兼容，不需要修改现有代码
- 后端会自动为每条消息生成唯一的 `hmi_show_id`

## 前端组件改进

### NotificationManager 组件

位置：`src/components/NotificationManager/index.vue`

#### 主要方法

1. **showNotification(options)** - 显示通知
   ```javascript
   this.$refs.notificationManager.showNotification({
     title: '标题',
     message: '消息内容',
     type: 'success',
     position: 'top-right',
     duration: 5000
   })
   ```

2. **showMessageNotification(message)** - 显示消息通知（兼容旧版本）
   ```javascript
   this.$refs.notificationManager.showMessageNotification(messageObject)
   ```

3. **快捷方法**
   ```javascript
   // 成功通知
   this.$refs.notificationManager.success('标题', '消息')
   
   // 警告通知
   this.$refs.notificationManager.warning('标题', '消息')
   
   // 信息通知
   this.$refs.notificationManager.info('标题', '消息')
   
   // 错误通知
   this.$refs.notificationManager.error('标题', '消息')
   ```

### MessageDialog 组件

位置：`src/components/MessageDialog/index.vue`

#### 功能特性

- 消息历史查询和显示
- 支持按来源和状态筛选
- 分页浏览
- 显示新的弹窗属性（位置、类型）

#### 使用方式

```vue
<template>
  <MessageDialog 
    :visible.sync="dialogVisible" 
    :current-station="currentStation"
    @message-click="handleMessageClick" />
</template>
```

## 数据库字段说明

### a_eap_me_station_hmi_show 表新增字段

| 字段名 | 类型 | 说明 | 默认值 |
|--------|------|------|--------|
| popup_position | String | 弹窗位置 | top-right |
| popup_type | String | 弹窗类型 | info |

## 使用示例

### 1. 基本使用（兼容旧版本）

```json
{
  "station_code": "ST001",
  "msg": "设备运行正常",
  "screen_control": "0",
  "interval_second_time": 5
}
```

### 2. 使用新功能

```json
{
  "station_code": "ST001",
  "msg": "设备需要维护",
  "screen_control": "1",
  "popup_position": "bottom-right",
  "popup_type": "warning"
}
```

### 3. 错误提示

```json
{
  "station_code": "ST001",
  "msg": "设备故障，请立即处理",
  "screen_control": "1",
  "popup_position": "top-left",
  "popup_type": "error"
}
```

### 4. 成功提示

```json
{
  "station_code": "ST001",
  "msg": "操作完成",
  "screen_control": "0",
  "interval_second_time": 3,
  "popup_position": "top-right",
  "popup_type": "success"
}
```

### 5. 正中间弹窗

```json
{
  "station_code": "ST001",
  "msg": "重要通知：系统将在5分钟后维护",
  "screen_control": "1",
  "popup_position": "center",
  "popup_type": "warning"
}
```

## 通知功能开关

### 系统参数配置

在系统参数表中添加以下配置：

| 参数代码 | 参数值 | 说明 |
|---------|--------|------|
| HMI_NOTIFICATION_ENABLED | true/false, 1/0, Y/N, on/off | 控制整个HMI通知功能是否启用 |

### 功能开关逻辑

1. **开发人员配置**：只能通过修改系统参数来控制
2. **功能级控制**：关闭时整个通知功能不可用
3. **界面隐藏**：关闭时连消息图标都不显示
4. **完全禁用**：关闭时不启动消息轮询，不显示任何通知相关功能

### 开关效果

- **启用时**：
  - 显示消息图标和未读消息数量
  - 启动消息轮询检查
  - 可以打开消息历史弹窗
  - 显示新消息通知

- **禁用时**：
  - 隐藏消息图标
  - 不启动消息轮询
  - 无法打开消息历史弹窗
  - 不显示任何通知

## 字体优化

### 通知样式改进

- **标题字体**：16px，加粗
- **内容字体**：14px，正常
- **行高**：1.5，提高可读性
- **字体差异**：缩小标题和内容的字体大小差异

### 正中间弹窗

- 使用MessageBox组件实现
- 支持自动关闭和手动关闭
- 字体样式与通知保持一致

## 注意事项

1. **向后兼容**：所有现有的调用方式都保持兼容，不需要修改现有代码
2. **参数验证**：系统会自动验证参数的有效性，无效参数会使用默认值
3. **数据库兼容**：旧数据在查询时会自动补充默认值
4. **性能影响**：新功能对系统性能影响极小
5. **正中间位置**：使用center位置时，弹窗会显示在屏幕正中间，更加醒目
6. **功能开关**：HMI_NOTIFICATION_ENABLED 是功能级开关，关闭时整个通知功能不可用
7. **开发人员控制**：功能开关只能通过修改系统参数控制，用户无法在界面上切换
8. **消息唯一性**：使用后端生成的 hmi_show_id 确保消息唯一性，避免重复显示
9. **自动去重**：相同 hmi_show_id 的消息只会显示一次，无需额外配置

## 测试建议

1. 测试所有弹窗位置组合
2. 测试所有弹窗类型组合
3. 测试兼容性（不传新参数的情况）
4. 测试无效参数的处理
5. 测试消息历史查询功能

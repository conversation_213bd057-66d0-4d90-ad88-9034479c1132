import request from '@/utils/request'

// 写入点位
export function DcsPointModificationSelect(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsPointModificationSelect',
    method: 'post',
    data
  })
}
// 读取点位值
export function DcsReadPointPositionSelect(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsReadPointPositionSelect',
    method: 'post',
    data
  })
}
//统计物料库存
export function DcsInventoryStatistics(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsInventoryStatistics',
    method: 'post',
    data
  })
}
//判断码头是否符合自动出库条件
export function DcsAutomaticOutboundJudgment(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsAutomaticOutboundJudgment',
    method: 'post',
    data
  })
}
//生成倒垛任务，判断目标库位是否是码头，如果是就出库，不是就倒垛
export function DcsInventoryScheduling(data) {
  return request({
    url: 'aisEsbWeb/dcs/project/fjrm/DcsInventoryScheduling',
    method: 'post',
    data
  })
}
export default { DcsPointModificationSelect, DcsReadPointPositionSelect ,DcsInventoryStatistics, DcsAutomaticOutboundJudgment,DcsInventoryScheduling }


<template>
  <el-container class="container-zlrm">
    <el-header>
      <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
        <el-col :span="7" style="display: flex;align-items: center;">
          <img
            :src="headerLogo"
            style="width: 300px; height: 90px; float: left; ;margin-top: 10px;margin-left: 10px;"
          >
        </el-col>
        <el-col :span="10" style="text-align: center; padding-top: 5px;height: 100%;line-height: 100px;">
          <span class="title">中铝瑞闽废料二期立体智能仓库</span>
        </el-col>
        <el-col :span="7" style="text-align: right; padding-right: 10px;line-height: 100px;"><span class="time"> {{ DateTime[0] + ' ' + DateTime[1] }} </span><span class="time time1"> {{ DateTime[2] }} </span></el-col>
      </el-row>
    </el-header>
    <el-main>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="car-box">
            <div class="car-box-left">
              <p>06</p>
              <p>05</p>
              <p>04</p>
              <p>03</p>
              <p>02</p>
              <p style="height: 60px;">01</p>
            </div>
            <div class="car-box-right">
              <MainChart
                ref="chart"
                v-loading="loading"
                :nodes="nodes"
                :width="'1870'"
                :height="'536'"
                :readonly="false"
                element-loading-text="拼命绘制流程图中"
              />
            </div>
          </div>
          <div class="car-gra">
            <div class="car-gra-left" />
            <div class="car-gra-right">
              <span v-for="(item,index) in 24" :key="index">{{ index > 8 ? `${index+1}` : `0${index+1}` }}</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-main>
    <div class="footer">
      <div class="box footer-left">
        <div class="header"><svg-icon icon-class="stock" />库存信息</div>
        <div>
          <template>
            <!-- <el-select
              v-model="inventory_date"
              placeholder="请选择时间范围"
              @change="updateInventoryDate"
              class="custom-select"
            >
              <el-option
                v-for="item in options2"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select> -->
            <el-date-picker
              v-model="inventory_date"
              type="datetimerange"
              size="small"
              align="right"
              unlink-panels
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              style="width: 50%"
              :default-time="['00:00:00', '23:59:59']"
              @change="updateInventoryDate"
              @input="$forceUpdate()"
            />
          </template>
          <template>
            <el-select
              v-model="TaskType"
              placeholder="请选择库存类型"
              class="custom-select"
              @change="updateInventoryDate"
            >
              <el-option
                v-for="item in options"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </template>

        </div>
        <div class="table-wrapper">
          <el-table
            border
            stripe
            highlight-current-row
            :data="this.inventoryData"
            style="width: 100%;"
            :cell-style="{ backgroundColor: '#031850 !important', color: '#00ffff !important' }"
            :header-cell-style="{ backgroundColor: '#031850 !important', color: '#00ffff !important' }"
          >
            <el-table-column prop="grade" label="合金编号" width="200" />
            <el-table-column prop="stock_width" label="重量(kg)" width="200" />
            <el-table-column prop="count" label="料框数" />
          </el-table>
        </div>
        <!-- <div class="image"><img src="@/assets/images/fjrm/zsCss.png" alt=""></div> -->
      </div>
      <div class="box footer-centet">
        <div class="header"><svg-icon icon-class="rmCar" />平板小车信息</div>
        <div class="table-wrapper">
          <table>
            <thead>
              <tr class="event">
                <th>行车</th>
                <th>自动就绪状态</th>
                <th>按钮</th>
                <th>控制按钮</th>
                <th>自动出空框</th>
                <th>位置状态</th>
                <th>故障复位</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>1#平板小车</td>
                <td v-if="this.SlRgvPlc01stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc01stop }}</td>
                <td v-if="this.SlRgvPlc01stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc01stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc01'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc01'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc01'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;" @click="(smallCarDetail('1'))">详</el-button> -->
                </td>
                <td />
                <td style="color: aqua;">{{ this.SlRgvPlc01station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc01'))">复位</el-tag></td>
              </tr>
              <tr>
                <td>2#平板小车</td>
                <td v-if="this.SlRgvPlc02stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc02stop }}</td>
                <td v-if="this.SlRgvPlc02stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc02stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc02'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc02'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc02'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;" @click="(smallCarDetail('2'))">详</el-button> -->
                </td>
                <td>
                  <el-switch v-model="isActive2" active-color="#13ce66" inactive-color="#ff4949" @change="OutboundTask2" />
                </td>
                <td style="color: aqua;">{{ this.SlRgvPlc02station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc02'))">复位</el-tag></td>
              </tr>
              <tr>
                <td>3#平板小车</td>
                <td v-if="this.SlRgvPlc03stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc03stop }}</td>
                <td v-if="this.SlRgvPlc03stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc03stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc03'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc03'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc03'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;" @click="(smallCarDetail('3'))">详</el-button> -->
                </td>
                <td />
                <td style="color: aqua;">{{ this.SlRgvPlc03station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc03'))">复位</el-tag></td>
              </tr>
              <tr>
                <td>4#平板小车</td>
                <td v-if="this.SlRgvPlc04stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc04stop }}</td>
                <td v-if="this.SlRgvPlc04stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc04stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc04'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc04'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc04'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;" @click="(smallCarDetail('4'))">详</el-button> -->
                </td>
                <td>
                  <el-switch v-model="isActive4" active-color="#13ce66" inactive-color="#ff4949" @change="OutboundTask4" />
                </td>
                <td style="color: aqua;">{{ this.SlRgvPlc04station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc04'))">复位</el-tag></td>
              </tr>
              <tr>
                <td>5#平板小车</td>
                <td v-if="this.SlRgvPlc05stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc05stop }}</td>
                <td v-if="this.SlRgvPlc05stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc05stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc05'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc05'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc05'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;" @click="(smallCarDetail('5'))">详</el-button> -->
                </td>
                <td />
                <td style="color: aqua;">{{ this.SlRgvPlc05station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc05'))">复位</el-tag></td>
              </tr>
              <tr>
                <td>6#平板小车</td>
                <td v-if="this.SlRgvPlc06stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc06stop }}</td>
                <td v-if="this.SlRgvPlc06stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc06stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc06'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc06'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc04'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;"  @click="(smallCarDetail('6'))">详</el-button> -->
                </td>
                <td />
                <td style="color: aqua;">{{ this.SlRgvPlc06station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc06'))">复位</el-tag></td>
              </tr>
              <tr>
                <td>7#平板小车</td>
                <td v-if="this.SlRgvPlc07stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc07stop }}</td>
                <td v-if="this.SlRgvPlc07stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc07stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc07'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc07'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc07'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;"  @click="(smallCarDetail('7'))">详</el-button> -->
                </td>
                <td />
                <td style="color: aqua;">{{ this.SlRgvPlc07station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc07'))">复位</el-tag></td>
              </tr>
              <tr>
                <td>8#平板小车</td>
                <td v-if="this.SlRgvPlc08stop==='自动就绪'" style="color: aqua;">{{ this.SlRgvPlc08stop }}</td>
                <td v-if="this.SlRgvPlc08stop==='未自动就绪'" style="color: red;">{{ this.SlRgvPlc08stop }}</td>
                <td>
                  <el-tag type="danger" effect="dark" style="width: 40px;" @click="(stop('SlRgvPlc08'))">停止</el-tag>
                </td>
                <td style="display: flex;align-items: center;justify-content: center;">
                  <el-tag type="success" effect="dark" style="width: 40px;" @click="(outbound('SlRgvPlc08'))">入库</el-tag>
                  <el-tag type="warning" effect="dark" style="width: 40px;margin:0 10px" @click="(warehousing('SlRgvPlc08'))">出库</el-tag>
                  <!-- <el-button type="danger" circle style="background-color: red;width: 25px;height: 25px;display: flex;align-items: center;justify-content: center;"  @click="(smallCarDetail('8'))">详</el-button> -->
                </td>
                <td />
                <td style="color: aqua;">{{ this.SlRgvPlc08station }}</td>
                <td><el-tag type="success" effect="dark" style="width: 40px;" @click="(FaultReset('SlRgvPlc08'))">复位</el-tag></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="box footer-right">
        <div class="header"><svg-icon icon-class="xingChe" />行车作业信息</div>
        <div class="table-wrapper">
          <table>
            <thead>
              <tr>
                <th>行车详情</th>
                <td>
                  <!-- <el-button  @click="crownBlockDetail">西行车详情</el-button> -->
                  <el-tag type="warning" effect="dark" style="width: 50px;margin:0 10px" @click="crownBlockDetail">西行车</el-tag>
                </td>
                <th>
                  <!-- <el-button  @click="crownBlockDetailEast">东行车详情</el-button> -->
                  <el-tag type="warning" effect="dark" style="width: 50px;margin:0 10px" @click="crownBlockDetailEast">东行车</el-tag>
                </th>
              </tr>
            </thead>
            <thead>
              <tr class="event">
                <th>行车名字</th>
                <td>西行车</td>
                <th>东行车</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <th>当前模式</th>
                <td>{{ this.pattern1 }}</td>
                <th>{{ this.pattern2 }}</th>
              </tr>
              <tr class="event">
                <th>任务类型</th>
                <td>{{ this.TaskType1 }}</td>
                <th>{{ this.TaskType2 }}</th>
              </tr>
              <tr>
                <th>任务执行中</th>
                <td>{{ this.taskID1 }}</td>
                <th>{{ this.taskID2 }}</th>
              </tr>
              <tr class="event">
                <th>当前位置</th>
                <td>{{ -((this.westY-2000)/3100-6).toFixed(0)+"-"+((this.westX-2800)/3400+1).toFixed(0)+"-"+((this.zCoordinates1-0+375)/1375).toFixed(0) }}</td>
                <th>{{ -((this.eastY-2000)/3100-6).toFixed(0)+"-"+((this.eastX-2800)/3400+1).toFixed(0)+"-"+((this.zCoordinates2-0+375)/1375).toFixed(0) }}</th>
              </tr>
              <tr>
                <th>目标位置</th>
                <td>{{ -((this.westTaskY-2000)/3100-6).toFixed(0)+"-"+((this.westTaskX-2800)/3400+1).toFixed(0)+"-"+((this.westTaskZ-0+375)/1375).toFixed(0) }}</td>
                <th>{{ -((this.eastTaskY-2000)/3100-6).toFixed(0)+"-"+((this.eastTaskX-2800)/3400+1).toFixed(0)+"-"+((this.eastTaskZ-0+375)/1375).toFixed(0) }}</th>
              </tr>
              <!-- <tr class="event">
                <th>起升位置</th>
                <td>{{((this.zCoordinates1-0+375)/1375).toFixed(0)}}</td>
                <th>{{((this.zCoordinates2-0+375)/1375).toFixed(0)}}</th>
              </tr> -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <selectModal ref="paneal" class="fjTable" />
    <smallCarDetail ref="smallCarDetail" class="fjTable" />
    <crownBlockDetail ref="crownBlockDetail" class="fjTable" />
    <crownBlockDetailEast ref="crownBlockDetailEast" class="fjTable" />
  </el-container>
</template>
<script>
import { DcsFmodMapMeStockSelect } from '@/api/dcs/project/fjrm/stock/stock'
import crudTask from '@/api/dcs/project/fjrm/intefTask/intefTask'
import LargeScreenPage from '@/api/dcs/project/fjrm/LargeScreenPage/LargeScreenPage'
import Cookies from 'js-cookie'
import selectModal from '@/components/selectModal'
import crownBlock from '@/api/dcs/project/fjrm/crownBlock.json'
import { getFormatDate } from '@/utils/index.js'
import headerLogo from '@/assets/images/fjrm/logo.png'
import MainChart from './wmsDashboard/index'
import smallCarDetail from '../module/smallCarDetail.vue'
import crownBlockDetail from '../module/crownBlockDetail.vue'
import crownBlockDetailEast from '../module/crownBlockDetailEast.vue'
import { selCellIP } from '@/api/core/center/cell'
import axios from 'axios'
import Paho from 'paho-mqtt'
export default {
  name: 'HelloWorld',
  components: { selectModal, smallCarDetail, crownBlockDetail, crownBlockDetailEast, MainChart },
  data() {
    return {
      headerLogo,
      DateTime: [],
      loading: false,
      nodes: [],
      widthPx: 0.0230, // 页面宽度1870/大天车的可活动范围 = 0.0239  1mm = 0.0239px
      heightPx: 0.029, // 页面高度530/小天车的可活动范围 =  0.03697
      carVrituWorkRegionWidth: 1870, //  西天车 左边天车  大天车 天车网页活动范围X长度:1870是实际活动范围
      carVrituWorkRegionHeight: 456, // 西天车 左边天车  小天车 天车网页活动范围Y长度 530是实际活动范围
      carAuctalWorkRegionWidth: 78200, // 天车实际活动范围X长度:2800 - 81000
      carAuctalWorkRegionHeight: 15500, // 小车实际活动范围Y长度:2000 - 17500
      westX: '',
      westY: '',
      eastX: '',
      eastY: '',
      westTaskX: '',
      westTaskY: '',
      westTaskZ: '',
      eastTaskX: '',
      eastTaskY: '',
      eastTaskZ: '',
      tagKeyList: [
        'SlCarPlc01/PlcStatus/Tag023', // 西边x大车激光
        'SlCarPlc01/PlcStatus/Tag022', // 西边y小车车激光
        'SlCarPlc02/PlcStatus/Tag023', // 东边x大车激光
        'SlCarPlc02/PlcStatus/Tag022', // 东边y小车车激光
        'SlCarPlc01/PlcStatus/Tag011', // 任务.X 西车
        'SlCarPlc01/PlcStatus/Tag012', // 任务.Y
        'SlCarPlc01/PlcStatus/Tag013', // 任务.Z
        'SlCarPlc02/PlcStatus/Tag011', // 任务.X 东车
        'SlCarPlc02/PlcStatus/Tag012', // 任务.Y
        'SlCarPlc02/PlcStatus/Tag013', // 任务.Z
        'SlRgvPlc01/PlcStatus/Tag001', // rgv01的自动就绪状态
        'SlRgvPlc02/PlcStatus/Tag001', // rgv02的自动就绪状态
        'SlRgvPlc03/PlcStatus/Tag001', // rgv03的自动就绪状态
        'SlRgvPlc04/PlcStatus/Tag001', // rgv04的自动就绪状态
        'SlRgvPlc05/PlcStatus/Tag001', // rgv05的自动就绪状态
        'SlRgvPlc06/PlcStatus/Tag001', // rgv06的自动就绪状态
        'SlRgvPlc07/PlcStatus/Tag001', // rgv07的自动就绪状态
        'SlRgvPlc08/PlcStatus/Tag001', // rgv08的自动就绪状态
        'SlRgvPlc01/PlcStatus/Tag004', // rgv01的位置状态
        'SlRgvPlc02/PlcStatus/Tag004', // rgv02的位置状态
        'SlRgvPlc03/PlcStatus/Tag004', // rgv03的位置状态
        'SlRgvPlc04/PlcStatus/Tag004', // rgv04的位置状态
        'SlRgvPlc05/PlcStatus/Tag004', // rgv05的位置状态
        'SlRgvPlc06/PlcStatus/Tag004', // rgv06的位置状态
        'SlRgvPlc07/PlcStatus/Tag004', // rgv07的位置状态
        'SlRgvPlc08/PlcStatus/Tag004', // rgv08的位置状态
        'SlCarPlc01/PlcStatus/Tag009', // 1号天车的自动状态
        'SlCarPlc02/PlcStatus/Tag009', // 2号天车的自动状态
        'SlCarPlc01/DcsStatus/RcsWriteR', // 1号天车的任务类型
        'SlCarPlc02/DcsStatus/RcsWriteR', // 2号天车的任务类型
        'SlCarPlc01/PlcStatus/Tag016', // 1号天车的任务号
        'SlCarPlc02/PlcStatus/Tag016', // 2号天车的任务号
        'SlCarPlc01/PlcStatus/Tag024', // 1号天车的升位置
        'SlCarPlc02/PlcStatus/Tag024' // 2号天车的升位置
      ],
      SlRgvPlcTagKeyList: {
        '01': {
          // Tag004: { tagKey: 'SlRgvPlc01/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc01/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc01/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet4/PlcStatus/Tag003', tagDes: '1#平车超高扫描', tagValue: '' }
        },
        '02': {
          // Tag004: { tagKey: 'SlRgvPlc02/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc02/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc02/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet4/PlcStatus/Tag016', tagDes: '2#平车超高扫描', tagValue: '' }
        },
        '03': {
          // Tag004: { tagKey: 'SlRgvPlc03/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc03/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc03/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet3/PlcStatus/Tag016', tagDes: '3#平车超高扫描', tagValue: '' }
        },
        '04': {
          // Tag004: { tagKey: 'SlRgvPlc04/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc04/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc04/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet3/PlcStatus/Tag003', tagDes: '4#平车超高扫描', tagValue: '' }
        },
        '05': {
          // Tag004: { tagKey: 'SlRgvPlc05/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc05/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc05/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet3/PlcStatus/Tag032', tagDes: '5#平车超高扫描', tagValue: '' }
        },
        '06': {
          // Tag004: { tagKey: 'SlRgvPlc06/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc06/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc06/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet3/PlcStatus/Tag040', tagDes: '6#平车超高扫描', tagValue: '' }
        },
        '07': {
          // Tag004: { tagKey: 'SlRgvPlc07/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc07/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc07/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet4/PlcStatus/Tag032', tagDes: '7#平车超高扫描', tagValue: '' }
        },
        '08': {
          // Tag004: { tagKey: 'SlRgvPlc08/PlcStatus/Tag004', tagDes: '小车当前工位', tagValue: '' },
          Tag005: { tagKey: 'SlRgvPlc08/PlcStatus/Tag005', tagDes: '台面有料信号', tagValue: '' },
          Tag008: { tagKey: 'SlRgvPlc08/PlcStatus/Tag008', tagDes: '重量', tagValue: '' },
          GroundCabinet: { tagKey: 'GroundCabinet4/PlcStatus/Tag040', tagDes: '1#平车超高扫描', tagValue: '' }
        },
        '1278key': {
          QRcode1: { tagKey: 'SlAisSimPlc/WmsStatus/QRcode1', tagDes: '是否手动录入料框码', tagValue: '' },
          QRcode2: { tagKey: 'SlAisSimPlc/WmsStatus/QRcode2', tagDes: '手动录入的料框码', tagValue: '' }
        },
        '3456key': {
          QRcode1: { tagKey: 'SlAisSimPlc/WmsStatus/QRcode3', tagDes: '是否手动录入料框码', tagValue: '' },
          QRcode2: { tagKey: 'SlAisSimPlc/WmsStatus/QRcode4', tagDes: '手动录入的料框码', tagValue: '' }
        }
      },
      clients: {},
      SlRgvPlc01stop: '自动就绪',
      SlRgvPlc02stop: '自动就绪',
      SlRgvPlc03stop: '自动就绪',
      SlRgvPlc04stop: '自动就绪',
      SlRgvPlc05stop: '自动就绪',
      SlRgvPlc06stop: '自动就绪',
      SlRgvPlc07stop: '自动就绪',
      SlRgvPlc08stop: '自动就绪',
      SlRgvPlc01station: '库内到位',
      SlRgvPlc02station: '库内到位',
      SlRgvPlc03station: '库内到位',
      SlRgvPlc04station: '库内到位',
      SlRgvPlc05station: '库内到位',
      SlRgvPlc06station: '库内到位',
      SlRgvPlc07station: '库内到位',
      SlRgvPlc08station: '库内到位',
      pattern1: '手动',
      pattern2: '手动',
      TaskType1: '',
      TaskType2: '',
      taskID1: '',
      taskID2: '',
      yCoordinates1: '',
      yCoordinates2: '',
      xCoordinates1: '',
      xCoordinates2: '',
      zCoordinates1: '',
      zCoordinates2: '',
      inventoryData: [],
      inventory_date: '',
      TaskType: '',
      options: [
        { label: '出库统计', value: 'out' },
        { label: '入库统计', value: 'in' },
        { label: '库存统计', value: '' }
      ],
      options2: [
        { label: '今日', value: 'day' },
        { label: '本月', value: 'month' },
        { label: '今年', value: 'year' },
        { label: '全部', value: '' }
      ],
      isActive2: false,
      isActive4: false,
      Outbound2Timer: null,
      Outbound4Timer: null,
      mqttClient: {}
    }
  },
  created() {
    this.DateTime = getFormatDate().split(' ')
    this.timer = setInterval(() => {
      this.DateTime = getFormatDate().split(' ')
    }, 1000)
    this.getStock()
    this.updateInventoryDate()
    // this.DcsInventoryStatistics()
    // this.timer2 = setInterval(() => {
    //   this.DcsInventoryStatistics()
    // }, 30000)
  },
  methods: {
    trainDetail() {
      const search = {
        page: 1,
        size: 20,
        sort: 'function_id asc',
        user_name: Cookies.get('userName')
      }
      this.$refs.paneal.open({
        type: 'pljh',
        checkType: '',
        search
      })
    },
    async getStock() {
      DcsFmodMapMeStockSelect({
        user_name: Cookies.get('userName')
      }).then(res => {
        if (res.code === 0 && res.data.length > 0) {
          // res.data.map(item => {
          //   item.type = item.attribute1 && item.attribute1 === 'Y' ? 'rgv' : 'stock'
          //   item.x = (item.location_x - 2800) * this.widthPx
          //   item.y = (item.location_y - 2000) * this.heightPx
          // })
          for (let i = 0; i < res.data.length; i++) {
            if (res.data[i].attribute1 === 'Y') {
              res.data[i].type = 'rgv'
              Object.keys(this.SlRgvPlcTagKeyList).forEach(key => {
                // stock_code的值为 AMT01 ... BMT03 ...
                const stockCodeKey = res.data[i].stock_code.split('_')[0].slice(3)
                if (this.SlRgvPlcTagKeyList[stockCodeKey]) {
                  res.data[i].rgvData = { ...this.SlRgvPlcTagKeyList[stockCodeKey], 'tagIndex': stockCodeKey }
                }
                // if (['01', '02', '07', '08'].includes(stockCodeKey)) {
                //   // 添加1278key的对象
                //   res.data[i].rgvData = {
                //     ...res.data[i].rgvData,
                //     ...this.SlRgvPlcTagKeyList['1278key']
                //   }
                // } else if (['03', '04', '05', '06'].includes(stockCodeKey)) {
                //   // 添加3456key的对象
                //   res.data[i].rgvData = {
                //     ...res.data[i].rgvData,
                //     ...this.SlRgvPlcTagKeyList['3456key']
                //   }
                // }
              })
            } else {
              res.data[i].type = 'stock'
            }
            res.data[i].x = (res.data[i].location_x - 2800) * this.widthPx
            res.data[i].y = (res.data[i].location_y - 2000) * this.heightPx
          }
          res.data.push(...crownBlock)
          this.nodes = res.data
          this.toStartWatch()
        }
      })
    },
    toStartWatch() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: '请先维护服务、单元信息',
              type: 'error'
            })
            return
          }
          // var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
          this.getScadaTagValue(result.ip, result.webapi_port)
          this.$nextTick(() => {
            this.connectMQTT(result.ip, +result.mqtt_port, (c) => {
              this.clients['SCADA_CHANGE'] = c
              this.tagKeyList.forEach(item => {
                c.subscribe(`SCADA_CHANGE/${item}`, {
                  onSuccess: () => {
                    console.debug('Subscribe success.')
                  },
                  onFailure: (responseObject) => {
                    console.error('Subscribe fail:', responseObject.errorMessage)
                  }
                })
              })
            })
            this.connectMQTT(result.ip, +result.mqtt_port, (c) => {
              this.clients['SCADA_CHANGE'] = c
              Object.keys(this.SlRgvPlcTagKeyList).forEach(key => {
                Object.keys(this.SlRgvPlcTagKeyList[key]).forEach(item => {
                  c.subscribe(`SCADA_CHANGE/${this.SlRgvPlcTagKeyList[key][item].tagKey}`, {
                    onSuccess: () => {
                      console.debug('Subscribe success.')
                    },
                    onFailure: (responseObject) => {
                      console.error('Subscribe fail:', responseObject.errorMessage)
                    }
                  })
                })
              })
            })
          })
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },
    getScadaTagValue(ip, port) {
      var readTagArray = []
      for (var i = 0; i < this.tagKeyList.length; i++) {
        var readTag = {}
        readTag.tag_key = this.tagKeyList[i].toString()
        readTagArray.push(readTag)
      }
      Object.keys(this.SlRgvPlcTagKeyList).forEach(key => {
        Object.keys(this.SlRgvPlcTagKeyList[key]).forEach(item => {
          readTagArray.push({
            tag_key: this.SlRgvPlcTagKeyList[key][item].tagKey.toString()
          })
        })
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
      } else {
        path = 'http://' + ip + ':' + port + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  var TagKey = result[k].tag_key.toString()
                  var tagValue = result[k].tag_value && result[k].tag_value.toString() || ''
                  if (TagKey === 'SlCarPlc01/PlcStatus/Tag023') { // 西边大车激光
                    this.westX = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag022') { // 西边小车激光
                    this.westY = tagValue
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag023') { // 东边大车激光
                    this.eastX = tagValue
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag022') { // 东边小车激光
                    this.eastY = tagValue
                  } else if (TagKey === 'SlRgvPlc01/PlcStatus/Tag001') { // rgv01的自动就绪状态
                    this.SlRgvPlc01stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc02/PlcStatus/Tag001') { // rgv02的自动就绪状态
                    this.SlRgvPlc02stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc03/PlcStatus/Tag001') { // rgv03的自动就绪状态
                    this.SlRgvPlc03stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc04/PlcStatus/Tag001') { // rgv04的自动就绪状态
                    this.SlRgvPlc04stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc05/PlcStatus/Tag001') { // rgv05的自动就绪状态
                    this.SlRgvPlc05stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc06/PlcStatus/Tag001') { // rgv06的自动就绪状态
                    this.SlRgvPlc06stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc07/PlcStatus/Tag001') { // rgv07的自动就绪状态
                    this.SlRgvPlc07stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc08/PlcStatus/Tag001') { // rgv08的自动就绪状态
                    this.SlRgvPlc08stop = tagValue === '1' ? '自动就绪' : '未自动就绪'
                  } else if (TagKey === 'SlRgvPlc01/PlcStatus/Tag004') { // rgv01的位置状态
                    this.SlRgvPlc01station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT01_RGV",tagValue})
                  } else if (TagKey === 'SlRgvPlc02/PlcStatus/Tag004') { // rgv02的位置状态
                    this.SlRgvPlc02station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT02_RGV",tagValue})
                  } else if (TagKey === 'SlRgvPlc03/PlcStatus/Tag004') { // rgv03的位置状态
                    this.SlRgvPlc03station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT03_RGV",tagValue})
                  } else if (TagKey === 'SlRgvPlc04/PlcStatus/Tag004') { // rgv04的位置状态
                    this.SlRgvPlc04station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT04_RGV",tagValue})
                  } else if (TagKey === 'SlRgvPlc05/PlcStatus/Tag004') { // rgv05的位置状态
                    this.SlRgvPlc05station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT05_RGV",tagValue})
                  } else if (TagKey === 'SlRgvPlc06/PlcStatus/Tag004') { // rgv06的位置状态
                    this.SlRgvPlc06station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT06_RGV",tagValue})
                  } else if (TagKey === 'SlRgvPlc07/PlcStatus/Tag004') { // rgv07的位置状态
                    this.SlRgvPlc07station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT07_RGV",tagValue})
                  } else if (TagKey === 'SlRgvPlc08/PlcStatus/Tag004') { // rgv08的位置状态
                    this.SlRgvPlc08station = tagValue === '0' ? '运行中' : tagValue === '1' ? '库外到位' : tagValue === '2' ? '库内到位' : '其他'
                    LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT08_RGV",tagValue})
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag009') { // 1号天车的自动状态
                    this.pattern1 = tagValue === '1' ? '自动' : '手动' // 1表示自动，0表示手动
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag009') { // 2号天车的自动状态
                    this.pattern2 = tagValue === '1' ? '自动' : '手动'
                  } else if (TagKey === 'SlCarPlc01/DcsStatus/RcsWriteR') { // 1号天车的任务类型
                    this.TaskType1 = tagValue === '1' ? '叉取' : tagValue === '2' ? '放置' : tagValue === '3' ? '避让' : '其他'
                  } else if (TagKey === 'SlCarPlc02/DcsStatus/RcsWriteR') { // 2号天车的任务类型
                    this.TaskType2 = tagValue === '1' ? '叉取' : tagValue === '2' ? '放置' : tagValue === '3' ? '避让' : '其他'
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag016') { // 1号天车的任务号
                    this.taskID1 = tagValue === '1' ? '是' : '否'
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag016') { // 2号天车的任务号
                    this.taskID2 = tagValue === '1' ? '是' : '否'
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag024') { // 1号天车的升位置
                    this.zCoordinates1 = tagValue
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag024') { // 2号天车的升位置
                    this.zCoordinates2 = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag011') { // 任务.X
                    this.westTaskX = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag012') { // 任务.Y
                    this.westTaskY = tagValue
                  } else if (TagKey === 'SlCarPlc01/PlcStatus/Tag013') { // 任务.Z
                    this.westTaskZ = tagValue
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag011') { // 任务.X
                    this.eastTaskX = tagValue
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag012') { // 任务.Y
                    this.eastTaskY = tagValue
                  } else if (TagKey === 'SlCarPlc02/PlcStatus/Tag013') { // 任务.Z
                    this.eastTaskZ = tagValue
                  }
                  Object.keys(this.SlRgvPlcTagKeyList).forEach(key => {
                    Object.keys(this.SlRgvPlcTagKeyList[key]).forEach(item => {
                      if (this.SlRgvPlcTagKeyList[key][item].tagKey === TagKey) {
                        this.SlRgvPlcTagKeyList[key][item].tagValue = tagValue
                      }
                    })
                  })
                }
                this.updateWestCarLocation()
                this.updateEastCarLocation()
              }
            }
          }
        })
        .catch(ex => {
          // this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    connectMQTT(host, port, onConnected) {
      const id = `mqtt_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
      const mqttClient = new Paho.Client(
        host,
        port,
        id
      )
      this.mqttClient = mqttClient
      const onSuccess = () => {
        console.debug(`ws://{${host}:${port}}/mqtt is connected.`)
        onConnected && onConnected(mqttClient)
      }
      const onFailure = (responseObject) => {
        console.error(`ws://{${host}:${port}}/mqtt is disconnected: ${responseObject.errorMessage}`)
        // this.$message({ message: '连接服务器[' + host + ':' + port + ']失败：' + responseObject.errorMessage, type: 'error' })
        setTimeout(() => {
          console.log('Attempting to reconnect...')
          mqttClient.connect({ onSuccess, onFailure })
        }, 15000) // 15秒后尝试重连
      }
      mqttClient.onConnectionLost = (responseObject) => {
        if (responseObject.errorCode !== 0) {
          console.error('onConnectionLost:', responseObject.errorMessage)
          // this.$message({ message: '与服务器[' + host + ':' + port + ']断开连接，5s后将会自动重连...', type: 'error' })
          setTimeout(() => {
            console.log('Attempting to reconnect...')
            mqttClient.connect({ onSuccess, onFailure })
          }, 5000) // 5秒后尝试重连
        }
      }
      mqttClient.onMessageArrived = (message) => {
        const topic = message.destinationName
        const payload = message.payloadString
        const data = JSON.parse(payload)
        if (data && data.TagNewValue && data.TagNewValue !== '') {
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag023') {
            this.westX = data.TagNewValue
            if (this.westY !== '' && this.westX !== '') {
              this.updateWestCarLocation()
            }
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag022') {
            this.westY = data.TagNewValue
            if (this.westY !== '' && this.eastX !== '') {
              this.updateWestCarLocation()
            }
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag023') {
            this.eastX = data.TagNewValue
            if (this.eastY !== '' && this.eastX !== '') {
              this.updateEastCarLocation()
            }
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag022') {
            this.eastY = data.TagNewValue
            if (this.eastY !== '' && this.eastX !== '') {
              this.updateEastCarLocation()
            }
          }
          if (data.TagKey === 'SlRgvPlc01/PlcStatus/Tag001') { // rgv01的自动就绪状态
            this.SlRgvPlc01stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }
          if (data.TagKey === 'SlRgvPlc02/PlcStatus/Tag001') { // rgv02的自动就绪状态
            this.SlRgvPlc02stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }
          if (data.TagKey === 'SlRgvPlc03/PlcStatus/Tag001') { // rgv03的自动就绪状态
            this.SlRgvPlc03stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }
          if (data.TagKey === 'SlRgvPlc04/PlcStatus/Tag001') { // rgv04的自动就绪状态
            this.SlRgvPlc04stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }
          if (data.TagKey === 'SlRgvPlc05/PlcStatus/Tag001') { // rgv05的自动就绪状态
            this.SlRgvPlc05stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }

          if (data.TagKey === 'SlRgvPlc06/PlcStatus/Tag001') { // rgv06的自动就绪状态
            this.SlRgvPlc06stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }
          if (data.TagKey === 'SlRgvPlc07/PlcStatus/Tag001') { // rgv07的自动就绪状态
            this.SlRgvPlc07stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }
          if (data.TagKey === 'SlRgvPlc08/PlcStatus/Tag001') { // rgv08的自动就绪状态
            this.SlRgvPlc08stop = data.TagNewValue === '1' ? '自动就绪' : '未自动就绪'
          }
          if (data.TagKey === 'SlRgvPlc01/PlcStatus/Tag004') { // rgv01的位置状态
            this.SlRgvPlc01station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT01_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlRgvPlc02/PlcStatus/Tag004') { // rgv02的位置状态
            this.SlRgvPlc02station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT02_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlRgvPlc03/PlcStatus/Tag004') { // rgv03的位置状态
            this.SlRgvPlc03station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT03_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlRgvPlc04/PlcStatus/Tag004') { // rgv04的位置状态
            this.SlRgvPlc04station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT04_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlRgvPlc05/PlcStatus/Tag004') { // rgv05的位置状态
            this.SlRgvPlc05station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT05_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlRgvPlc06/PlcStatus/Tag004') { // rgv06的位置状态
            this.SlRgvPlc06station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"BMT06_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlRgvPlc07/PlcStatus/Tag004') { // rgv07的位置状态
            this.SlRgvPlc07station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT07_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlRgvPlc08/PlcStatus/Tag004') { // rgv08的位置状态
            this.SlRgvPlc08station = data.TagNewValue === '0' ? '运行中' : data.TagNewValue === '1' ? '库外到位' : data.TagNewValue === '2' ? '库内到位' : '其他'
            LargeScreenPage.DcsDockCoordinateUpdate({stock_code:"AMT08_RGV",coordinate:data.TagNewValue})
            this.getStock();
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag009') { // 1号天车的自动状态
            this.pattern1 = data.TagNewValue === '1' ? '自动' : '手动' // 1表示自动，0表示手动
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag009') { // 2号天车的自动状态
            this.pattern2 = data.TagNewValue === '1' ? '自动' : '手动'
          }
          if (data.TagKey === 'SlCarPlc01/DcsStatus/RcsWriteR') { // 1号天车的任务类型
            this.TaskType1 = data.TagNewValue === '1' ? '叉取' : data.TagNewValue === '2' ? '放置' : data.TagNewValue === '3' ? '避让' : '其他'
          }
          if (data.TagKey === 'SlCarPlc02/DcsStatus/RcsWriteR') { // 2号天车的任务类型
            this.TaskType2 = data.TagNewValue === '1' ? '叉取' : data.TagNewValue === '2' ? '放置' : data.TagNewValue === '3' ? '避让' : '其他'
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag016') { // 1号天车的任务号
            this.taskID1 = data.TagNewValue === '1' ? '是' : '否'
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag016') { // 2号天车的任务号
            this.taskID2 = data.TagNewValue === '1' ? '是' : '否'
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag024') { // 1号天车的升位置
            this.zCoordinates1 = data.TagNewValue
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag024') { // 2号天车的升位置
            this.zCoordinates2 = data.TagNewValue
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag011') {
            this.westTaskX = data.TagNewValue // 任务.X  -西车
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag012') {
            this.westTaskY = data.TagNewValue // 任务.Y
          }
          if (data.TagKey === 'SlCarPlc01/PlcStatus/Tag013') {
            this.westTaskZ = data.TagNewValue // 任务.Z
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag011') {
            this.eastTaskX = data.TagNewValue // 任务.X  -东车
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag012') {
            this.eastTaskY = data.TagNewValue // 任务.Y
          }
          if (data.TagKey === 'SlCarPlc02/PlcStatus/Tag013') {
            this.eastTaskZ = data.TagNewValue // 任务.Z
          }
          Object.keys(this.SlRgvPlcTagKeyList).forEach(key => {
            Object.keys(this.SlRgvPlcTagKeyList[key]).forEach(item => {
              if (this.SlRgvPlcTagKeyList[key][item].tagKey === data.TagKey) {
                this.SlRgvPlcTagKeyList[key][item].tagValue = data.TagNewValue
              }
            })
          })
        } else {
          console.error('Invalid data:', payload)
        }
      }
      mqttClient.connect({ onSuccess, onFailure })
    },
    sendMqttMessage(topic, sendStr) {
      this.mqttClient.send(topic, sendStr)
    },
    updateWestCarLocation() {
      if (this.westX === '' || this.westY === '') {
        return
      }
      var car1LocationX = (parseFloat(this.westX) - 2800) * this.widthPx // 天车实际活动范围X长度:2800 - 81000
      var car1LocationY = (parseFloat(this.westY) - 2000) * this.widthPx // 小车实际活动范围Y长度:2000 - 17500
      var rate1 = +(
        (this.carAuctalWorkRegionWidth * this.widthPx) /
        this.carVrituWorkRegionWidth
      ).toFixed(2)
      var rate2 = +(
        (this.carAuctalWorkRegionHeight * this.widthPx) /
        this.carVrituWorkRegionHeight
      ).toFixed(2)
      var x = +(car1LocationX / rate1).toFixed(2)
      var y = +(car1LocationY / rate2).toFixed(2)
      const bigCarInfo = this.nodes.filter((item) => item.type === 'westBigCar')[0]
      const smallCarInfo = this.nodes.filter((item) => item.type === 'westSmallCar')[0]
      if (x <= 0) {
        bigCarInfo.x = -15
        smallCarInfo.x = -15
      } else if (x > 1770) {
        bigCarInfo.x = 1770
        smallCarInfo.x = 1780
      } else {
        bigCarInfo.x = x
        smallCarInfo.x = x
      }
      if (y > 456) {
        smallCarInfo.y = 456
      } else if (y <= 0) {
        smallCarInfo.y = 0
      } else {
        smallCarInfo.y = y
      }
    },
    updateEastCarLocation() {
      if (this.eastX === '' || this.eastY === '') {
        return
      }
      var car1LocationX = (parseFloat(this.eastX) - 2800) * this.widthPx // 天车实际活动范围X长度:2800 - 81000
      var car1LocationY = (parseFloat(this.eastY) - 2000) * this.widthPx // 小车实际活动范围Y长度:2000 - 17500
      var rate1 = +(
        (this.carAuctalWorkRegionWidth * this.widthPx) /
        this.carVrituWorkRegionWidth
      ).toFixed(2)
      var rate2 = +(
        (this.carAuctalWorkRegionHeight * this.widthPx) /
        this.carVrituWorkRegionHeight
      ).toFixed(2)
      var x = +(car1LocationX / rate1).toFixed(2)
      var y = +(car1LocationY / rate2).toFixed(2)
      const bigCarInfo = this.nodes.filter((item) => item.type === 'eastBigCar')[0]
      const smallCarInfo = this.nodes.filter((item) => item.type === 'eastSmallCar')[0]
      if (x <= 0) {
        bigCarInfo.x = 1770
        smallCarInfo.x = 1780
      } else if (x > 1770) {
        bigCarInfo.x = 1770
        smallCarInfo.x = 1780
      } else {
        bigCarInfo.x = x
        smallCarInfo.x = x
      }
      if (y > 456) {
        smallCarInfo.y = 456
      } else if (y <= 0) {
        smallCarInfo.y = 0
      } else {
        smallCarInfo.y = y
      }
    },
    // stockDetail() {
    //   this.$refs.stackIndex.open()
    // },
    smallCarDetail(i) {
      this.$refs.smallCarDetail.open()
      this.$refs.smallCarDetail.modalTitle = i + '#平板小车详情页面-小车编号' + i
    },
    crownBlockDetail() {
      this.$refs.crownBlockDetail.open()
    },
    crownBlockDetailEast() {
      this.$refs.crownBlockDetailEast.open()
    },
    outbound(SlRgvPlc) {
      // 提取公共参数，减少重复代码
      const baseQuery = {
        station_code: 'P01',
        user_name: Cookies.get('userName')
      }
      // 指定rgv工位
      LargeScreenPage.DcsPointModificationSelect({
        ...baseQuery,
        Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum2',
        Value: '2'
      })
      // 给rgv启动信号
      LargeScreenPage.DcsPointModificationSelect({
        ...baseQuery,
        Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum1',
        Value: '1'
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
        // 延迟1秒后执行第三次接口调用（复位）
        setTimeout(() => {
          LargeScreenPage.DcsPointModificationSelect({
            ...baseQuery,
            Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum1',
            Value: '0'
          })
        }, 1000) // 500毫秒 = 0.5秒
      }).catch(() => {
        this.$message({
          message: '执行失败',
          type: 'error'
        })
      })
    },
    warehousing(SlRgvPlc) {
      // 提取公共参数，减少重复代码
      const baseQuery = {
        station_code: 'P01',
        user_name: Cookies.get('userName')
      }
      // 指定rgv工位
      LargeScreenPage.DcsPointModificationSelect({
        ...baseQuery,
        Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum2',
        Value: '1'
      })
      // 给rgv启动信号
      LargeScreenPage.DcsPointModificationSelect({
        ...baseQuery,
        Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum1',
        Value: '1'
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
        // 延迟1秒后执行第三次接口调用（复位）
        setTimeout(() => {
          LargeScreenPage.DcsPointModificationSelect({
            ...baseQuery,
            Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum1',
            Value: '0'
          })
        }, 1000) // 500毫秒 = 0.5秒
      }).catch(() => {
        this.$message({
          message: '执行失败',
          type: 'error'
        })
      })
    },
    FaultReset(SlRgvPlc) {
      LargeScreenPage.DcsUnlockDock({wharf_order:SlRgvPlc})
      // 自动就绪复位
      LargeScreenPage.DcsPointModificationSelect({
        station_code: 'P01',
        user_name: Cookies.get('userName'),
        Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum12',
        Value: '0'
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
      }).catch(() => {
        this.$message({
          message: '执行失败',
          type: 'error'
        })
      })
    },
    stop(SlRgvPlc) {
      // 复位
      LargeScreenPage.DcsPointModificationSelect({
        station_code: 'P01',
        user_name: Cookies.get('userName'),
        Tag: SlRgvPlc + '/DcsStatus/RcsWriteTaskNum12',
        Value: '1'
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message.success('执行成功')
        } else {
          this.$message({
            message: defaultQuery.msg || '执行失败',
            type: 'error'
          })
        }
      }).catch(() => {
        this.$message({
          message: '执行失败',
          type: 'error'
        })
      })
    },
    updateInventoryDate() {
      LargeScreenPage.DcsInventoryStatistics({
        inventory_date: this.inventory_date,
        TaskType: this.TaskType
      }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        this.inventoryData = defaultQuery.data
        this.inventoryData = this.inventoryData.filter(data => data.grade !== '')
        this.inventoryData = this.inventoryData.filter(data => data.grade !== 'null')
      })
    },
    OutboundTask2(isActive2) {
      if (isActive2) {
        // 开启定时出库任务：立即执行一次，之后每10秒执行
        this.generationTask('BMT02', 'A')
        this.Outbound2Timer = setInterval(() => this.generationTask('BMT02', 'A'), 10000)
      } else {
        // 关闭定时出库任务
        clearInterval(this.Outbound2Timer)
        this.Outbound2Timer = null
      }
    },
    OutboundTask4(isActive4) {
      if (isActive4) {
        // 开启定时出库任务：立即执行一次，之后每10秒执行
        this.generationTask('BMT04', 'B')
        this.Outbound4Timer = setInterval(() => this.generationTask('BMT04', 'B'), 10000)
      } else {
        // 关闭定时出库任务
        clearInterval(this.Outbound4Timer)
        this.Outbound4Timer = null
      }
    },
    generationTask(wharf_code, ware_house) {
      LargeScreenPage.DcsAutomaticOutboundJudgment({ wharf_code: wharf_code }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.result == 'Y') {
          if(defaultQuery.msg != null && defaultQuery.msg != ''){
            ware_house = defaultQuery.msg
          }
          crudTask.add({
            task_from: 'WMS',
            task_way: 'AUTO',
            task_type: 'WASTE_BOX_OUT_TASK',
            ware_house: ware_house,
            wharf_code: wharf_code,
            waste_box_code: '',
            lot_num: '',
            material_code: '',
            grade: '',
            task_status: 'WORK',
            lock_flag: 'N'
          })
        }
      })
    }
  }
}
</script>
<style scoped  lang="scss">
@import '~@/assets/styles/fjrm/index.scss';
</style>
<style>
.fjTable .el-card{
  background-color: transparent!important;
  border: none!important;
}
.fjTable .el-table{
  background-color: transparent!important;
  border: none!important;
}
.fjTable .el-table th{
  background-color: #011c39 !important;
  color: aqua!important;
}
body .fjTable .el-table th.gutter{
    display: table-cell!important;
  }
.fjTable .el-table__body-wrapper::-webkit-scrollbar {
  width: 3px !important;
  height: 3px !important;
  background-color: rgb(18, 36, 80) !important;
  cursor: pointer !important;
}
.fjTable .el-scrollbar__wrap::-webkit-scrollbar {
  width: 3px!important;
  height: 3px!important;
}
.fjTable .el-table__body-wrapper::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: aqua !important;
  cursor: pointer !important;
}
.fjTable .el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell{
  background-color: transparent !important;
}
.fjTable .el-table__body tr.current-row>td{
  background-color: transparent !important;
}
.fjTable .el-table tr{
  background-color: #002E52 !important;
}
.fjTable .el-table tr:nth-child(even){
  background-color: #08192D !important;
}
.fjTable .el-table tr td{
  color: #fff!important;
}
.fjTable .el-table__body-wrapper{
  background-color: rgba(12, 35, 103,0.9) !important;
}

.fjTable .el-pagination .el-pagination__total{
color: aqua !important;
}

.fjTable .el-pagination button:disabled,.fjTable .el-pagination .btn-next,.fjTable .el-pagination .btn-prev{
  color: aqua !important;
  background-color: #193078 !important;
  border: none;
}
.fjTable .el-pager{
  background-color: #193078 !important;
}
.fjTable .el-pager li.active{
  color: aqua !important;
}
.fjTable .el-pager li{
  color: #fff!important;
  border: 1px none;
  border-right: nonde;
  background-color: transparent !important;
}

.fjTable .el-pagination__sizes .el-input__inner{
  background-color: #193078 !important;
  color: aqua!important;
  border: 1px solid aqua !important;
}
.fjTable .el-select .el-input .el-select__caret{
  color: aqua!important;
}
.custom-select .el-input__inner {
    background-color: #1E3A8A; /* 深蓝色背景 */
    color: #BFDBFE; /* 浅蓝色文字 */
    border: 1px solid #3B82F6; /* 边框颜色 */
}

.custom-select .el-input__inner::placeholder {
    color: #93C5FD; /* 占位符颜色 */
}

.custom-select .el-input__icon {
    color: #BFDBFE; /* 下拉图标颜色 */
}
</style>

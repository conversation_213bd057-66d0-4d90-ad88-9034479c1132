// 核心模块的中文翻译

export default {
  // 这里可以添加核心模块特定的翻译
  core: {
    // SECS/AIS配方管理
    secsais: {
      maintenance: 'SECS/AIS配方管理',
      downRecipeSuccess: '配方下发成功！',
      downRecipeAndSyncSuccess: '配方下发成功，同步到其他设备成功！',
      recipeSyncFailed: '配方同步失败',
      modifyParameter: '是否修改参数',
      parameterCode: '参数编码',
      parameterDes: '参数描述',
      parameterVal: '参数值',
      parameterInput: '输入值',
      parameterUnit: '单位',
      parameterLimit: '上下限',
      noRecipeSelected: '未选择配方或配方详情加载中...',
      fetchRecipeDataFailed: '获取配方数据失败',
      valueTooLow: '输入值低于下限',
      valueTooHigh: '输入值高于上限',
      mqttNotConnected: '请先连接MQTT服务',
      modifyFailed: '修改失败',
      modifySuccess: '修改成功',
      serviceCell: '无法获取单元服务器信息',
      mqttConnectSuccess: 'MQTT连接成功',
      mqttConnectionFailed: 'MQTT连接失败',
      getCellIPFailed: '获取单元IP失败',
      clientDes: '客户端描述',
      clientCode: '客户端编码',
      search: '搜索',
      reset: '重置',
      clientList: '实例列表',
      refreshInstanceList: '刷新实例列表',
      online: '在线',
      offline: '离线',
      modelList: '配方列表',
      recipeDetails: '配方详情',
      stationCodeMissing: '工站编码缺失，无法查询配方模型数据',
      fetchModelDataFailed: '获取配方模型数据失败',
      refreshingInstanceList: '正在刷新实例列表...',
      confirmDelete: '确认删除选中的{0}条数据?',
      prompt: '提示',
      confirm: '确认',
      cancel: '取消'
    },

    // EAP配方管理
    recipe: {
      recipeDes: '配方描述',
      recipeName: '配方名称',
      versionNo: '版本号',
      recipeType: '配方类型',
      enableFlag: '有效标识',
      deviceCode: '设备编码',
      deviceDes: '设备描述',
      materialCode: '物料编码',
      materialDes: '物料描述',
      isValid: '是否有效',
      operation: '操作',
      addParams: '新增参数',
      editParameters: '编辑参数',
      recipeTypeRequired: '请选择配方类型',
      versionNoRequired: '请填写版本号',
      recipeDesRequired: '请填写配方描述',
      recipeNameRequired: '请填写配方名称',
      materialCodeRequired: '请填写物料编码',
      materialDesRequired: '请填写物料描述',
      deviceCodeRequired: '请填写设备编码',
      deviceDesRequired: '请填写设备描述',
      enableFlagRequired: '请选择有效标识',
      copyRecipe: '复制配方',
      exportRecipe: '导出配方',
      downloadTemplate: '下载模板',
      importRecipe: '导入配方',
      batchDelete: '批量删除',
      copyRecipeName: '复制配方名称',
      selectRecipeToExport: '请选择要导出的配方',
      selectOneRecipeToExport: '请选择一条配方记录进行导出',
      selectOneRecipeToCopy: '请选择一条配方记录进行复制',
      exportSuccess: '配方导出成功',
      exportFailed: '导出配方失败，请重试',
      templateDownloadSuccess: '模板下载成功',
      templateDownloadFailed: '下载模板失败，请重试',
      importSuccess: '配方导入成功',
      importFailed: '导入配方失败，请检查文件格式和内容',
      onlyXlsSupported: '仅支持 .xls 文件格式',
      confirmBatchDelete: '确认删除选中的',
      dataItems: '条数据?',
      unit: '单位',
      minValue: '最小值',
      maxValue: '最大值',
      currentValue: '当前值',
      noLimit: '不限制'
    },

    // SCADA数据变化报表
    scadaReport: {
      scadaTagList: 'SCADA 标签列表',
      searchTag: '搜索标签...',
      instance: '实例',
      tagGroup: '标签组',
      tag: '标签',
      currentSelectedTag: '当前选择标签：',
      tagMissingUnitInfo: '该标签缺少单元信息，请联系管理员',
      pleaseSelectTagFirst: '请先从左侧列表选择一个标签'
    }
  }
}

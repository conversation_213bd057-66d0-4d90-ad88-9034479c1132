// THJX project English translation

export default {
  // THJX project specific translations
  thjx: {
    // Page title
    pageTitle: 'Shengfeng--Thailand Golden Elephant-Local Recipe Management',
    
    // Mode selection
    offlineMode: 'Offline Mode',
    onlineLocal: 'Online/Local',
    onlineRemote: 'Online/Remote',
    
    // Device status
    deviceStatus: 'Device Status:',
    unknown: 'Unknown',
    alarmLight: 'Alarm Light',
    plcHeartbeat: 'PLC Heartbeat',
    
    // Form fields
    lotNo: 'Lot No',
    materialCode: 'Material Code',
    recipe: 'Recipe',
    selectRecipe: 'Please select recipe',
    pleaseSelectRecipeFirst: 'Please select recipe first',
    refreshRecipeList: 'Refresh recipe list',
    processQuantity: 'Process Quantity',
    downloadPreview: 'Download Preview',
    employeeId: 'Employee ID',
    employeeNo: 'Employee No',
    pleaseInputEmployeeNo: 'Please input employee no',
    panelId: 'Panel ID',
    panelNo: 'Panel No',
    pleaseInputPanelNo: 'Please input panel no',
    pleaseEnterPanelId: 'Please enter panel ID',
    inkModel: 'Ink Model',
    pleaseInputInkModel: 'Please input ink model',
    pleaseEnterInkModel: 'Please enter ink model',
    pleaseSelectInkModel: 'Please select ink model',
    inkBatch: 'Ink Batch',
    pleaseInputInkBatch: 'Please input ink batch',
    pleaseEnterInkBatch: 'Please enter ink batch',
    inkThawTime: 'Ink Thaw Time',
    inkThawStartTime: 'Ink Thaw Start Time',
    selectThawStartTime: 'Select thaw start time',
    inkThawEndTime: 'Ink Thaw End Time',
    selectThawEndTime: 'Select thaw end time',
    inkViscosity: 'Ink Viscosity',
    pleaseInputInkViscosity: 'Please input ink viscosity',
    pleaseEnterInkViscosity: 'Please enter ink viscosity',
    deliveryPreview: 'Delivery Preview',
    station: 'Station',
    isSimulated: 'Is Simulated',
    
    // Information area titles
    productionInfo: 'Production Information',
    alarmInfo: 'Alarm Information',
    
    // Table column headers
    workstation: 'Workstation',
    instanceNo: 'Instance No',
    instanceDesc: 'Instance Description',
    alarmCode: 'Alarm Code',
    alarmLevel: 'Alarm Level',
    alarmDesc: 'Alarm Description',
    alarmTime: 'Alarm Time',
    resetTime: 'Reset Time',
    isReset: 'Is Reset',
    resetted: 'Resetted',
    pendingReset: 'Pending Reset',
    isSimulation: 'Is Simulation',
    yes: 'Yes',
    no: 'No',
    
    // Recipe related
    projectName: 'Project Name',
    currentValue: 'Current Value',
    unit: 'Unit',
    upperLimit: 'Upper Limit',
    lowerLimit: 'Lower Limit',
    status: 'Status',
    modifyRecipe: 'Modify Recipe',
    isModifyParams: 'Modify Parameters',
    cancel: 'Cancel',
    confirmDownload: 'Confirm Download',
    
    // CIM messages
    cimMessage: 'CIM Message',
    code: 'Code',
    message: 'Message',
    confirm: 'Confirm',
    
    // Recipe download confirmation
    recipeDownloadConfirm: 'Recipe Download Confirmation',
    confirmDownloadAction: 'Confirm Download',
    recipeInfo: 'Recipe Information',
    recipeName: 'Recipe Name',
    stationCode: 'Station Code',
    batchNo: 'Batch No',
    partNo: 'Part No',
    quantity: 'Quantity',
    employeeInfo: 'Employee Information',
    recipeDetails: 'Recipe Details',
    parameterCode: 'Parameter Code',
    parameterDesc: 'Parameter Description',
    parameterValue: 'Parameter Value',
    validFlag: 'Valid Flag',
    noRecipeSelected: 'No recipe selected or recipe details loading...',
    
    // Status related
    offDuty: 'Off Duty',
    onDuty: 'On Duty',
    pleaseInputMaterialCode: 'Please input material code',
    pleaseInputMaterialDesc: 'Please input material description',
    initializing: 'Initializing',
    standby: 'Standby',
    alarm: 'Alarm',
    stop: 'Stop',
    running: 'Running',
    pm: 'PM',
    greenLight: 'Green Light',
    yellowLight: 'Yellow Light',
    redLight: 'Red Light',
    deviceControlStatus: 'Device Control Status',
    deviceStatusLabel: 'Device Status',
    recipeModification: 'Recipe Modification',
    recipeSelectionComplete: 'Recipe Selection Complete',
    recipeMaintenance: 'Recipe Maintenance',
    
    // Prompt messages
    pleaseStartMonitor: 'Please start monitoring',
    connectionSuccess: 'Connection successful',
    connectionFailed: 'Connection failed',
    connectionLost: 'Connection lost, reconnecting...',
    connectionLostReconnecting: 'Connection lost, reconnecting...',
    writeSuccess: 'Write successful',
    operationFailed: 'Operation failed!',
    queryException: 'Query exception',
    downloadRecipeSuccess: 'Recipe download successful',
    deviceMustBeStandby: 'Device must be in standby mode to deliver recipe',
    deviceMustBeInStandbyToDeliverRecipe: 'Device must be in standby mode to deliver recipe',
    deviceRunningCannotDownload: 'Device is running, recipe download not allowed',
    pleaseSelectRecipe: 'Please select recipe first',
    pleaseInputLotNo: 'Please input lot number',
    pleaseInputMaterialCodeMsg: 'Please input material code',
    quantityMustBePositiveInteger: 'Quantity must be a positive integer greater than or equal to 0',
    getRecipeListFailed: 'Failed to get recipe list',
    getRecipeDetailsFailed: 'Failed to get recipe details',
    refreshRecipeListSuccess: 'Recipe list refreshed successfully',
    noRecipeDataFound: 'No recipe data found',
    refreshRecipeListFailed: 'Failed to refresh recipe list',
    recipeConfigIncomplete: 'Recipe parameter configuration incomplete, cannot download recipe, please contact administrator to complete scada_tag table block_addr configuration',
    loadingRecipeList: 'Refreshing recipe list...',
    noMaterialData: 'No material data found, please maintain materials first',
    
    // Chart related
    oee: 'OEE',
    readRate: 'Read Rate',
    capacity: 'Capacity'
  }
}
<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm" style="align-items:center">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="时间：">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="至"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width: 100%"
                    :default-time="['00:00:00', '23:59:59']"
                    @input="$forceUpdate()"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="返修策略名称:">
                <el-input v-model="query.repair_work_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="返修策略:">
                <el-input v-model="query.repair_work_station_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料码:">
                <el-input v-model="query.exact_barcode" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission">
        <template slot="right">
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-close" plain round :disabled="crud.selections.length <=0" @click="unbind">
            解绑
          </el-button>
        </template>
      </crudOperation>
      <el-table 
      ref="table" 
      v-loading="crud.loading" 
      border 
      size="small" 
      :data="crud.data" 
      style="width: 100%" 
      :cell-style="crud.cellStyle" 
      :height="height" 
      :highlight-current-row="true" 
      @header-dragend="crud.tableHeaderDragend()"
      @selection-change="crud.selectionChangeHandler"
      >
        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
        <el-table-column type="selection" width="55" fixed="left" />
        <!-- 返修策略名称 -->
        <el-table-column :show-overflow-tooltip="true" prop="repair_work_des" :label="$t('返修策略名称')" width="200" align="center" />
        <!-- 返修策略 -->
        <el-table-column :show-overflow-tooltip="true" prop="repair_work_station_code" :label="$t('返修策略')" width="800" align="center" />
        <!-- 返修策略绑定时间 -->
        <el-table-column :show-overflow-tooltip="true" prop="creation_date" :label="$t('返修策略绑定时间')" width="200" align="center" />
        <!-- 物料码 -->
        <el-table-column :show-overflow-tooltip="true" prop="exact_barcode" :label="$t('物料码')" width="300" align="center" />
      </el-table>
      <!--分页组件-->
      <pagination style="margin-bottom: 10px;" />
    </el-card>
  </div>
</template>
<script>
import crudStationSel from '@/api/mes/project/sh/shMaterialWork2'
// import crudStation from '@/api/core/factory/sysStation'
import rrOperation from '@crud/RR.operation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
}
export default {
  name: 'repairStationIndex',
  components: { crudOperation, pagination, rrOperation },
  cruds() {
    return CRUD({
      title: '工位管理',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'repair_work_id',
      // 排序
      sort: ['repair_work_id asc'],
      // CRUD Method
      crudMethod: { ...crudStationSel },
      // 打开页面不查询
      queryOnPresenterCreated: false,
      query: {
      },
      // 按钮显示
      optShow: {
        add: false,
        edit: false,
        del: false,
        reset: true,
        buttonGroup: false
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 220,
      permission: {
        add: ['admin', 'sys_fmod_station:add'],
        edit: ['admin', 'sys_fmod_station:edit'],
        del: ['admin', 'sys_fmod_station:del'],
        reset: ['admin', 'sys_fmod_station:reset']
      },
      // 单元服务器信息：IP、WEB端口、MQTT端口
      cellId: '',
      cellIp: '',
      cellMqttPort: '',
      cellWebPort: '',
      scanValue: ''
    }
  },
    computed: {
    // 默认时间
    timeDefault() {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      // 月，日 不够10补0
      const defalutStartTime =
        start.getFullYear() +
        '-' +
        (start.getMonth() + 1 >= 10
          ? start.getMonth() + 1
          : '0' + (start.getMonth() + 1)) +
        '-' +
        (start.getDate() >= 10 ? start.getDate() : '0' + start.getDate()) +
        ' 00:00:00'
      const defalutEndTime =
        end.getFullYear() +
        '-' +
        (end.getMonth() + 1 >= 10 ? end.getMonth() + 1 : '0' + (end.getMonth() + 1)) +
        '-' +
        (end.getDate() >= 10 ? end.getDate() : '0' + end.getDate()) +
        ' 23:59:59'
      return [defalutStartTime, defalutEndTime]
    }
  },
  mounted() {
  },
  mounted: function() {
  const that = this
  window.onresize = function temp() {
    that.height = document.documentElement.clientHeight - 270
  }
  },
  created: function() {
    this.crud.query.item_date = this.timeDefault

    // selProdLine({
    //   user_name: Cookies.get('userName'),
    //   enable_flag: 'Y'
    // })
    //   .then((res) => {
    //     const defaultQuery = JSON.parse(JSON.stringify(res))
    //     if (defaultQuery.code === 0) {
    //       if (defaultQuery.data.length > 0) {
    //         this.prodLineData = defaultQuery.data
    //       }
    //     }
    //   })
    //   .catch(() => {
    //     this.$message({
    //       message: '初始化模式数据异常',
    //       type: 'error'
    //     })
    //   })
  },
  methods: {
    unbind() {
      this.$confirm(`确认解绑选中的${this.crud.selections.length}条数据?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          var idList = []
          this.crud.selections.forEach((item) => {
            idList.push(item.repair_work_id)
          })
          crudStationSel
            .MesShMaterialWork6({
              idList: idList
            })
            .then(res => {
              if (res.code === 0) {
                this.$message({ message: '操作成功', type: 'success' })
                this.crud.toQuery()
              } else {
                this.$message({ message: '操作失败：' + res.msg, type: 'error' })
              }
            })
            .catch(ex => {
            })
        })
        .catch(() => {})
    }
  }
}
</script>
<style lang="less" scoped>
.orderInfo {
    font-size: 11px;
    display: flex;
    align-items: center;
   ::v-deep .el-input__inner {
      width: 960px;
      height: 30px;
      margin: 0 5px;
    }
  }
 ::v-deep .el-pagination{
    margin-bottom: 10px !important;
  }
</style>

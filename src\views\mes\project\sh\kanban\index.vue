<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="产线ID">
                <el-input v-model="query.prod_line_id" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
        <el-form ref="form" class="el-form-wrap el-form-column" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item label="上线数" prop="up_count">
            <el-input v-model="form.up_count" />
          </el-form-item>
          <el-form-item label="下线数" prop="down_count">
            <el-input v-model="form.down_count" />
          </el-form-item>
          <el-form-item label="基准数" prop="target_count">
            <el-input v-model="form.target_count" />
          </el-form-item>
          <el-form-item label="节拍(秒)" prop="beats">
            <el-input v-model="form.beats" />
          </el-form-item>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>  <!-- 取消 -->
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>  <!-- 确认 -->
        </div>
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table border @header-dragend="crud.tableHeaderDragend()" ref="table" v-loading="crud.loading" size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @selection-change="crud.selectionChangeHandler">
            <el-table-column  type="selection" width="45" align="center" />
            <el-table-column  :show-overflow-tooltip="true" prop="prod_line_id" label="产线ID" />
            <el-table-column  :show-overflow-tooltip="true" prop="up_count" label="上线数" />
            <el-table-column  :show-overflow-tooltip="true" prop="down_count" label="下线数" />
            <el-table-column  :show-overflow-tooltip="true" prop="target_count" label="基准数" />
            <el-table-column  :show-overflow-tooltip="true" prop="beats" label="节拍(秒)" />
            <!-- Table单条操作-->
            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">  <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudKanban from '@/api/mes/project/sh/kanban'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  prod_line_id: ''
}
export default {
  name: 'ME_KANBAN_BASE',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '看板参数维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'prod_line_id',
      // 排序
      sort: ['prod_line_id asc'],
      // CRUD Method
      crudMethod: { ...crudKanban },
      // 按钮显示
      optShow: {
        add: false,
        edit: true,
        del: true,
        down: false,
        reset: true
      },
      // 设置默认分页大小为20条每页
      props: {
        pageSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      permission: {
      },
      rules: {
      },
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
  },
  methods: {
  }
}
</script>

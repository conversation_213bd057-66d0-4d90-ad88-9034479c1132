import request from '@/utils/request'
import axios from 'axios'

// 珠海超毅大元-志圣 工位当前登录信息查询
export function selLoginInfo(data) {
  console.log('调用selLoginInfo API:', data) // 调试日志
  return request({
    url: 'aisEsbApi/eap/project/zhcy/EapMeStationLoginInfoSelect',
    method: 'post',
    data
  })
}

// 珠海超毅大元-志圣 员工扫卡登录
export function userLogin(data) {
  console.log('调用userLogin API:', data) // 调试日志
  return request({
    url: 'aisEsbApi/eap/project/zhcy/EapMeStationUserLogin',
    method: 'post',
    data
  })
}

// 珠海超毅大元-志圣 员工登出
export function userLogout(data) {
  console.log('调用userLogout API:', data) // 调试日志
  return request({
    url: 'aisEsbApi/eap/project/zhcy/EapMeStationUserLogout',
    method: 'post',
    data
  })
}

// 获取任务列表
export function fetchTaskList(params) {
  console.log('调用fetchTaskList API:', params) // 调试日志
  return axios.post('aisEsbApi/eap/project/zhcy/aps-plan/queryApsPlans', params)
    .then(response => {
      console.log('任务列表响应:', response) // 调试日志
      return response
    })
    .catch(error => {
      console.error('获取任务列表异常:', error) // 调试日志
      throw error
    })
}

// 查询面板详情
export function fetchPanelDetails(jobId) {
  return axios.post('aisEsbApi/eap/project/zhcy/aps-plan/queryApsPlanPanelDetails', { jobId })
}

// 取消计划
export function cancelTask(params) {
  return axios.post('aisEsbApi/eap/project/zhcy/aps-plan/cancelApsPlan', params)
}

// 删除计划
export function deleteTask(params) {
  return axios.post('aisEsbApi/eap/project/zhcy/aps-plan/deleteApsPlan', params)
}

// 手动请求权限账户信息同步
export function syncIcCodeReport(stationId) {
  return axios.post('aisEsbApi/eap/project/zhcy/interf/send/EQP_IcCodeReport', { stationId: stationId })
}

// 下载配方
export function downloadRecipe(data) {
  console.log('调用downloadRecipe API:', data) // 调试日志
  
  // 准备请求数据
  const requestData = {
    qrCode: data.qrCode,
    stationId: data.stationId
  }

  return axios.post('aisEsbApi/eap/project/zhcydayuan/interf/send/EQP_QRCodeReadReport', requestData)
    .then(response => {
      console.log('下载配方响应:', response) // 调试日志
      return response
    })
    .catch(error => {
      console.error('下载配方异常:', error) // 调试日志
      throw error
    })
}

// 手动启动任务
export function manuallySendPlan(data) {
  console.log('调用manuallySendPlan API:', data) // 调试日志
  return axios.post('aisEsbApi/eap/project/zhcydayuan/interf/send/ManuallySendPlan', data)
    .then(response => {
      console.log('手动启动任务响应:', response) // 调试日志
      return response
    })
    .catch(error => {
      console.error('手动启动任务异常:', error) // 调试日志
      throw error
    })
}

// 手动结束任务
export function manuallyEndWorkPlan(data) {
  console.log('调用manuallyEndWorkPlan API:', data) // 调试日志
  return axios.post('aisEsbApi/eap/project/zhcydayuan/interf/send/ManuallyEndWorkPlan', data)
    .then(response => {
      console.log('手动结束任务响应:', response) // 调试日志
      return response
    })
    .catch(error => {
      console.error('手动结束任务异常:', error) // 调试日志
      throw error
    })
}

export default {
  selLoginInfo,
  userLogin,
  userLogout,
  fetchTaskList,
  fetchPanelDetails,
  cancelTask,
  deleteTask,
  syncIcCodeReport,
  downloadRecipe,
  manuallySendPlan,
  manuallyEndWorkPlan
}
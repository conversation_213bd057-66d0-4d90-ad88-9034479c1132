import request from '@/utils/request'

// 员工扫卡登录
export function selLoginInfo(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeStationLoginInfoSelect',
    method: 'post',
    data
  })
}
// 员工扫卡登录
export function userLogin(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeStationUserLogin',
    method: 'post',
    data
  })
}
// 员工登出
export function userLogout(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeStationUserLogout',
    method: 'post',
    data
  })
}

// 员工扫卡登录
export function userLoginZhsl(data) {
  return request({
    url: 'aisEsbWeb/eap/project/zhsl/interf/send/EapEQPOperatorLoginRequest',
    method: 'post',
    data
  })
}
export default { selLoginInfo, userLogin, userLogout,userLoginZhsl }


<template>
  <div id="#bigScreen" class="mesContainer">
    <div class="header">
      <span>减  速  器  装  配  一 线</span>
    </div>
    <div class="header1">
      <span>Reducer assembly line 01</span>
    </div>
    <div style="margin-top: 10px;display: flex;">
      <div class="time">
        <div>{{ date }}</div>
        <div>{{ week }}</div>
        <div class="timedata">{{ time }}</div>
      </div>
      <div class="productPlan">
        <div v-for="(item,index) in productionData" :key="index" class="box">
          <div class="name "><span class="textName">{{ item.name }}</span><span>:</span></div>
          <div class="name"><span>{{ item.value }}</span></div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { sel } from '@/api/mes/project/sh/shProductionScreen'
import autofit from 'autofit.js'
export default {
  name: 'productionBoard',
  data() {
    return {
      productionData: [
        { name: '生产机型', value: '', key: 'small_model_type' },
        { name: '计划数', value: 0, key: 'mo_plan_count' },
        { name: '基准数', value: 0, key: 'start_date' },
        { name: '上线数', value: 0, key: 'mo_online_count' },
        { name: '下线数', value: 0, key: 'mo_offline_count' }
      ],
      date: '',
      time: '',
      week: '',
      timer: null,
      productTimer: null
    }
  },
  mounted() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#bigScreen',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    this.getProductionData()
    //this.getTime() // 获取时间
    //this.timer = setInterval(() => {
    //  this.getTime()
    //}, 1000)
    this.productTimer = setInterval(() => {
      this.getProductionData()
    }, 1000)
  },
  beforeDestroy() {
    //if (this.timer) {
    //  clearInterval(this.timer)
    //}
    this.productTimer && clearInterval(this.productTimer)
  },
  methods: {
    getProductionData() {
      const query = {}
      sel(query).then(res => {
        if (res.code === 0 && res.data !== '') {
          const data = res.data[0]
          for (const val in data) {
            if(val.toString()==='now_date' || val.toString()==='now_time' || val.toString()==='now_week'){
              if(val.toString()==='now_date'){
                this.date =data[val]
              }
              if(val.toString()==='now_time'){
                this.time =data[val]
              }
              if(val.toString()==='now_week'){
                this.week =data[val]
              }
            }
            else{
              const obj = this.productionData.find(e => e.key === val)
              if (Object.keys(obj).length > 0) {
                obj.value = data[val]
              }
            }
          }
        }
      })
    },
    getTime() {
      // 当前年月日时分秒
      const yy = new Date().getFullYear()
      const mm = (new Date().getMonth() + 1) < 10 ? '0' + (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf = new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss = new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.date = yy + '-' + mm + '-' + dd
      this.time = hh + ' ' + ':' + ' ' + mf + ' ' + ':' + ' ' + ss
      // 当前星期
      const wk = new Date().getDay()
      const weeks = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
      this.week = weeks[wk]
    }
  }
}
</script>
<style lang="less" scoped>
.mesContainer{
    background: #2B304D;
    width: 100%;
    height: 100%;
    overflow: hidden;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    transform-origin: 0 0;
    padding: 50px 100px;
    .header{
        width: 100%;
        height: 120px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span{
            margin: auto;
            font-size: 100px;
            color: #fff;
            font-weight: 600;
        }
    }
    .header1{
        width: 100%;
        height: 120px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        span{
            margin: auto;
            font-size: 80px;
            color: #fff;
            font-weight: 600;
        }
    }
    .time{
        width: 50%;
        display: flex;
        flex-direction: column;
        padding: 150px 100px;
        align-items: center;
        div{
            font-size: 90px;
            color: #fff;
            font-weight: 600;
        }
        .timedata{
            font-size: 120px;
        }
    }
    .productPlan{
        width: 50%;
        .box{
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            .name{
                margin: auto;
                border: 3px solid #faf7f7;
                border-radius: 8px;
                height: 130px;
                width: 750px;
                font-size: 70px;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                .textName{
                  display: block;
                  width: 70%;
                  text-align-last: justify;
                  text-align: justify;
                }
            }
            .value{
                width: 30%;
                height: 100%;
                font-size: 65px;
                color: #fff;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
            }
        }
    }
}
</style>

<template>
  <div class="app-container">
    <el-card shadow="never">
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="loading" border size="small" :data="tableData" style="width: 100%;" cell-style="border:0px;border-bottom:1px solid #dfe6ec" height="500px" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
            <el-table-column :show-overflow-tooltip="true" prop="item_date" width="140" :label="$t('view.table.time')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_index" width="100" :label="$t('view.table.boardSorting')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_num" width="150" :label="$t('view.table.subTaskNo')" />
            <el-table-column :show-overflow-tooltip="true" prop="port_code" width="100" :label="$t('view.table.portNo')" />
            <el-table-column :show-overflow-tooltip="true" prop="tray_barcode" width="120" :label="$t('view.table.carrierBarcode')" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_num" width="150" :label="$t('view.table.rooftopBarcode')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_barcode" width="150" :label="$t('view.table.boardBarcode')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_status" :label="$t('view.table.boardStatus')" width="120" >
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.panel_status == 'NG' ? 'danger' : (scope.row.panel_status == 'OK' ? 'success' : 'warning'))"
                  class="elTag"
                >
                  {{ scope.row.panel_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_code" width="120" :label="$t('view.table.ngBoardErrorCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_msg" width="150" :label="$t('view.table.ngBoardErrorDescription')" />
            <el-table-column :show-overflow-tooltip="true" prop="inspect_flag" width="150" :label="$t('view.table.isFirstCheckBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="dummy_flag" width="120" :label="$t('view.table.isDummyBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="group_lot_num" width="150" :label="$t('view.table.motherBatch')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_short_num" width="120" :label="$t('view.table.subTaskSimplifiedCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_index" width="100" :label="$t('view.table.subTaskSorting')" />
            <el-table-column :show-overflow-tooltip="true" prop="material_code" width="150" :label="$t('view.table.materialNo')" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_type" width="120" :label="$t('view.table.carrierType')" />
            <el-table-column :show-overflow-tooltip="true" prop="lot_level" width="100" :label="$t('view.table.layer')" />
            <el-table-column :show-overflow-tooltip="true" prop="fp_index" width="100" :label="$t('view.table.subDiskSorting')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_length" width="150" :label="$t('view.table.boardLength')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_width" width="150" :label="$t('view.table.boardWidth')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_tickness" width="180" :label="$t('view.table.boardThickness')" />
            <el-table-column :show-overflow-tooltip="true" prop="manual_judge_code" width="150" :label="$t('view.table.manualProcessingBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_flag" width="120" :label="$t('view.table.isPanelMode')" />
            <el-table-column :show-overflow-tooltip="true" prop="user_name" width="100" :label="$t('view.table.operator')" />
            <el-table-column :show-overflow-tooltip="true" prop="eap_flag" width="100" :label="$t('view.table.isEAP')" />
            <el-table-column :show-overflow-tooltip="true" prop="face_code" width="100" :label="$t('view.table.orientation')" />
            <el-table-column :show-overflow-tooltip="true" prop="task_from" width="100" :label="$t('view.table.taskSource')" />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { eapApsPlanStationFlowSelect } from '@/api/eap/eapApsPlanReport'
export default {
  name: 'PlanStationFlow',
  props: {
    plan_id: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      loading: true,
      tableData: []
    }
  },
  created() {
    eapApsPlanStationFlowSelect({ 'plan_id': this.plan_id }).then(res => {
      this.loading = false
      const defaultQuery = JSON.parse(JSON.stringify(res))
      if (defaultQuery.code === 0) {
        if (defaultQuery.data.length > 0) {
          this.tableData = defaultQuery.data
        }
      }
    })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 280
    }
  },
  methods: {

  }
}
</script>
<style lang="less" scoped>
::v-deep .wrapElFormFirst {
  .el-form-item__label{
    width: 110px !important;
  }
}
::v-deep .el-descriptions-item__label.is-bordered-label{
  width:130px;
}
</style>

import { sysWhiteLogin, sysWhiteLoginWx, sysLogin, sysWhiteLoginOfVerify, sysLoginOfVerify, getSysInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'
import Cookies from 'js-cookie'
import { Notification } from 'element-ui'
import { encrypt } from '@/utils/rsaEncrypt'
import md5 from 'js-md5'

// promise
// 1、主要用于异步计算
// 2、可以将异步操作队列化，按照期望的顺序执行，返回符合预期的结果
// 3、可以在对象之间传递和操作promise，帮助我们处理队列
const user = {
  state: {
    token: getToken(),
    user: {},
    roles: [],
    permissions: [],
    // 第一次加载菜单时用到
    loadMenus: false
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_USER: (state, user) => {
      state.user = user
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_LOAD_MENUS: (state, loadMenus) => {
      state.loadMenus = loadMenus
    }
  },

  actions: {
    mqttLogin({ commit }, res) {
      // Cookies 设置userId
      const defaultQuery = JSON.parse(JSON.stringify(res.user))
      Cookies.set('userId', encrypt(defaultQuery.user.id))
      Cookies.set('userName', encrypt(defaultQuery.user.username))
      Cookies.set('nickName', defaultQuery.user.nickName)

      setToken(res.token, false)
      commit('SET_TOKEN', res.token)
      setUserInfo(res.user, commit)
      // 第一次加载菜单时用到， 具体见 src 目录下的 permission.js
      commit('SET_LOAD_MENUS', true)
    },
    // 白名单登录
    whiteLogin({ commit }, userInfo) {
      const rememberMe = userInfo.rememberMe
      const password = userInfo.password
      const md5password = md5(password)

      return new Promise((resolve, reject) => {
        sysWhiteLogin(encrypt(userInfo.username), encrypt(userInfo.password),
          userInfo.uuid).then(res => {
          // Cookies 设置userId
          const defaultQuery = JSON.parse(JSON.stringify(res.user))
          Cookies.set('userId', encrypt(defaultQuery.user.id))
          Cookies.set('userName', encrypt(defaultQuery.user.username))
          Cookies.set('nickName', defaultQuery.user.nickName)

          setToken(res.token, rememberMe)
          commit('SET_TOKEN', res.token)
          sessionStorage.setItem('lockPassword', md5password)
          sessionStorage.setItem('newlockPassword', md5password)
          setUserInfo(res.user, commit)
          // 第一次加载菜单时用到， 具体见 src 目录下的 permission.js
          commit('SET_LOAD_MENUS', true)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    whiteLoginWx({ commit }, userInfo) {
      const rememberMe = userInfo.rememberMe
      const password = userInfo.password
      const md5password = md5(password)

      return new Promise((resolve, reject) => {
        sysWhiteLoginWx(encrypt(userInfo.username), encrypt(userInfo.password),
          userInfo.uuid).then(res => {
          // Cookies 设置userId
          console.log(JSON.stringify(res.user));
          const defaultQuery = JSON.parse(JSON.stringify(res.user))
          Cookies.set('userId', encrypt(defaultQuery.user.id))
          Cookies.set('userName', encrypt(defaultQuery.user.username))
          Cookies.set('nickName', defaultQuery.user.nickName)
          Cookies.set('userMsg', res.userMsg)
          Cookies.set('wxRole', JSON.stringify(res.wxRole) || '{}')
          setToken(res.token, rememberMe)
          commit('SET_TOKEN', res.token)
          sessionStorage.setItem('lockPassword', md5password)
          sessionStorage.setItem('newlockPassword', md5password)
          setUserInfo(res.user, commit)
          // 第一次加载菜单时用到， 具体见 src 目录下的 permission.js
          commit('SET_LOAD_MENUS', true)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 登录
    Login({ commit }, userInfo) {
      const rememberMe = userInfo.rememberMe

      return new Promise((resolve, reject) => {
        sysLogin(encrypt(userInfo.username), encrypt(userInfo.password),
          userInfo.code, userInfo.uuid).then(res => {
          // Cookies 设置userId
          const defaultQuery = JSON.parse(JSON.stringify(res.user))
          Cookies.set('userId', encrypt(defaultQuery.user.id))
          Cookies.set('userName', encrypt(defaultQuery.user.username))
          Cookies.set('nickName', defaultQuery.user.nickName)

          setToken(res.token, rememberMe)
          commit('SET_TOKEN', res.token)
          setUserInfo(res.user, commit)
          // 第一次加载菜单时用到， 具体见 src 目录下的 permission.js
          commit('SET_LOAD_MENUS', true)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    whiteLoginOfVerify({ commit }, userInfo) {
      const username = encrypt(userInfo.username)
      Cookies.set('userName', username)
      const rememberMe = userInfo.rememberMe
      const password = userInfo.password
      const md5password = md5(password)

      return new Promise((resolve, reject) => {
        sysWhiteLoginOfVerify(username, encrypt(userInfo.password),
          userInfo.uuid).then(res => {
          // Cookies 设置userId
          const defaultQuery = JSON.parse(JSON.stringify(res.user))
          Cookies.set('userId', encrypt(defaultQuery.user.id))
          Cookies.set('userName', encrypt(defaultQuery.user.username))
          Cookies.set('nickName', defaultQuery.user.nickName)

          setToken(res.token, rememberMe)
          commit('SET_TOKEN', res.token)
          sessionStorage.setItem('lockPassword', md5password)
          sessionStorage.setItem('newlockPassword', md5password)
          setUserInfo(res.user, commit)
          // 第一次加载菜单时用到， 具体见 src 目录下的 permission.js
          commit('SET_LOAD_MENUS', true)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 登录
    LoginOfVerify({ commit }, userInfo) {
      const username = encrypt(userInfo.username)
      Cookies.set('userName', username)
      const rememberMe = userInfo.rememberMe

      return new Promise((resolve, reject) => {
        sysLoginOfVerify(username, encrypt(userInfo.password),
          userInfo.code, userInfo.uuid).then(res => {
          // Cookies 设置userId
          const defaultQuery = JSON.parse(JSON.stringify(res.user))
          Cookies.set('userId', encrypt(defaultQuery.user.id))
          Cookies.set('userName', encrypt(defaultQuery.user.username))
          Cookies.set('nickName', defaultQuery.user.nickName)

          setToken(res.token, rememberMe)
          commit('SET_TOKEN', res.token)
          setUserInfo(res.user, commit)
          // 第一次加载菜单时用到， 具体见 src 目录下的 permission.js
          commit('SET_LOAD_MENUS', true)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit }) {
      return new Promise((resolve, reject) => {
        // 2020-12-23 注释(传入user_id)
        // getInfo().then(res => {
        //  setUserInfo(res, commit)
        //  resolve(res)
        // }).catch(error => {
        //  reject(error)
        // })
        getSysInfo(Cookies.get('userId')).then(res => {
          setUserInfo(res.user, commit)
          resolve(res)
        }).catch(error => {
          reject(error)
        })
      })
    },
    // 登出
    LogOut({ commit }) {
      return new Promise((resolve, reject) => {
        // logout().then(res => {
        //  logOut(commit)
        //  resolve()
        // }).catch(error => {
        //  logOut(commit)
        //  reject(error)
        // })

        // 202-12-23 修改-去掉token认证
        logOut(commit)
        resolve()
      })
    },

    updateLoadMenus({ commit }) {
      return new Promise((resolve, reject) => {
        commit('SET_LOAD_MENUS', false)
      })
    }
  }
}

export const logOut = (commit) => {
  commit('SET_TOKEN', '')
  commit('SET_ROLES', [])
  removeToken()
}

export const setUserInfo = (res, commit) => {
  // 如果没有任何权限，则赋予一个默认的权限，避免请求死循环
  if (res.roles.length === 0) {
    commit('SET_ROLES', ['ROLE_SYSTEM_DEFAULT'])
  } else {
    commit('SET_ROLES', res.roles)
  }

  // 存储用户权限
  if (res.permissions && Array.isArray(res.permissions)) {
    commit('SET_PERMISSIONS', res.permissions)
  } else {
    commit('SET_PERMISSIONS', [])
  }

  commit('SET_USER', res.user)
}

export default user

<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-6 col-12">
              <el-form-item :label="$t('lang_pack.workOrder.workOrderNumber') + ':'">
                <el-input v-model="query.lot_num" size="small" :readonly="true" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.hmiMain.leftNoReadCount') + ':'">
                <el-input v-model="query.left_noread_count" size="small" :readonly="true" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.hmiMain.shortQuantity') + ':'">
                <el-input v-model="query.left_short_count" size="small" :readonly="true" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-6 col-12">
              <el-form-item :label="$t('lang_pack.hmiMain.plateCode') + ':'">
                <el-input v-model="query.panel_barcode" size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item>
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="barcodeScan">{{ $t('lang_pack.commonPage.check') }}</el-button>
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item>
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-circle-close" @click="cancelWip" style="margin-left:10px;background:red;">{{ $t('lang_pack.hmiMain.cancelWip') }}</el-button>
              </el-form-item>
            </div>
            <div class="formChild col-md-2 col-12">
              <el-form-item>
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-s-promotion" @click="endWip" style="margin-left:20px;background:green;">{{ $t('lang_pack.hmiMain.endWip') }}</el-button>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never">
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="loading" border size="small" :data="tableData" style="width: 100%;" cell-style="border:0px;border-bottom:1px solid #dfe6ec" height="350px" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
            <el-table-column :show-overflow-tooltip="true" prop="item_date" width="140" :label="$t('view.table.time')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_index" width="100" :label="$t('view.table.boardSorting')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_barcode" width="120" :label="$t('view.table.boardBarcode')" />
            <el-table-column :show-overflow-tooltip="true" prop="face_code" width="100" :label="$t('view.table.orientation')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_status" :label="$t('view.table.boardStatus')" width="120" >
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.panel_status == 'NG' ? 'danger' : (scope.row.panel_status == 'OK' ? 'success' : 'warning'))"
                  class="elTag"
                >
                  {{ scope.row.panel_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_code" width="150" :label="$t('view.table.ngBoardErrorCode')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_msg" width="150" :label="$t('view.table.ngBoardErrorDescription')" />
            <el-table-column :show-overflow-tooltip="true" prop="inspect_flag" width="150" :label="$t('view.table.isFirstCheckBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="dummy_flag" width="120" :label="$t('view.table.isDummyBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="manual_judge_code" width="100" :label="$t('view.table.manualProcessingBoard')" />
            <el-table-column :show-overflow-tooltip="true" prop="user_name" width="100" :label="$t('view.table.operator')" />
          </el-table>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>

import { eapDyApsPlanStationFlowSelect,eapDyUnLoadPlanPanelCheckAndSaveNew,eapDyUnLoadPlanWipManualReport } from '@/api/eap/project/dy/eapDyApsPlan'
import Cookies from 'js-cookie'
export default {
  name: 'PlanStationFlow',
  props: {
    plan_id: {
      type: String,
      default: ''
    },
    group_lot_num: {
      type: String,
      default: ''
    },
    lot_num: {
      type: String,
      default: ''
    },
    pallet_num: {
      type: String,
      default: ''
    },
    port_code: {
      type: String,
      default: ''
    },
    plan_lot_count: {
      type: Number,
      default: 0
    },
    wip_ok_count: {
      type: Number,
      default: 0
    },
    wip_noread_count: {
      type: Number,
      default: 0
    },
    wip_short_count: {
      type: Number,
      default: 0
    },
  },
  // 数据模型
  data() {
    return {
      loading: true,
      tableData: [],
      query: {
        lot_num: '',
        left_noread_count:0,
        left_short_count:0,
        panel_barcode:''
      },
    }
  },
  created: function() {
    this.query.lot_num=this.lot_num
    this.query.left_noread_count=this.wip_noread_count
    this.query.left_short_count=this.wip_short_count
    this.reflashData()
  },
  methods:{
    reflashData(){
      eapDyApsPlanStationFlowSelect({ 'plan_id': this.plan_id }).then(res => {
        this.loading = false
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.tableData = defaultQuery.data
          }
        }
      })
        .catch(() => {
          this.$message({
            message: 'select error',
            type: 'error'
          })
      })
    },
    //扫描板件条码
    barcodeScan(){
      if(this.query.left_short_count<=0){
        this.$message({ message: 'need no more scan', type: 'warning' })
        return
      }
      if(this.query.panel_barcode==='' || this.query.panel_barcode==='NoRead'){
        this.$message({ message: 'scan barcode can not be empty', type: 'warning' })
        return
      }
      const query = {
          userName: Cookies.get('userName'),
          station_id: this.$route.query.station_id,
          station_code: this.$route.query.station_code,
          panel_barcode: this.query.panel_barcode,
          port_code: this.port_code,
          group_lot_num: this.group_lot_num,
          plan_id: this.plan_id,
          lot_num: this.lot_num
      }
      eapDyUnLoadPlanPanelCheckAndSaveNew(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message({ message: 'The Panel is Check OK', type: 'success' })
          this.query.left_short_count=parseInt(this.query.left_short_count.toString())-1
          this.reflashData()
          this.query.panel_barcode=''
        }
        else{
          this.$message({ message: defaultQuery.msg, type: 'error' })
        }
      })
        .catch(() => {
          this.$message({
            message: 'scan error',
            type: 'error'
          })
      })
    },
    //放弃补报
    cancelWip(){
      if(this.query.left_short_count<=0){
        this.$message({ message: 'need no more scan', type: 'warning' })
        return
      }
      var tipMsg='Give Up Scan,Panel is Set NoRead?'
      //弹窗等待确认
      this.$confirm(tipMsg, this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
            const query = {
                userName: Cookies.get('userName'),
                station_id: this.$route.query.station_id,
                station_code: this.$route.query.station_code,
                panel_barcode: 'NoRead',
                port_code: this.port_code,
                group_lot_num: this.group_lot_num,
                plan_id: this.plan_id,
                lot_num: this.lot_num,
                noread_flag:'Y'
            }
            eapDyUnLoadPlanPanelCheckAndSaveNew(query).then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: 'One Panel is Cancel OK', type: 'success' })
                this.query.left_short_count=parseInt(this.query.left_short_count.toString())-1
                this.query.left_noread_count=parseInt(this.query.left_noread_count.toString())+1
                this.reflashData()
                this.query.panel_barcode=''
              }
              else{
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
              .catch(() => {
                this.$message({
                  message: 'wip error',
                  type: 'error'
                })
            })
        })
        .catch(() => {
      })
    },
    //结束补报
    endWip(){
      //弹窗等待确认
      var tipMsg='Finish Wip?'
      this.$confirm(tipMsg, this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
            const query = {
                userName: Cookies.get('userName'),
                station_id: this.$route.query.station_id,
                station_code: this.$route.query.station_code,
                port_code: this.port_code,
                group_lot_num: this.group_lot_num,
                plan_id: this.plan_id,
                lot_num: this.lot_num,
                pallet_num: this.pallet_num
            }
            eapDyUnLoadPlanWipManualReport(query).then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: 'The Wip is Report OK', type: 'success' })
              }
              else{
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
              .catch(() => {
                this.$message({
                  message: 'wip error',
                  type: 'error'
                })
            })
        })
        .catch(() => {
      })
    }
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 280
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .wrapElFormFirst {
  .el-form-item__label{
    width: 110px !important;
  }
}
::v-deep .el-descriptions-item__label.is-bordered-label{
  width:130px;
}
</style>

[{"borderColor": "", "color": "yellow", "describe": "A跨", "height": 60, "stock_code": "", "stock_count": "", "model_type": "", "strokeWidth": "", "type": "text", "width": 330, "x": 1400, "y": 460}, {"borderColor": "", "color": "yellow", "describe": "B跨", "height": 60, "stock_code": "", "stock_count": "", "model_type": "", "strokeWidth": "", "type": "text", "width": 330, "x": 1400, "y": 280}, {"borderColor": "", "color": "yellow", "describe": "C跨", "height": 60, "stock_code": "", "stock_count": "", "model_type": "", "strokeWidth": "", "type": "text", "width": 330, "x": 700, "y": 100}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 740, "y": 0, "x2": 700}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 940, "y": 30, "x2": 200}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 940, "y": 70, "x2": 200}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 1270, "y": 30, "x2": 150}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 1270, "y": 50, "x2": 150}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 1270, "y": 70, "x2": 150}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 0, "y": 480, "x2": 120}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 0, "y": 480, "x2": 120}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "line", "width": 60, "x": 0, "y": 520, "x2": 120}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "rkgd", "width": 60, "x": 0, "y": 185}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "rkgd", "width": 60, "x": 0, "y": 365}, {"color": "", "describe": "", "height": 190, "stock_code": "", "stock_count": "", "borderColor": "yellow", "strokeWidth": 0, "type": "rkgd", "width": 60, "x": 0, "y": 546}, {"color": "", "describe": "5号天车", "showDialog": "crownCar", "height": 190, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "bigCar", "width": 60, "x": 1365, "y": 0, "data": [{"tcbh": "5号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "5号天车", "showDialog": "crownCar", "height": 20, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "smallCar", "width": 45, "x": 1373, "y": 0, "data": [{"tcbh": "5号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "4号天车", "showDialog": "crownCar", "height": 180, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "bigCar", "width": 60, "x": 1305, "y": 185, "data": [{"tcbh": "4号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "4号天车", "showDialog": "crownCar", "height": 20, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "smallCar", "width": 45, "x": 1313, "y": 185, "data": [{"tcbh": "4号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "2号天车", "showDialog": "crownCar", "height": 180, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "bigCar", "width": 60, "x": 1365, "y": 365, "data": [{"tcbh": "2号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "2号天车", "showDialog": "crownCar", "height": 20, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "smallCar", "width": 45, "x": 1373, "y": 365, "data": [{"tcbh": "2号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "3号天车", "showDialog": "crownCar", "height": 180, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "bigCar", "width": 60, "x": 155, "y": 185, "data": [{"tcbh": "3号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "3号天车", "showDialog": "crownCar", "height": 20, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "smallCar", "width": 45, "x": 163, "y": 185, "data": [{"tcbh": "3号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "1号天车", "showDialog": "crownCar", "height": 180, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "bigCar", "width": 60, "x": 255, "y": 365, "data": [{"tcbh": "1号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "1号天车", "showDialog": "crownCar", "height": 20, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "smallCar", "width": 45, "x": 263, "y": 365, "data": [{"tcbh": "1号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "6号天车", "showDialog": "crownCar", "height": 90, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "bigCar", "width": 60, "x": 55, "y": 455, "data": [{"tcbh": "6号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}, {"color": "", "describe": "6号天车", "showDialog": "crownCar", "height": 20, "stock_code": "", "stock_count": "", "strokeWidth": 0, "type": "smallCar", "width": 45, "x": 65, "y": 455, "data": [{"tcbh": "6号天车", "dqzt": "空闲", "rwbh": "00001", "gbpc": "202505", "zqwz": "B跨F-30", "fzwz": "F跨B-30", "zyzt": "完成"}]}]
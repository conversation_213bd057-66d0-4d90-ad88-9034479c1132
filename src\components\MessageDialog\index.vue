<template>
  <el-dialog
    title="消息历史"
    :visible.sync="dialogVisible"
    width="600px"
    :modal="false"
    :close-on-click-modal="false"
    append-to-body
    class="message-dialog"
    :before-close="handleClose">
    <div class="message-list-container">
      <div class="message-filters">
        <!--
        <el-select v-model="messageFilter.cim_from" placeholder="消息来源" clearable size="small" style="width: 120px; margin-right: 10px;">
          <el-option label="全部" value="" />
          <el-option label="EAP" value="EAP" />
          <el-option label="CIM" value="CIM" />
          <el-option label="PLC" value="PLC" />
        </el-select>
        -->
        <el-select v-model="messageFilter.finish_flag" placeholder="状态" clearable size="small" style="width: 100px; margin-right: 10px;">
          <el-option label="全部" value="" />
          <el-option label="已读" value="Y" />
          <el-option label="未读" value="N" />
        </el-select>
        <el-button type="primary" size="small" @click="loadMessageHistory">刷新</el-button>
      </div>

      <div class="message-list" style="height: 400px; overflow-y: auto;">
        <div v-if="messageList.length === 0" class="no-messages">
          <i class="el-icon-chat-dot-round" style="font-size: 48px; color: #ddd;"></i>
          <p style="color: #999; margin-top: 10px;">暂无消息</p>
        </div>
        <div v-else>
          <div
            v-for="(message, index) in messageList"
            :key="message.id || index"
            class="message-item"
            :class="{ 'unread': message.finish_flag === 'N' }">
            <div class="message-header">
              <span class="message-source" :class="getMessageSourceClass(message.cim_from)">
                {{ message.cim_from || 'SYSTEM' }}
              </span>
              <span class="message-time">{{ message.item_date }}</span>
              <el-tag v-if="message.finish_flag === 'N'" type="danger" size="mini">未读</el-tag>
            </div>
            <div class="message-content">
              {{ message.cim_msg }}
            </div>
            <!--
              <div class="message-meta">
                <span class="message-type">类型: {{ getMessageTypeText(message.screen_control) }}</span>
                <span v-if="message.screen_control === '0' && message.interval_second_time" class="message-duration">
                  | 关闭时间: {{ message.interval_second_time }}秒
                </span>
                <span v-else-if="message.screen_control === '0'" class="message-duration">
                  | 关闭时间: 5秒(默认)
                </span>
                <span v-if="message.popup_position" class="message-position">
                  | 位置: {{ getPositionText(message.popup_position) }}
                </span>
                <span v-if="message.popup_type" class="message-popup-type">
                  | 弹窗类型: {{ getPopupTypeText(message.popup_type) }}
                </span>
              </div>
            -->
          </div>
        </div>
      </div>

      <div class="message-pagination">
        <div class="pagination-controls">
          <el-button
            size="small"
            :disabled="!hasPrevPage"
            @click="handlePrevPage">
            上一页
          </el-button>
          <span class="page-info">第 {{ messagePage }} 页 / 共 {{ totalPages }} 页</span>
          <el-button
            size="small"
            :disabled="!hasNextPage"
            @click="handleNextPage">
            下一页
          </el-button>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { sel as selHmiMessages, selHmiMessagesNew } from '@/api/eap/eapStationHmiShow'
import Cookies from 'js-cookie'

export default {
  name: 'MessageDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentStation: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      messageList: [],
      messageTotal: 0,
      messagePage: 1,
      messagePageSize: 10,
      totalPages: 0,
      messageFilter: {
        cim_from: '',
        finish_flag: ''
      },
      hasNextPage: false, // 是否有下一页
      hasPrevPage: false  // 是否有上一页
    }
  },
  watch: {
    visible(newVal) {
      this.dialogVisible = newVal
      if (newVal) {
        this.resetPagination()
        this.loadMessageHistory()
      }
    },
    dialogVisible(newVal) {
      this.$emit('update:visible', newVal)
    }
  },
  methods: {
    handleClose() {
      this.dialogVisible = false
    },

    resetPagination() {
      this.messagePage = 1
      this.totalPages = 0
      this.hasNextPage = false
      this.hasPrevPage = false
    },

    loadMessageHistory() {
      // 确保使用当前选中站点的station_id
      if (!this.currentStation.station_id) {
        this.$message.warning('请先选择工位')
        return
      }

      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        cim_from: this.messageFilter.cim_from,
        finish_flag: this.messageFilter.finish_flag,
        pageSize: this.messagePageSize,
        currentPage: this.messagePage
      }

      console.log('查询消息历史参数:', query)

      selHmiMessagesNew(query).then(res => {
        if (res.code === 0) {
          console.log('接口返回数据:', res)
          
          // 接口返回的数据结构：消息列表直接在res.data中
          this.messageList = res.data || []
          this.messageTotal = res.count || 0
          
          // 计算分页信息
          this.totalPages = Math.ceil(this.messageTotal / this.messagePageSize)
          this.hasNextPage = this.messagePage < this.totalPages
          this.hasPrevPage = this.messagePage > 1

          console.log('消息列表:', this.messageList)
          console.log('总数:', this.messageTotal, '当前页:', this.messagePage, '总页数:', this.totalPages)
          console.log('分页状态 - 有下一页:', this.hasNextPage, '有上一页:', this.hasPrevPage)
        } else {
          this.$message.error('获取消息历史失败')
        }
      }).catch(error => {
        console.error('获取消息历史异常:', error)
        this.$message.error('获取消息历史异常')
      })
    },

    handlePrevPage() {
      if (this.hasPrevPage && this.messagePage > 1) {
        this.messagePage--
        this.loadMessageHistory()
      }
    },

    handleNextPage() {
      if (this.hasNextPage && this.messagePage < this.totalPages) {
        this.messagePage++
        this.loadMessageHistory()
      }
    },

    getMessageTypeText(screenControl) {
      switch (screenControl) {
        case '0':
          return '自动关闭'
        case '1':
          return '手动关闭'
        default:
          return '未知'
      }
    },

    getPositionText(position) {
      const positionMap = {
        'top-right': '右上角',
        'top-left': '左上角',
        'bottom-right': '右下角',
        'bottom-left': '左下角',
        'center': '正中间'
      }
      return positionMap[position] || position
    },

    getPopupTypeText(type) {
      const typeMap = {
        'success': '成功',
        'warning': '警告',
        'info': '信息',
        'error': '错误'
      }
      return typeMap[type] || type
    },

    getMessageSourceClass(source) {
      switch (source) {
        case 'EAP':
          return 'source-eap'
        case 'CIM':
          return 'source-cim'
        case 'PLC':
          return 'source-plc'
        default:
          return 'source-default'
      }
    }
  }
}
</script>

<style scoped>
.message-dialog {
  .el-dialog__body {
    padding: 10px 20px;
  }
}

.message-list-container {
  .message-filters {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
  }

  .message-list {
    .no-messages {
      text-align: center;
      padding: 50px 0;
    }

    .message-item {
      border: 1px solid #e6e8ee;
      border-radius: 6px;
      margin-bottom: 10px;
      padding: 12px;
      background: #fff;
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }

      &.unread {
        border-left: 4px solid #409eff;
        background: #f0f9ff;
      }

      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .message-source {
          padding: 2px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: bold;

          &.source-eap {
            background: #e1f5fe;
            color: #0277bd;
          }

          &.source-cim {
            background: #f3e5f5;
            color: #7b1fa2;
          }

          &.source-plc {
            background: #e8f5e8;
            color: #2e7d32;
          }

          &.source-default {
            background: #f5f5f5;
            color: #666;
          }
        }

        .message-time {
          font-size: 12px;
          color: #999;
        }
      }

      .message-content {
        color: #333;
        line-height: 1.5;
        margin-bottom: 8px;
      }

      .message-meta {
        .message-type, .message-duration, .message-position, .message-popup-type {
          font-size: 12px;
          color: #666;
        }

        .message-duration, .message-position, .message-popup-type {
          color: #999;
          margin-left: 5px;
        }
      }
    }
  }

  .message-pagination {
    margin-top: 15px;
    text-align: center;

    .pagination-controls {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;

      .page-info {
        font-size: 14px;
        color: #666;
      }
    }
  }
}
</style>

<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.wx.recipeName') + ':'">
                <el-input v-model="query.recipe_name" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('zhcy.materialNumber') + ':'">
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.wx.equipment') + ':'">
                <el-input v-model="query.device_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('lang_pack.commonPage.validIdentification')">
                <el-select v-model="query.enable_flag" clearable>
                  <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <div class="recipeInfo">
        <!-- 扫码栏-->
        <div style="visibility: hidden;">
          <span>{{ $t('lang_pack.wx.scanDownload') }}</span>
          <el-input ref="recipeInfoScan" v-model="recipeInfoScan" style="width: 500px;" type="text" />
          <el-button
            v-if="isButtonVisible('download')"
            v-permission="permission.download"
            slot="reference"
            class="filter-item"
            type="primary"
            icon="el-icon-download"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="recipeInfoScan === ''"
            @click="batchDownload()"
          >
            {{ $t('lang_pack.commonPage.download') }}
          </el-button>
        </div>
        <div v-if="isButtonVisible('edit')" style=" display: flex;justify-content: right;align-items: center;">
          <span>{{ $t('lang_pack.wx.editParameters') }}</span>
          <el-switch v-model="disabled" active-color="#13ce66" inactive-color="#ff4949" />
        </div>
      </div>

      <!--工具栏-->
      <crudOperation v-if="isButtonVisible('edit')" show="" :permission="permission">
        <template slot="right">
          <el-button
            v-permission="permission.export"
            slot="reference"
            class="filter-item"
            type="success"
            icon="el-icon-download"
            plain
            round
            size="small"
            :disabled="crud.selections.length === 0"
            @click="exportRecipe()"
          >
            导出配方
          </el-button>
          <el-button
            v-permission="permission.template"
            slot="reference"
            class="filter-item"
            type="warning"
            icon="el-icon-document"
            plain
            round
            size="small"
            @click="exportTemplate()"
          >
            下载模板
          </el-button>
          <el-upload
            v-permission="permission.import"
            ref="upload"
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :show-file-list="false"
            :on-change="handleFileChange"
            accept=".xls"
            style="display: inline-block; margin-left: 10px;"
          >
            <el-button
              slot="trigger"
              class="filter-item"
              type="primary"
              icon="el-icon-upload2"
              plain
              round
              size="small"
            >
              导入配方
            </el-button>
          </el-upload>
          <el-button
            v-permission="permission.batchDelete"
            slot="reference"
            class="filter-item"
            type="danger"
            icon="el-icon-delete"
            plain
            round
            size="small"
            :loading="crud.delAllLoading"
            :disabled="crud.selections.length === 0"
            @click="batchDelete()"
          >
            {{ $t('lang_pack.commonPage.remove') }}
          </el-button>
        </template>
      </crudOperation>
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
        <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
          <el-form-item :label="$t('lang_pack.wx.recipeType')" prop="recipe_type">
            <el-select v-model="form.recipe_type" clearable @change="chooseRecipeType">
              <el-option v-for="item in dict.EAP_RECIPE_TYPE" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('lang_pack.wx.recipeName')" prop="recipe_name">
            <el-input v-model="form.recipe_name" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.wx.version')" prop="recipe_version">
            <el-input v-model="form.recipe_version" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.meStationFlow.mainMaterialCode')" prop="material_code">
            <div style="display: flex; gap: 10px; align-items: center;">
              <el-input v-model="form.material_code" style="flex: 1;" />
              <el-button type="primary" @click="getMaterialInfo" :loading="materialInfoLoading">获取物料信息</el-button>
            </div>
          </el-form-item>
          <el-form-item label="板厚" prop="device_code">
            <el-input v-model="form.device_code" />
          </el-form-item>
          <el-form-item label="孔径" prop="device_des">
            <el-input v-model="form.device_des" />
          </el-form-item>
          <el-form-item label="孔数" prop="material_des">
            <el-input v-model="form.material_des" />
          </el-form-item>
          <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
            <el-select v-model="form.enable_flag" clearable>
              <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <div v-if="copyInfo.show" style="background-color: #f5f7fa; padding: 15px; margin-bottom: 20px; border-radius: 4px; border-left: 4px solid #409eff;">
            <div style="display: flex; flex-wrap: wrap; gap: 20px;">
               <div><strong>复制配方名称:</strong> {{ copyInfo.originalName }}</div>
             </div>
          </div>
        </el-form>
        <el-divider />
        <div style="text-align: center">
          <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.wx.cancel') }}</el-button>
          <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('view.button.confirm') }}</el-button>
        </div>
      </el-drawer>
      <div class="wrapRowItem">
        <el-row :gutter="20">
          <el-col :span="24">
            <!--表格渲染-->
            <el-table
              ref="table"
              v-loading="crud.loading"
              size="small"
              :data="crud.data"
              style="width: 100%"
              :cell-style="crud.cellStyle"
              height="478"
              max-height="478"
              highlight-current-row
              @selection-change="crud.selectionChangeHandler"
              @row-click="handleRowClick"
            >
              <el-table-column type="selection" width="45" align="center" />
              <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
              <el-table-column v-if="1 == 0" width="10" prop="recipe_id" label="id" />
              <el-table-column :show-overflow-tooltip="true" width="200" prop="recipe_name" :label="$t('lang_pack.wx.recipeName')" />
              <el-table-column :show-overflow-tooltip="true" prop="device_code" label="板厚" />
              <el-table-column :show-overflow-tooltip="true" prop="material_code" :label="$t('lang_pack.meStationFlow.mainMaterialCode')" />
              <el-table-column :label="$t('lang_pack.fastCode.isEnabled')" align="center" prop="enable_flag">
                <template slot-scope="scope">
                  <!--取到当前单元格-->
                  {{ scope.row.enable_flag === 'Y' ? $t('zhcy.valid') : $t('zhcy.invalid') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('lang_pack.commonPage.operate')" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <operation :data="scope.row" :permission="permission" :disabled-edit="!isButtonVisible('edit')" :disabled-dle="!isButtonVisible('del')" @ok="doDelete">
                    <template slot="right">
                      <el-button v-if="isButtonVisible('copy')" v-permission="permission.copy" slot="reference" type="text" size="small" @click="handleCopyRecipe(scope.row)">复制</el-button>
                      <el-button v-if="isButtonVisible('addParams')" v-permission="permission.addParams" slot="reference" type="text" size="small" @click="$refs.detail && $refs.detail.crud.toAdd()">{{ $t('lang_pack.wx.addParams') }}</el-button>
                      <!--<el-button slot="reference" type="text" size="small" style="margin-left:0px" @click="down(scope.row)">下发</el-button>-->
                    </template>
                  </operation>

                </template>
              </el-table-column>
            </el-table>
            <!--分页组件-->
            <pagination />
          </el-col>
        </el-row>
        <!-- 列表 -->
        <el-row :gutter="20">
          <el-col :span="24">
            <detail ref="detail" class="tableFirst" :recipe_detail_id="currentRecipeId" :disabled="disabled" />
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import eapRecipe from '@/api/eap/project/sfcom/eapRecipe'
import detail from './detail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import operation from '@/views/core/system/errorMsg/operation.vue'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
import mqtt from 'mqtt'
import { selCellIP } from '@/api/core/center/cell'
import { getMaterialInfo, EapRecipeExport, EapRecipeImport } from '@/api/eap/project/hsql/index'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {
  recipe_id: '',
  recipe_name: '',
  recipe_version: '',
  device_code: '',
  device_des: '',
  material_code: '',
  material_des: '',
  recipe_type: '',
  enable_flag: 'Y',
  recipeFileNames: ''
}
export default {
  name: 'EAP_RECIPE',
  components: { crudOperation, rrOperation, udOperation, pagination, detail, operation },
  props: {},
  cruds() {
    return CRUD({
      title: this.parent.$t('zhcy.recipeMaintenance'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_id',
      // 排序
      sort: ['recipe_id desc'],
      // CRUD Method
      crudMethod: { ...eapRecipe },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true
      },
      // 设置默认分页大小为20条每页
      props: {
        pageSize: 20
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'EAP_RECIPE_TYPE'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      recipeInfoScan: '',
      permission: {
        add: ['admin', 'c_eap_fmod_recipe_maintain:add'],
        edit: ['admin', 'c_eap_fmod_recipe_maintain:edit'],
        del: ['admin', 'c_eap_fmod_recipe_maintain:del'],
        copy: ['admin', 'c_eap_fmod_recipe_maintain:copy'],
        addParams: ['admin', 'c_eap_fmod_recipe_maintain:addParams'],
        download: ['admin', 'c_eap_fmod_recipe_maintain:download'],
        export: ['admin', 'c_eap_fmod_recipe_maintain:export'],
        template: ['admin', 'c_eap_fmod_recipe_maintain:template'],
        import: ['admin', 'c_eap_fmod_recipe_maintain:import'],
        batchDelete: ['admin', 'c_eap_fmod_recipe_maintain:batchDelete']
      },
      rules: {
        recipe_type: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        recipe_version: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        recipe_name: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        material_code: [{ required: false, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        material_des: [{ required: false, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        device_code: [{ required: false, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        device_des: [{ required: false, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      currentRecipeId: 0,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // 认证信息
        clientId:
            'ScadaWeb_' +
            Cookies.get('userId') +
            '_' +
            Math.random().toString(16).substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        cleansession: false
      },
      tagKeyList: [],
      applyObj: {
        uploadValue: '',
        downloadValue: '',
        downValue: ''
      },
      popoverContent: '',
      parameterObj: {},
      disabled: false,
      // 复制信息
      copyInfo: {
        show: false,
        originalName: '',
        originalId: '',
        originalVersion: ''
      },
      // 获取物料信息加载状态
      materialInfoLoading: false
    }
  },
  watch: {},
  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 200
    }
    this.$refs.recipeInfoScan.focus()
  },
  created: function() {
    this.toStartWatch()
    this.fetchContent()
  },
  methods: {
    // 检查用户是否有指定权限
    hasPermission(permission) {
      try {
        // 获取用户权限信息
        var userInfo = this.$store.state.user || {}
        var userPermissions = []
        
        // 优先从Store的permissions字段获取
        if (this.$store.state.user.permissions && Array.isArray(this.$store.state.user.permissions)) {
          userPermissions = this.$store.state.user.permissions
        } else if (userInfo.permissions && Array.isArray(userInfo.permissions)) {
          userPermissions = userInfo.permissions
        } else {
          userPermissions = []
        }
        
        // 确保permissions是数组
        if (!Array.isArray(userPermissions)) {
          console.warn('用户权限不是数组格式:', userPermissions)
          userPermissions = []
        }
        // 检查是否包含指定权限
        var hasPermission = userPermissions.includes(permission)
        
        return hasPermission
        
      } catch (error) {
        console.error('权限检查出错:', error)
        // 出错时返回false，确保安全
        return false
      }
    },
    // 检查按钮是否可见（基于权限控制）
    isButtonVisible(permissionType) {
      // 如果没有指定权限类型，检查是否有任何配方管理权限
      if (!permissionType) {
        return this.hasPermission('c_eap_fmod_recipe_maintain:edit') || 
               this.hasPermission('c_eap_fmod_recipe_maintain:add') ||
               this.hasPermission('c_eap_fmod_recipe_maintain:del')
      }
      
      // 根据权限类型检查具体权限
      var permissionMap = {
        'add': 'c_eap_fmod_recipe_maintain:add',
        'edit': 'c_eap_fmod_recipe_maintain:edit',
        'del': 'c_eap_fmod_recipe_maintain:del',
        'copy': 'c_eap_fmod_recipe_maintain:copy',
        'addParams': 'c_eap_fmod_recipe_maintain:addParams',
        'download': 'c_eap_fmod_recipe_maintain:download',
        'export': 'c_eap_fmod_recipe_maintain:export',
        'template': 'c_eap_fmod_recipe_maintain:template',
        'import': 'c_eap_fmod_recipe_maintain:import',
        'batchDelete': 'c_eap_fmod_recipe_maintain:batchDelete'
      }
      
      var permission = permissionMap[permissionType]
      if (permission) {
        return this.hasPermission(permission)
      }
      
      return false
    },
    // 多个删除
    batchDelete() {
      this.$confirm(this.$t('lang_pack.wx.confirmDelete') + `${this.crud.selections.length}` + this.$t('lang_pack.wx.articleData') + '?',
        this.$t('lang_pack.Prompt'), {
          confirmButtonText: this.$t('tlps.confirm.confirmButtonText'),
          cancelButtonText: this.$t('tlps.confirm.cancelButtonText'),
          type: 'warning'
        })
        .then(() => {
          const ids = this.crud.selections.map(item => item[this.crud.idField]).join(',')
          const recipeFileNames = this.crud.selections.map(item => item.recipe_name + '-' + item.recipe_version).join(',')
          const query = {
            ids,
            recipeFileNames,
            user_name: Cookies.get('userName')
          }
          this.delete(query)
        }).catch(() => {})
    },
    // 单个删除
    doDelete(data) {
      const query = {
        ids: data.recipe_id,
        recipeFileNames: data.recipe_name + '-' + data.recipe_version,
        user_name: Cookies.get('userName')
      }
      this.delete(query)
    },
    delete(data) {
      eapRecipe.del(data).then(res => {
        if (res.code === 0) {
          this.$message({
            message: this.$t('lang_pack.commonPage.deleteSuccesful'),
            type: 'success'
          })
          this.crud.toQuery()
        } else {
          this.$message({
            message: res.msg || this.$t('lang_pack.commonPage.operationfailure'),
            type: 'error'
          })
        }
      }).catch((e) => {
        this.$message({
          message: this.$t('lang_pack.commonPage.operationfailure'),
          type: 'error'
        })
      })
    },
    chooseRecipeType(val) {
      console.log(val)
      if (val === 'MATERIAL') {
        this.rules.material_code[0].required = true
        this.rules.material_des[0].required = true
        this.rules.device_code[0].required = false
        this.rules.device_des[0].required = false
      } else {
        this.rules.material_code[0].required = false
        this.rules.material_des[0].required = false
        this.rules.device_code[0].required = true
        this.rules.device_des[0].required = true
      }
    },
    handleRowClick(row, column, event) {
      this.currentRecipeId = row.recipe_id
      console.log(row.recipe_id)
    },
    async handleMouseEnter() {
      await this.fetchContent()
    },
    handleMouseLeave() {
    },
    async fetchContent() {
    },
    handleDevice() {
      eapRecipe.equipStatusReport({ station_code: this.$route.query.station_code }).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          this.$message({
            type: 'success',
            message: this.$t('lang_pack.wx.SuccessfullyMes')
          })
        }
      }).catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.operationException'),
          type: 'error'
        })
      })
    },
    // 批量上传
    batchUplaod() {
      if (this.applyObj.uploadValue === '1') {
        this.$message({
          type: 'warning',
          message: this.$t('lang_pack.wx.waitUpload')
        })
        return
      }
      const dataKey = [
        { TagKey: 'PmAis/AisStatus/ApplyUploadRecipe', TagValue: '1' },
        { TagKey: 'PmAis/AisStatus/UploadRecipeName', TagValue: this.crud.selections.map(item => item.recipe_name).join(',') }
      ]
      this.scadaPoint(dataKey)
      this.$message({
        type: 'success',
        message: this.$t('lang_pack.wx.uploadSuccessful')
      })
    },
    // 单个上传
    upload(row) {
      if (this.applyObj.uploadValue === '1') {
        this.$message({
          type: 'warning',
          message: this.$t('lang_pack.wx.waitUpload')
        })
        return
      }
      const dataKey = [
        { TagKey: 'PmAis/AisStatus/ApplyUploadRecipe', TagValue: '1' },
        { TagKey: 'PmAis/AisStatus/UploadRecipeName', TagValue: row.recipe_name }
      ]
      this.scadaPoint(dataKey)
      this.$message({
        type: 'success',
        message: this.$t('lang_pack.wx.uploadSuccessful')
      })
    },
    // 批量下载
    batchDownload() {
      var method = '/aisEsbWeb/eap/project/sfcom/recipe/download'
      var path = 'http://127.0.0.1:9090' + method
      var request = { 'lotNo': this.recipeInfoScan }
      axios.post(path, request, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            this.$message({
              type: 'success',
              message: this.$t('lang_pack.wx.scadaRequest')
            })
          } else {
            this.$message({
              type: 'success',
              message: this.$t('lang_pack.wx.scadaError')
            })
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    },
    // 单个下载
    downLoad(row) {
      var method = '/aisEsbWeb/eap/project/sfcom/recipe/download'
      var path = 'http://127.0.0.1:9090' + method
      var request = { 'lot_no': this.recipeInfoScan }
      axios.post(path, request, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  if (result[k].tag_key === 'PmAis/AisStatus/ApplyUploadRecipe') {
                    this.applyObj.uploadValue = result[k].tag_value
                  }
                  if (result[k].tag_key === 'PmAis/AisStatus/ApplyDownLoadRecipe') {
                    this.applyObj.downloadValue = result[k].tag_value
                  }
                  if (result[k].tag_key === 'PmAis/AisStatus/ApplyDistributeRecipe') {
                    this.applyObj.downValue = result[k].tag_value
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    },
    // 单个下发
    down(row) {
      this.$confirm(this.$t('lang_pack.wx.downLoadRecipe'), this.$t('lang_pack.Prompt'), {
        confirmButtonText: this.$t('tlps.confirm.confirmButtonText'),
        cancelButtonText: this.$t('tlps.confirm.cancelButtonText'),
        type: 'warning'
      }).then(() => {
        //  TagKey: 'PmPlc/PcStatus/RecipeDownReq'
        const dataKey = [
          { TagKey: 'PmPlc/PcStatus/RecipeDownReq', TagValue: '1' },
          { TagKey: 'PmPlc/PcRecipe/RecipeCode', TagValue: row.recipe_name + '_' + row.recipe_version }
        ]
        this.scadaPoint(dataKey)
        this.$message({
          type: 'success',
          message: this.$t('zhcy.recipeDeploySuccess')
        })
      })
    },
    scadaPoint(dataKey) {
      for (let i = 0; i < dataKey.length; i++) {
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: dataKey[i].TagKey,
          TagValue: dataKey[i].TagValue
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/PmPlc'
        this.sendMessage(topic, sendStr)
      }
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      console.log(topic, msg)
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.warn('MQTT发送成功：' + topic)
        } else {
          console.warn('MQTT发送失败：' + topic)
        }
      })
    },
    toStartWatch() {
      // 获取连接地址
      // 'ws://***************:8090/mqtt'
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.result))
          const result = JSON.parse(defaultQuery)
          if (result === '' || result === undefined || result == null) {
            this.$message({
              message: this.$t('lang_pack.monitor.serviceCell'),
              type: 'error'
            })
            return
          }
          var connectUrl = 'ws://' + result.ip + ':' + result.mqtt_port + '/mqtt'
          this.getTagValue(result.ip, result.webapi_port)
          //  var connectUrl ="ws://*************:8083/mqtt";
          // connectUrl=MQTT_SERVICE;
          console.log('拼接URL：' + connectUrl)
          // mqtt连接
          // this.clientMqtt = mqtt.connect(MQTT_SERVICE, this.optionsMqtt); //开启连接
          this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
          this.clientMqtt.on('connect', (e) => {
            this.mqttConnStatus = true
            for (let index = 0; index < this.tagKeyList.length; index++) {
              this.topicSubscribe('SCADA_CHANGE/' + this.tagKeyList[index])
            }
          })

          // MQTT连接失败
          this.clientMqtt.on('error', (error) => {
            this.$message({
              message: this.$t('lang_pack.vie.cnneFailed'),
              type: 'error'
            })
            this.clientMqtt.end()
          })
          // 断开发起重连(异常)
          this.clientMqtt.on('reconnect', (error) => {
            this.$message({
              message: this.$t('lang_pack.vie.connDisconRecon'),
              type: 'error'
            })
          })
          this.clientMqtt.on('disconnect', (error) => {
            this.$message({
              message: this.$t('lang_pack.vie.cnneFailed'),
              type: 'error'
            })
          })
          this.clientMqtt.on('close', () => {
            this.clientMqtt.end()

            this.$message({
              message: this.$t('lang_pack.vie.cnneFailed'),
              type: 'error'
            })
          })
          // 接收消息处理
          this.clientMqtt.on('message', (topic, message) => {
            // 解析传过来的数据
            this.mqttUpdateTable(topic, message)
          })
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.operationException'),
            type: 'error'
          })
        })
    },
    getTagValue(ip, port) {
      var readTagArray = []
      for (var i = 0; i < this.tagKeyList.length; i++) {
        var readTag = {}
        readTag.tag_key = this.tagKeyList[i].toString()
        readTagArray.push(readTag)
      }
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + port + method
      } else {
        path = 'http://' + ip + ':' + port + method
      }
      axios.post(path, readTagArray, { headers: { 'Content-Type': 'application/json' }})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              if (result.length > 0) {
                for (var k = 0; k < result.length; k++) {
                  if (result[i].tag_key === 'PmAis/AisStatus/ApplyUploadRecipe') {
                    this.applyObj.uploadValue = result[i].tag_value
                  }
                  if (result[i].tag_key === 'PmAis/AisStatus/ApplyDownLoadRecipe') {
                    this.applyObj.downloadValue = result[i].tag_value
                  }
                  if (result[i].tag_key === 'PmAis/AisStatus/ApplyDistributeRecipe') {
                    this.applyObj.downValue = result[i].tag_value
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          console.log(ex)
        })
    },
    // 接收到消息后进行界面刷新
    mqttUpdateTable(channel, message) {
      if (channel.indexOf('SCADA_CHANGE/') >= 0) {
        this.reflashTagInfo(channel, message)
      }
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }

      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }

        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },

    // 取消订阅主题函数
    topicUnsubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }

      // 订阅主题
      this.clientMqtt.unsubscribe(topic, (error) => {
        if (!error) {
          console.log('MQTT取消订阅成功:' + topic)
        } else {
          console.log('MQTT取消订阅失败:' + topic)
        }
      })
    },
    reflashTagInfo(channel, message) {
      var jsonData = JSON.parse(message)
      if (jsonData == null) return
      var TagKey = jsonData.TagKey
      var TagNewValue = jsonData.TagNewValue
      if (TagKey === 'PmAis/AisStatus/ApplyUploadRecipe') {
        this.applyObj.uploadValue = TagNewValue
      }
      if (TagKey === 'PmAis/AisStatus/ApplyDownLoadRecipe') {
        this.applyObj.downLoadValue = TagNewValue
      }
      if (TagKey === 'PmAis/AisStatus/ApplyDistributeRecipe') {
        this.applyObj.downValue = TagNewValue
      }
    },
    // 复制配方
    copyRecipe() {
      if (this.crud.selections.length !== 1) {
        this.$message({
          message: '请选择一条配方记录进行复制',
          type: 'warning'
        })
        return
      }

      const selectedRecipe = this.crud.selections[0]
      this.performCopy(selectedRecipe)
    },
    // 处理单行复制
    handleCopyRecipe(row) {
      this.performCopy(row)
    },
    // 执行复制操作
    performCopy(selectedRecipe) {
      const currentDateTime = new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      }).replace(/\//g, '-')

      // 复制配方数据，除类型外的非空字段都添加日期时间
      const copiedData = {
        recipe_type: selectedRecipe.recipe_type, // 类型不修改
        recipe_name: selectedRecipe.recipe_name ? `${selectedRecipe.recipe_name}_复制_${currentDateTime}` : '',
        recipe_des: selectedRecipe.recipe_des ? `${selectedRecipe.recipe_des}_复制_${currentDateTime}` : '',
        recipe_version: selectedRecipe.recipe_version ? `${selectedRecipe.recipe_version}_复制_${currentDateTime}` : '',
        material_code: selectedRecipe.material_code || '',
        material_des: selectedRecipe.material_des || '',
        device_code: selectedRecipe.device_code || '',
        device_des: selectedRecipe.device_des || '',
        enable_flag: selectedRecipe.enable_flag || '',
        copy_id: selectedRecipe.recipe_id
      }

      // 先打开新增弹窗
      this.crud.toAdd()

      // 然后填充表单数据
      Object.keys(copiedData).forEach(key => {
        if (copiedData[key] !== undefined && copiedData[key] !== null) {
          this.crud.form[key] = copiedData[key]
        }
      })

      // 最后设置复制信息（必须在crud.toAdd()之后，因为afterToCU钩子会清除copyInfo）
      this.copyInfo = {
        show: true,
        originalName: selectedRecipe.recipe_name,
        originalId: selectedRecipe.recipe_id,
        originalVersion: selectedRecipe.recipe_version
      }
    },
    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.old_recipe_name = crud.form.recipe_name
      crud.form.old_recipe_version = crud.form.recipe_version
      crud.form.station_code = this.$route.query.station_code

      // 如果是新增操作且不是复制操作，确保表单字段有正确的初始值
      if (crud.status.add === 1 && !this.copyInfo.show) {
        // 确保所有字段都有值，避免显示为空
        crud.form.recipe_id = crud.form.recipe_id || ''
        crud.form.recipe_name = crud.form.recipe_name || ''
        crud.form.recipe_version = crud.form.recipe_version || ''
        crud.form.device_code = crud.form.device_code || ''
        crud.form.device_des = crud.form.device_des || ''
        crud.form.material_code = crud.form.material_code || ''
        crud.form.material_des = crud.form.material_des || ''
        crud.form.recipe_type = crud.form.recipe_type || ''
        crud.form.enable_flag = crud.form.enable_flag || 'Y'
        crud.form.recipeFileNames = crud.form.recipeFileNames || ''
        // 清除copy_id参数
        crud.form.copy_id = ''
      }

      return true
    },
    // 新增/编辑弹窗关闭后
    [CRUD.HOOK.afterToCU](crud) {
      // 清除复制信息
      this.copyInfo = {
        show: false,
        originalName: '',
        originalId: '',
        originalVersion: ''
      }
      // 清除copy_id参数
      crud.form.copy_id = ''
    },
    // 保存成功后
    [CRUD.HOOK.afterSubmit](crud) {
      // 清除复制信息
      this.copyInfo = {
        show: false,
        originalName: '',
        originalId: '',
        originalVersion: ''
      }
      // 清除copy_id参数
      crud.form.copy_id = ''
      /**
      // 如果是复制操作保存成功，传递copy_id给新增配方按钮
      if (this.copyInfo.show && crud.form.copy_id) {
        // 这里可以根据需要处理copy_id的传递逻辑
        console.log('复制保存成功，copy_id:', crud.form.copy_id)
        // 可以通过事件或其他方式通知新增配方按钮
        this.$emit('copy-saved', {
          copy_id: crud.form.copy_id,
          original_name: this.copyInfo.originalName,
          original_id: this.copyInfo.originalId
        })
      }
      **/
    },
    // 获取物料信息
    async getMaterialInfo() {
      if (!this.form.material_code) {
        this.$message({
          type: 'warning',
          message: '请先输入物料编码'
        })
        return
      }

      this.materialInfoLoading = true
      try {
        const request = { 'PartNo': this.form.material_code }

        const res = await getMaterialInfo(request)

        if (res.code === 0 && res.data && res.data.length > 0) {
          const materialInfo = res.data[0]
          // 填充到对应的输入框
          this.form.device_code = materialInfo.Thickness || '' // 板厚
          this.form.device_des = materialInfo.Aperture || '' // 孔径
          this.form.material_des = materialInfo.Holes || '' // 孔数

          this.$message({
            type: 'success',
            message: '物料信息获取成功'
          })
        } else {
          this.$message({
            type: 'error',
            message: res.msg || '未找到物料信息或接口返回异常'
          })
        }
      } catch (error) {
        console.error('获取物料信息失败:', error)
        this.$message({
          type: 'error',
          message: '获取物料信息失败，请检查网络连接'
        })
      } finally {
        this.materialInfoLoading = false
      }
    },
    // 导出配方
    exportRecipe() {
      if (this.crud.selections.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要导出的配方'
        })
        return
      }

      if (this.crud.selections.length > 1) {
        this.$message({
          type: 'warning',
          message: '请选择一条配方记录进行导出'
        })
        return
      }

      const selectedRecipe = this.crud.selections[0]
      const exportData = {
        recipe_id: selectedRecipe.recipe_id,
        recipe_name: selectedRecipe.recipe_name
      }

      EapRecipeExport(exportData).then(response => {
        const blob = new Blob([response], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        const now = new Date()
        const timestamp = now.getFullYear().toString() +
                         (now.getMonth() + 1).toString().padStart(2, '0') +
                         now.getDate().toString().padStart(2, '0') +
                         now.getHours().toString().padStart(2, '0') +
                         now.getMinutes().toString().padStart(2, '0') +
                         now.getSeconds().toString().padStart(2, '0')
        link.download = `export_${selectedRecipe.recipe_name}_${selectedRecipe.recipe_version}_${timestamp}.xls`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message({
          type: 'success',
          message: '配方导出成功'
        })
      }).catch(error => {
        console.error('导出配方失败:', error)
        this.$message({
          type: 'error',
          message: '导出配方失败，请重试'
        })
      })
    },
    // 下载模板
    exportTemplate() {
      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在生成模板...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 调用下载模板API
      axios({
        method: 'get',
        url: '/aisEsbWeb/eap/project/hsql/index/EapRecipeExportTemplate',
        responseType: 'blob'
      }).then(response => {
        loading.close()

        const blob = new Blob([response.data], { type: 'application/vnd.ms-excel' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        const now = new Date()
        const timestamp = now.getFullYear().toString() +
                         (now.getMonth() + 1).toString().padStart(2, '0') +
                         now.getDate().toString().padStart(2, '0') +
                         now.getHours().toString().padStart(2, '0') +
                         now.getMinutes().toString().padStart(2, '0') +
                         now.getSeconds().toString().padStart(2, '0')
        link.download = `recipe_template_${timestamp}.xls`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message({
          type: 'success',
          message: '模板下载成功'
        })
      }).catch(error => {
        loading.close()
        console.error('下载模板失败:', error)
        this.$message({
          type: 'error',
          message: '下载模板失败，请重试'
        })
      })
    },
    // 处理文件选择
    handleFileChange(file, fileList) {
      if (!file.raw) {
        return
      }

      // 检查文件类型
      if (!file.name.endsWith('.xls')) {
        this.$message({
          type: 'error',
          message: '仅支持 .xls 文件格式'
        })
        return
      }

      // 创建FormData对象
      const formData = new FormData()
      formData.append('file', file.raw)
      formData.append('userName', Cookies.get('userName'))

      // 显示加载提示
      const loading = this.$loading({
        lock: true,
        text: '正在导入配方...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 调用导入API
      EapRecipeImport(formData).then(response => {
        loading.close()

        // 处理blob响应
        const reader = new FileReader()
        reader.onload = () => {
          try {
            const result = JSON.parse(reader.result)
            if (result.result === '配方導入成功' || result.code === 0) {
              this.$message({
                type: 'success',
                message: result.result || result.msg || '配方导入成功'
              })
              // 刷新列表
              this.crud.toQuery()
            } else {
              // 使用MessageBox显示错误信息，不自动关闭
              this.$alert(result.result || result.msg || '配方导入失败', '导入失败', {
                confirmButtonText: '确定',
                type: 'error',
                dangerouslyUseHTMLString: true,
                customClass: 'wide-alert-dialog'
              })
            }
          } catch (e) {
            this.$alert('导入响应解析失败', '解析错误', {
              confirmButtonText: '确定',
              type: 'error',
              customClass: 'wide-alert-dialog'
            })
          }
        }
        reader.readAsText(response)
      }).catch(error => {
        loading.close()
        console.error('导入配方失败:', error)
        this.$alert('导入配方失败，请检查文件格式和内容', '导入错误', {
          confirmButtonText: '确定',
          type: 'error',
          customClass: 'wide-alert-dialog'
        })
      })

      // 清空文件选择
      this.$refs.upload.clearFiles()
    }
  }
}
</script>
  <style lang="less" scoped>
  .wrapRowItem {
    display: flex;
    justify-content: space-between;
    .el-table {
      border-radius: 10px;
      border-color: rgba(0, 0, 0, 0.09);
      box-shadow: 0 2px 8px rgb(0 0 0 / 9%);
    }
    .el-row {
      width: 50%;
    }
  }
  .recipeInfo {
    width: 100%;
    display: flex;
    align-items: center;;
    justify-content: space-between;
  }
  .el-dialog__headerbtn {
  display: none;
}
  </style>

<style>
.wide-alert-dialog {
  width: 600px !important;
  max-width: 80vw !important;
}
.wide-alert-dialog .el-message-box {
  max-height: 80vh !important;
}
.wide-alert-dialog .el-message-box__message {
  white-space: pre-line !important;
  word-break: break-word !important;
  line-height: 1.6 !important;
  max-height: 60vh !important;
  overflow-y: auto !important;
  padding-right: 10px !important;
}
</style>

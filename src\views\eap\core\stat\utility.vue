<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-3 col-12">
              <el-form-item class="marginL" :label="$t('view.form.station') + ': '" label-width="120px">
                <el-select
                  v-model="query.station_id"
                  filterable
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.timePeriod') + ': '" label-width="120px">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    :start-placeholder="$t('view.form.timePeriodStart')"
                    :end-placeholder="$t('view.form.timePeriodEnd')"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    style="width:100%"
                  />
                </div>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.shiftCode') + ': '" label-width="120px">
                <el-select
                  v-model="query.shift_code"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['A','B']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-3 col-12">
              <el-form-item :label="$t('view.form.uploadFlag') + ': '" label-width="120px">
                <el-select
                  v-model="query.up_flag"
                  clearable
                  :placeholder="$t('view.enum.placeholder.pleaseChosen')"
                >
                  <el-option v-for="item in ['N','Y']" :key="item" :label="item" :value="item" />
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('view.button.search') }}</el-button>  <!-- 搜索 -->
                <el-button v-if="crud.optShow.reset" class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('view.button.reset') }}</el-button>  <!-- 重置 -->
              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>
    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
            <el-table-column :show-overflow-tooltip="true" prop="shift_des" width="130" :label="$t('view.table.shift')" />
            <el-table-column :show-overflow-tooltip="true" prop="shift_start_date" width="130" :label="$t('view.table.shiftStart')" />
            <el-table-column :show-overflow-tooltip="true" prop="shift_end_date" width="130" :label="$t('view.table.shiftEnd')" />
            <el-table-column :show-overflow-tooltip="true" prop="up_flag" width="100" :label="$t('view.table.uploadFlag')">
              <template slot-scope="scope">
                <el-tag :type="((scope.row.up_flag == 'Y') ? 'success' : '')">
                  {{ scope.row.up_flag }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column :show-overflow-tooltip="true" prop="up_count" width="130" :label="$t('view.table.uploadTimes')" />
            <el-table-column :show-overflow-tooltip="true" prop="up_msg" width="150" :label="$t('view.table.uploadMessage')" />
            <el-table-column :show-overflow-tooltip="true" prop="fail_board_count" width="150" :label="$t('view.table.dropBoardQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="total_count" width="120" :label="$t('view.table.totalQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="fail_percent" width="120" :label="$t('view.table.dropBoardRate')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_ok_count" width="150" :label="$t('view.table.boardOkQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_count" width="150" :label="$t('view.table.boardNgQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="panel_ng_percent" width="150" :label="$t('view.table.boardFailureRate')" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_ok_count" width="150" :label="$t('view.table.carrierOkQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_ng_count" width="150" :label="$t('view.table.carrierNgQuantity')" />
            <el-table-column :show-overflow-tooltip="true" prop="pallet_ng_percent" width="150" :label="$t('view.table.carrierFailureRate')" />
            <el-table-column :show-overflow-tooltip="true" prop="work_cycle_time" width="150" :label="$t('view.table.workingTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="shift_cycle_time" width="150" :label="$t('view.table.shiftTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="oee_percent" width="150" :label="$t('view.table.oee')" />
          </el-table>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <div style="margin-top: 5px;text-align:right;">
            <el-button-group>
              <el-button
                type="primary"
              >
                {{ $t('view.pagination.total') }}: {{ page.total }}</el-button>
              <el-button
                type="primary"
              >
                {{ $t('view.pagination.current') }}{{ nowPageIndex }}{{ $t('view.pagination.unit') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('pre')"
              >
                &lt;&nbsp;{{ $t('view.pagination.previous') }}</el-button>
              <el-button
                type="primary"
                @click="pageQuery('next')"
              >
                {{ $t('view.pagination.next') }}&nbsp;&gt;</el-button>
            </el-button-group>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud, pagination } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import { sel as selStation } from '@/api/core/factory/sysStation'
import crudEapStatUtility from '@/api/eap/eapStatUtility'
const defaultForm = {

}
export default {
  name: 'EapStatUtility',
  components: { crudOperation },
  cruds() {
    return CRUD({
      title: this.parent.$t('view.title.threeRateReport'),
      // 登录用户
      userName: Cookies.get('userName'),
      // // 唯一字段
      idField: 'id',
      // // 排序
      sort: ['id desc'],
      // CRUD Method
      crudMethod: { ...crudEapStatUtility },
      // 按钮显示
      optShow: {
        reset: true,
        buttonGroup: false
      },
      query: {
        tableSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud(), pagination()],
  // 数据模型
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      stationData: [],
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'eap_stat_utility:add'],
        edit: ['admin', 'eap_stat_utility:edit'],
        del: ['admin', 'eap_stat_utility:del'],
        down: ['admin', 'eap_stat_utility:down']
      }
    }
  },
  created() {
    this.getStationList()
  },
  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },
  methods: {
    toQuery() {
      if (!this.query.station_id || this.query.station_id === '') {
        this.$message({ message: this.$t('view.dialog.selectStation'), type: 'info' })
        return
      }
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: this.$t('view.dialog.top'),
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: this.$t('view.dialog.bottom'),
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    },
    getStationList() {
      selStation({
        userID: Cookies.get('userName'),
        enable_flag: 'Y'
      })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('view.dialog.queryException'),
            type: 'error'
          })
        })
    }
  }
}
</script>
<style lang="less" scoped>
::v-deep .el-button--primary {
  color: #fff;
  background-color: #1473c5;
  border-color: #1473c5;
}
::v-deep .el-button--primary:hover {
  background: #438fd1;
  border-color: #438fd1;
  color: #fff;
}
.labelIline {
  display: flex;
  align-items: center;
  ::v-deep .el-form-item__label {
    white-space: nowrap;
  }
}
::v-deep .wrapElFormFirst {
  .el-form-item__label {
    white-space: nowrap;
    margin: 0;
    width: 80px;
  }
}
::v-deep .el-date-editor--datetimerange.el-input,
.el-date-editor--datetimerange.el-input__inner {
  width: 235px;
}
::v-deep .el-date-editor .el-range-separator {
  width: 12%;
}
::v-deep .el-descriptions-item__label.is-bordered-label {
  width: 130px;
}
</style>

<template>
  <!--模组子维护-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="450px">
      <el-form ref="form" class="el-form-wrap form-colum" :model="form" :rules="rules" size="small" label-width="100px" :inline="true">
        <el-form-item :label="$t('lang_pack.wx.parameterCode')" prop="parameter_code">
          <el-input v-model="form.parameter_code" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.wx.parameterDesc')" prop="parameter_des">
          <el-input v-model="form.parameter_des" />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.wx.parameterValue')" prop="parameter_val">
          <el-input v-model="form.parameter_val" />
        </el-form-item>
        <!-- 新增的表单项应该放在这里 -->
        <el-form-item :label="$t('core.recipe.unit')" prop="unit">
          <el-input v-model="form.unit" readonly />
        </el-form-item>
        <el-form-item :label="$t('core.recipe.minValue')" prop="down_limit">
          <el-input v-model="form.down_limit" readonly>
            <template slot="append">{{ form.down_limit === -1 ? $t('core.recipe.noLimit') : '' }}</template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('core.recipe.maxValue')" prop="upper_limit">
          <el-input v-model="form.upper_limit" readonly>
            <template slot="append">{{ form.upper_limit === -1 ? $t('core.recipe.noLimit') : '' }}</template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('core.recipe.currentValue')" prop="current_value">
          <el-input v-model="form.current_value" readonly />
        </el-form-item>
        <el-form-item :label="$t('lang_pack.mainmain.triggerPoint')" prop="tag_id">
          <!-- 触发点位 -->
          <el-input v-model="form.tag_id" readonly="readonly">
            <template slot="append">
              <el-popover v-model="customPopover" placement="left" width="650">
                <tagSelect ref="tagSelect" :client-id-list="form.client_id_list" :tag-id="form.tag_id" @chooseTag="handleChooseTag" />
                <el-button slot="reference">{{ $t('lang_pack.vie.select') }}</el-button>
              </el-popover>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item :label="$t('lang_pack.commonPage.validIdentification')" prop="enable_flag">
          <el-select v-model="form.enable_flag" clearable>
            <el-option v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-divider />
      <div style="text-align: center">
        <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.wx.cancel') }}</el-button>
        <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('view.button.confirm') }}</el-button>
      </div>
    </el-drawer>
    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%"
          :cell-style="crud.cellStyle"
          height="478"
          max-height="478"
          highlight-current-row
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
        >
          <el-table-column type="selection" width="45" align="center" />
          <el-table-column v-if="1 == 0" width="10" prop="recipe_detail_id" label="id" />
          <el-table-column :show-overflow-tooltip="true" align="left" prop="parameter_des" :label="$t('lang_pack.wx.parameterDesc')">
            <template slot-scope="scope">
              <span>{{ scope.row.parameter_code + '-' + scope.row.parameter_des }}</span>
            </template>
          </el-table-column>
          <el-table-column :show-overflow-tooltip="true" align="center" prop="parameter_val" :label="$t('lang_pack.wx.parameterValue')">
            <template slot-scope="scope">
              <el-input 
                v-model="scope.row.parameter_val" 
                :disabled="!disabled || isSubmitting" 
                @blur="handleBlur(scope.row)"
                @input="validateInput(scope.row)"
                :class="{'input-error': scope.row.validationError}"
              />
              <div v-if="scope.row.validationError" class="error-message">{{ scope.row.validationError }}</div>
            </template>
          </el-table-column>
          <!-- 新增单位列 -->
          <el-table-column :show-overflow-tooltip="true" align="center" prop="unit" :label="$t('core.recipe.unit')" width="80">
            <template slot-scope="scope">
              <span>{{ scope.row.unit || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 新增最小值列 -->
          <el-table-column :show-overflow-tooltip="true" align="center" prop="down_limit" :label="$t('core.recipe.minValue')" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.down_limit === -1 ? $t('core.recipe.noLimit') : scope.row.down_limit }}</span>
            </template>
          </el-table-column>
          <!-- 新增最大值列 -->
          <el-table-column :show-overflow-tooltip="true" align="center" prop="upper_limit" :label="$t('core.recipe.maxValue')" width="100">
            <template slot-scope="scope">
              <span>{{ scope.row.upper_limit === -1 ? $t('core.recipe.noLimit') : scope.row.upper_limit }}</span>
            </template>
          </el-table-column>
          <!-- 新增当前值列 -->
          <el-table-column :show-overflow-tooltip="true" align="center" prop="current_value" :label="$t('core.recipe.currentValue')" width="120">
            <template slot-scope="scope">
              <span>{{ scope.row.current_value || '-' }}</span>
            </template>
          </el-table-column>
          <!-- 注释掉有效标识列
          <el-table-column :label="$t('lang_pack.commonPage.validIdentification')" align="center" prop="enable_flag" width="100">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.enable_flag"
                active-color="#13ce66"
                inactive-color="#ff4949"
                active-value="Y"
                inactive-value="N"
                @change="changeEnabled(scope.row, scope.row.enable_flag)"
              />
            </template>
          </el-table-column>
          -->
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>
<script>
import eapRecipeCommonDetail from '@/api/eap/core/eapRecipeCommonDetail'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  recipe_detail_id: '',
  parameter_code: '',
  parameter_des: '',
  parameter_val: '',
  enable_flag: 'Y',
  client_id_list: '',
  tag_id: '',
  unit: '', // 新增单位字段
  down_limit: -1, // 新增最小值字段
  upper_limit: -1, // 新增最大值字段
  current_value: '' // 新增当前值字段
}
export default {
  name: 'EAP_RECIPE_DETAIL',
  components: { crudOperation, rrOperation, udOperation, pagination },
  props: {
    recipe_detail_id: {
      type: [String, Number],
      default: -1
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.wx.subDetail'),
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_detail_id',
      // 排序
      sort: ['recipe_detail_id asc'],
      // CRUD Method
      crudMethod: { ...eapRecipeCommonDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      props: {
        pageSize: 50
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 200,
      permission: {
        add: ['admin', 'eap_recipe_common_d:add'],
        edit: ['admin', 'eap_recipe_common_d:edit'],
        del: ['admin', 'eap_recipe_common_d:del'],
        down: ['admin', 'eap_recipe_common_d:down']
      },
      rules: {
        tag_id: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        enable_flag: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        parameter_code: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        parameter_des: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }],
        parameter_val: [{ required: true, message: this.$t('lang_pack.commonPage.required'), trigger: 'blur' }]
      },
      customPopover: false,
      isSubmitting: false // 防止重复提交
    }
  },
  watch: {
    recipe_detail_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.recipe_id = this.recipe_detail_id
        this.crud.toQuery()
      }
    },
    disabled: {
      immediate: true,
      deep: true,
      handler(newValue) {
        this.disabled = newValue
      }
    }
  },
  mounted() {
    const that = this
    window.onresize = () => {
      that.height = document.documentElement.clientHeight - 200
    }
  },
  created() {},
  methods: {
    handleChooseTag(tagId) {
      this.form.tag_id = tagId
      this.customPopover = false
    },
    // 校验输入值是否为有效数字
    isValidNumber(value) {
      if (value === null || value === undefined || value === '') return true // 允许空值
      const num = parseFloat(value)
      return !isNaN(num)
    },
    // 校验输入值是否在范围内
    isWithinRange(value, down_limit, upper_limit) {
      const num = parseFloat(value)
      if (down_limit !== -1 && num < down_limit) {
        return { valid: false, message: `值不能小于 ${down_limit}` }
      }
      if (upper_limit !== -1 && num > upper_limit) {
        return { valid: false, message: `值不能大于 ${upper_limit}` }
      }
      return { valid: true }
    },
    // 优化后的输入校验方法
    validateInput(row) {
      // 清除之前的错误信息
      this.$set(row, 'validationError', '')

      // 如果输入值为空，跳过校验
      if (row.parameter_val === null || row.parameter_val === undefined || row.parameter_val === '') {
        return true
      }

      // 检查是否需要进行数字校验（只有设置了范围限制才需要）
      const hasRangeLimit = (row.down_limit !== null && row.down_limit !== undefined && row.down_limit !== -1) || 
                           (row.upper_limit !== null && row.upper_limit !== undefined && row.upper_limit !== -1)
      
      // 只有在有范围限制时才进行数字校验
      if (hasRangeLimit) {
        // 校验是否为有效数字
        if (!this.isValidNumber(row.parameter_val)) {
          this.$set(row, 'validationError', '请输入有效的数字')
          return false
        }

        // 校验范围
        const rangeCheck = this.isWithinRange(row.parameter_val, row.down_limit, row.upper_limit)
        if (!rangeCheck.valid) {
          this.$set(row, 'validationError', rangeCheck.message)
          return false
        }
      }

      return true
    },
    // 优化后的 handleBlur 方法
    handleBlur(row) {
      if (this.isSubmitting) {
        console.log('提交中，忽略重复 blur 事件')
        return
      }

      // 如果输入值与当前值相同，跳过保存
      if (row.parameter_val === row.current_value) {
        console.log('输入值未变更，跳过保存')
        return
      }

      // 先进行校验
      if (!this.validateInput(row)) {
        this.$message({ type: 'warning', message: row.validationError || '输入值不符合要求，请检查后重新输入' })
        row.parameter_val = row.current_value || '' // 校验失败时恢复原值
        return
      }

      this.isSubmitting = true
      eapRecipeCommonDetail.edit(row).then(res => {
        if (res.code === 0) {
          this.$set(row, 'current_value', row.parameter_val)
          this.$message({ type: 'success', message: '修改成功' })
        } else {
          let errorMsg = res.msg || '未知错误'
          if (errorMsg.includes('duplicate key')) {
            errorMsg = '配方详情ID已存在，请联系管理员'
          }
          this.$message({ type: 'error', message: `修改失败：${errorMsg}` })
          row.parameter_val = row.current_value || '' // 后端错误时恢复原值
        }
      }).catch(ex => {
        let errorMsg = ex.message || '网络错误'
        if (errorMsg.includes('duplicate key')) {
          errorMsg = '配方详情ID冲突，请联系管理员'
        }
        this.$message({ type: 'error', message: `修改失败：${errorMsg}` })
        row.parameter_val = row.current_value || '' // 网络错误时恢复原值
      }).finally(() => {
        this.isSubmitting = false
      })
    },
    changeEnabled(data, val) {
      this.$confirm(
        this.$t('lang_pack.vie.changeTo') + '【' + (val === 'Y' ? this.$t('lang_pack.vie.effective') : this.$t('lang_pack.vie.invalid')) + '】' + this.$t('lang_pack.vie.what'),
        this.$t('lang_pack.vie.prompt'),
        {
          confirmButtonText: this.$t('lang_pack.vie.determine'),
          cancelButtonText: this.$t('lang_pack.vie.cancel'),
          type: 'warning'
        }
      ).then(() => {
        eapRecipeCommonDetail.editEnableFlag({
          user_name: Cookies.get('userName'),
          recipe_detail_id: data.recipe_detail_id,
          enable_flag: val
        }).then(res => {
          if (res.code === 0) {
            this.$message({ message: this.$t('lang_pack.vie.editSuccess'), type: 'success' })
          } else {
            this.$message({ message: this.$t('lang_pack.vie.operationException') + res.msg, type: 'error' })
            data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
          }
        }).catch(ex => {
          this.$message({ message: this.$t('lang_pack.commonPage.operationException') + ex, type: 'error' })
          data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
        })
      }).catch(() => {
        data.enable_flag = data.enable_flag === 'Y' ? 'N' : 'Y'
      })
    },
    [CRUD.HOOK.beforeRefresh](crud) {
      crud.query.recipe_id = this.recipe_detail_id
    },
    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.recipe_id = this.recipe_detail_id
      return true
    },
    [CRUD.HOOK.afterRefresh](crud) {
      crud.data.forEach(row => {
        if (!row.current_value) {
          this.$set(row, 'current_value', row.parameter_val)
        }
        this.$set(row, 'validationError', '')
      })
    }
  }
}
</script>
<style lang="less" scoped>
.form-colum{
  flex-direction: column;
  .el-form-item{
    width: 100%;
  }
}

.input-error {
  /deep/ .el-input__inner {
    border-color: #f56c6c;
  }
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 2px;
}
</style>

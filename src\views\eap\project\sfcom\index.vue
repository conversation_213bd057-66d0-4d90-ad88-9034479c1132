<!--晟丰-公共WEB操作页面-本地配方管理-->
<template>
  <div class="app-container">
    <el-card shadow="never" class="wrapCard header">
      <el-header>
        <div class="statuHead">
          <div>
            <el-popover placement="right" width="170" trigger="click">
              <el-button
                v-for="(item, index) in [
                  {
                    tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                    tag_value: '0',
                    label: '离线模式',
                  },
                  {
                    tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                    tag_value: '2',
                    label: '在线/本地',
                  },
                  {
                    tag_key: `${stationAttr}Plc/PcStatus/Remote`,
                    tag_value: '1',
                    label: '在线/远程',
                  },
                ]"
                :key="index"
                size="medium"
                type="primary"
                style="font-size: 20px"
                :style="{ margin: index === 1 ? '10px 0' : '0px' }"
                @click="handleModeSwitch(item.tag_value)"
              >{{ item.label }}</el-button><br>
              <el-button
                slot="reference"
                :class="
                  monitorData.ControlMode.value === '2' ||
                    monitorData.ControlMode.value === '3'
                    ? 'btnone'
                    : 'btnone0'
                "
              >{{
                controlData[monitorData.ControlMode.value] || "离线模式"
              }}</el-button>
            </el-popover>
            <el-button
              :class="getDeviceStatusClass(monitorData.DeviceStatus && monitorData.DeviceStatus.value)"
              style="min-width: 150px;"
            >{{
              (monitorData.DeviceStatus && monitorData.DeviceStatus.value !== undefined && monitorData.DeviceStatus.value !== null)
                ? "设备状态：" + (deviceStatusData[monitorData.DeviceStatus.value] || "未知")
                : "设备状态：未知"
            }}</el-button>
          </div>
          <div>
            <div class="wrappstyle">
              <p>
                <span
                  :class="
                    monitorData.LightGreen.value === '1'
                      ? 'wholeline1 wholelinenormal1'
                      : monitorData.LightYellow.value === '1'
                        ? 'wholeline1 wholelineerror1'
                        : monitorData.LightRed.value === '1'
                          ? 'wholeline1 deviceRed'
                          : 'wholeline1 wholelinegray1'
                  "
                />
                <span class="statuText">告警灯</span>
              </p>
              <p>
                <span
                  :class="
                    monitorData.PlcHeartBeat.value === '1'
                      ? 'wholeline wholelinenormal'
                      : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">PLC心跳</span>
              </p>
              <!-- <p>
                <span
                  :class="
                    controlStatus.eap_status === '1'
                      ? 'wholeline wholelinenormal'
                      : 'wholeline wholelinegray'
                  "
                />
                <span class="statuText">EAP心跳</span>
              </p> -->
            </div>
          </div>
        </div>
      </el-header>
    </el-card>
    <el-row
      :gutter="20"
      style="margin-right: 0px; padding: 0px; margin-top: 10px"
    >
      <el-col :span="16" style="padding-right: 0">
        <el-col :span="24" style="padding: 0">
          <!-- <el-col :span="12" style="padding: 0 5px 0 0;"> -->
          <el-card shadow="never" class="wrapCard">
            <el-form ref="query" :inline="true" size="small" label-width="80px">
              <div class="wrapElForm">
                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="批次号：">
                      <el-input
                        ref="lot_no"
                        v-model="lot_no"
                        clearable
                      />
                    </el-form-item>
                  </div>
                </div>

                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="料号：">
                      <el-input
                        ref="material_code"
                        v-model="material_code"
                        clearable
                      />
                    </el-form-item>
                  </div>
                </div>

                <div class="wrapElFormFirst col-md-6 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="配方：">
                      <div style="display: flex; align-items: center;">
                        <el-select
                          v-model="selectedRecipe"
                          filterable
                          clearable
                          placeholder="请选择配方"
                          style="width: 100%;"
                          @change="handleRecipeChange"
                        >
                          <el-option
                            v-for="item in recipeList"
                            :key="item.recipe_id"
                            :label="item.recipe_name"
                            :value="item.recipe_id"
                            :class="{ 'duplicate-recipe': item.isDuplicate }"
                          />
                        </el-select>
                        <el-button
                          type="primary"
                          icon="el-icon-refresh"
                          circle
                          size="mini"
                          style="margin-left: 5px;"
                          title="刷新配方列表"
                          @click="refreshRecipeList"
                        />
                      </div>
                    </el-form-item>
                  </div>
                </div>

                <div class="wrapElFormFirst col-md-3 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-form-item label="加工数量：">
                      <el-input
                        ref="lot_count"
                        v-model="lot_count"
                        clearable
                        type="number"
                        min="0"
                        @change="validateQuantity"
                      />
                    </el-form-item>
                  </div>
                </div>
                <div class="wrapElFormFirst col-md-3 col-12">
                  <div class="formChild col-md-12 col-12">
                    <el-button
                      type="primary"
                      @click="showRecipeConfirmDialog"
                    >下发预览</el-button>
                  </div>
                </div>
              </div>
            </el-form>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <div slot="header" class="wrapTextSelect">
              <span>生产信息</span>
            </div>
            <el-table ref="table" :data="[tableData]" border :height="height">
              <el-table-column
                v-for="(item,index) in groupData"
                :key="index"
                :show-overflow-tooltip="true"
                align="center"
                :width="item.tag_des && item.tag_des.length >= 5 ? '190' : ''"
                :prop="item.value"
                :label="item.tag_des || ''"
              >
                <template slot-scope="scope">
                  {{ item.value }} <!-- 显示对应value -->
                </template>
              </el-table-column></el-table>
          </el-card>
        </el-col>
        <el-col :span="24" style="margin-top: 10px; padding: 0">
          <el-card shadow="never" class="wrapCard">
            <el-card shadow="never" class="wrapCard">
              <div slot="header" class="wrapTextSelect">
                <span>报警信息</span>
              </div>
              <el-table
                ref="table"
                border
                :data="alarmData"
                :row-key="(row) => row.id"
                height="200"
              >
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="station_code"
                  label="工位"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_code"
                  label="实例编号"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="client_des"
                  label="实例描述"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_code"
                  label="报警代码"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_level"
                  label="报警级别"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="alarm_des"
                  label="报警描述"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="item_date"
                  label="报警时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_date"
                  label="复位时间"
                />
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="reset_flag"
                  label="是否复位"
                >
                  <template slot-scope="scope">
                    {{ scope.row.reset_flag === "Y" ? "已复位" : "待复位" }}
                  </template>
                </el-table-column>
                <el-table-column
                  :show-overflow-tooltip="true"
                  prop="simulated_flag"
                  label="是否模拟"
                >
                  <template slot-scope="scope">
                    {{ scope.row.simulated_flag === "Y" ? "是" : "否" }}
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </el-card></el-col>
      </el-col>
      <el-col :span="8" style="padding-right: 0">
        <el-card shadow="never" class="wrapCard">
          <div class="pieChart">
            <div id="capacityDom" />
            <!-- 产能 -->
            <div id="oeeDom" />
            <!-- oee -->
            <div id="readbitRateDom" />
            <!-- 读码率 -->
          </div>
          <el-table
            ref="table"
            border
            :data="plcCraftData"
            :row-key="(row) => row.id"
            :height="gatherHeight"
          >
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              label="项目名称"
              prop="tag_des"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="tag_value"
              label="当前值"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="unit"
              label="单位"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="upper_limit"
              label="上限"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="down_limit"
              label="下限"
            />
            <el-table-column
              :show-overflow-tooltip="true"
              align="center"
              prop="status"
              label="状态"
            />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
    <el-dialog
      :show-close="true"
      title="修改配方"
      width="80%"
      :visible.sync="dialogVisible"
      class="dialogTable"
    >
      <div
        style="
          margin-bottom: 10px;
          display: flex;
          justify-content: right;
          align-items: center;
        "
      >
        <span>是否修改参数：</span>
        <el-switch
          v-model="disabled"
          active-color="#13ce66"
          inactive-color="#ff4949"
        />
      </div>
      <el-table
        ref="table"
        v-loading="crud.loading"
        border
        size="small"
        :data="crud.data"
        style="width: 100%"
        :cell-style="crud.cellStyle"
        height="478"
        max-height="478"
        highlight-current-row
        @header-dragend="crud.tableHeaderDragend()"
        @selection-change="crud.selectionChangeHandler"
      >
        <el-table-column type="selection" width="45" align="center" />
        <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
        <el-table-column
          v-if="1 == 0"
          width="10"
          prop="recipe_detail_id"
          label="id"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          prop="parameter_code"
          label="参数编码"
          width="150"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_des"
          label="参数描述"
        />
        <el-table-column
          :show-overflow-tooltip="true"
          align="center"
          prop="parameter_val"
          label="参数值"
        >
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.parameter_val"
              :disabled="handleDisabled(scope.row.parameter_val)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="有效标识"
          align="center"
          prop="enable_flag"
          width="100"
        >
          <template slot-scope="scope">
            <!--取到当前单元格-->
            {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
          </template>
        </el-table-column>
      </el-table>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleOk">确定下发</el-button>
      </span>
    </el-dialog>
    <el-dialog title="CIM消息" width="50%" top="20px" :visible.sync="dialogCIMMsgVisible" :close-on-click-modal="false" class="elDialog">
      <table class="table">
        <tr>
          <td class="label" style="width:100px;">代码：</td>
          <td class="content">{{ screen_code }}</td>
        </tr>
        <tr>
          <td class="label">消息：</td>
          <td class="content">{{ cim_msg }}</td>
        </tr>
        <tr v-if="ConfirmBtnVisible">
          <td colspan="2" style="text-align:center;">
            <el-button class="filter-item" size="mini" type="primary" icon="el-icon-check" @click="handleConfirmCIMMsg">确认</el-button>
          </td>
        </tr>
      </table>
    </el-dialog>

    <!-- 配方下发确认对话框 -->
    <el-dialog
      title="下发配方确认"
      :visible.sync="recipeConfirmVisible"
      width="50%"
      :fullscreen="false"
      :close-on-click-modal="false"
      custom-class="recipe-confirm-dialog"
      top="0"
    >
      <div style="margin-bottom: 15px; text-align: right;">
        <el-button @click="recipeConfirmVisible = false">取消</el-button>
        <el-button :disabled="!selectedRecipeDetail" type="primary" @click="downloadRecipe">确认下发</el-button>
      </div>

      <div v-loading="recipeDetailLoading" element-loading-text="正在加载配方详情...">
        <div v-if="selectedRecipeDetail">
          <h3 style="font-size: 20px; margin-bottom: 0px;">配方信息</h3>
          <el-descriptions :column="2" border class="recipe-info-descriptions">
            <el-descriptions-item label="配方名称" :labelStyle="{ fontSize: '18px' }" :contentStyle="{ fontSize: '18px' }" >{{ selectedRecipeDetail.recipe_name }}</el-descriptions-item>
            <el-descriptions-item label="工站编码" :labelStyle="{ fontSize: '18px' }" :contentStyle="{ fontSize: '18px' }" >{{ station_code }}</el-descriptions-item>
            <el-descriptions-item label="批次号"  :labelStyle="{ fontSize: '18px' }" :contentStyle="{ fontSize: '18px' }" >{{ lot_no }}</el-descriptions-item>
            <el-descriptions-item label="料号"  :labelStyle="{ fontSize: '18px' }" :contentStyle="{ fontSize: '18px' }" >{{ material_code }}</el-descriptions-item>
            <el-descriptions-item label="数量"  :labelStyle="{ fontSize: '18px' }" :contentStyle="{ fontSize: '18px' }" >{{ lot_count }}</el-descriptions-item>
            <el-descriptions-item label="员工信息"  :labelStyle="{ fontSize: '18px' }" :contentStyle="{ fontSize: '18px' }">{{ user.nickName }} ({{ user.username }})</el-descriptions-item>
          </el-descriptions>
          <h3 style="margin-top: 0px; font-size: 20px; margin-bottom: 0px;">配方详情</h3>
          <el-table
            :data="filteredRecipeDetails"
            border
            style="width: 100%; margin-top: 10px;"
            height="auto"
          >
            <el-table-column prop="parameter_code" label="参数编码" width="150" />
            <el-table-column prop="parameter_des" label="参数描述" width="220" />
            <el-table-column prop="parameter_val" label="参数值" />
            <el-table-column prop="enable_flag" label="有效标识" width="80">
              <template slot-scope="scope">
                {{ scope.row.enable_flag === "Y" ? "有效" : "无效" }}
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div v-else class="no-recipe-selected">
          <p>未选择配方或配方详情加载中...</p>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { selScadaTag } from '@/api/core/scada/tag'
import eapRecipe from '@/api/eap/project/sfcom/eapRecipe'
import eapRecipeDetail from '@/api/eap/project/sfcom/eapRecipeDetail'
import { eapCimMsgShow } from '@/api/eap/eapApsPlan'
import { scadaTagGroupTree } from '@/api/core/scada/tagGroup'
import { sel as selMaterial } from '@/api/eap/project/sfjd/eapMaterial'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import axios from 'axios'
import { selCellIP } from '@/api/core/center/cell'
import { sel as selStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import { mapGetters } from 'vuex'
const defaultForm = {}
export default {
  name: 'shRecipeMain',
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      height: document.documentElement.clientHeight - 608,
      gatherHeight: document.documentElement.clientHeight - 424,
      lot_no: '',
      material_code: '',
      lot_count: '0',
      current_user: '',
      recipeData: [],
      capacityDom: null,
      oeeDom: null,
      readbitRateDom: null,
      // 配方相关
      recipeList: [],
      selectedRecipe: null,
      selectedRecipeDetail: null,
      recipeConfirmVisible: false,
      recipeDetailLoading: false,
      // 控制配方参数配置不完整警告只显示一次
      recipeConfigWarningShown: false,
      // 标记配方参数配置是否不完整
      hasIncompleteRecipeConfig: false,
      // CIM消息相关
      queryCim: true,
      dialogCIMMsgVisible: false,
      screen_code: '',
      cim_msg: '',
      ConfirmBtnVisible: false,
      capacityOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: '产能',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#6df320', '#d2e312'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: '脱岗' },
              { value: 234, name: '在岗' }
            ]
          }
        ]
      },
      oeeOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: 'OEE',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#409EFF', '#40e2ff'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: '脱岗' },
              { value: 234, name: '在岗' }
            ]
          }
        ]
      },
      readbitRateOption: {
        title: {
          show: true,
          text: '100%',
          itemGap: 10,
          x: 'center',
          y: '30%',
          subtext: '读码率',
          textStyle: {
            fontSize: 24,
            color: '#999999'
          },
          subtextStyle: {
            fontSize: 24,
            fontWeight: 'bold',
            color: '#333333'
          }
        },
        color: ['#9d9727', '#c25b1f'],
        tooltip: {
          backgroundColor: '#fff',
          extraCssText: 'box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.15);',
          textStyle: {
            color: '#000'
          }
        },
        grid: {
          top: '0%',
          left: '0%',
          right: '0%',
          bottom: '0%',
          containLabel: true
        },
        series: [
          {
            type: 'pie',
            radius: ['60%', '90%'],
            center: ['50%', '40%'],
            label: {
              // 鼠标悬浮具体数据显示
              show: false
            },
            data: [
              { value: 335, name: '脱岗' },
              { value: 234, name: '在岗' }
            ]
          }
        ]
      },
      dialogVisible: false,
      rules: {
        material_code: [
          { required: true, message: '请输入物料编码', trigger: 'blur' }
        ],
        material_des: [
          { required: true, message: '请输入物料描述', trigger: 'blur' }
        ]
      },
      disabled: false,
      controlStatus: {
        light_status: '',
        device_plc_status: '',
        ais_status: '0',
        plc_status: '0',
        eap_status: '0'
      },
      controlData: {
        1: '离线模式',
        2: '在线/本地',
        3: '在线/远程'
      },
      deviceStatusData: {
        '0': '未知',
        '1': '初始化',
        '2': '待机',
        '3': '报警',
        '4': '停止',
        '5': '运行',
        '6': 'PM'
      },
      monitorData: {
        // 客户端心跳状态对象
        clientHeartbeats: {
          PlcPlc: { heartbeat: false, label: 'PLC心跳' }
          // EapPlc: { heartbeat: false, label: 'EAP心跳' }
        },

        PlcHeartBeat: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'PlcHeartBeat',
          tag_des: '[PlcStatus]PLC心跳',
          value: '0'
        },

        LightGreen: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightGreen',
          tag_des: '[PlcStatus]三色灯绿',
          value: '0'
        },
        LightYellow: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightYellow',
          tag_des: '[PlcStatus]三色灯黄',
          value: '0'
        },
        LightRed: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'LightRed',
          tag_des: '[PlcStatus]三色灯红',
          value: '0'
        },
        ControlMode: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'DeviceMode',
          tag_des: '[PlcStatus]设备控制状态(1:离线,2:在线/本地,3:在线/远程)',
          value: '0'
        },
        DeviceStatus: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'DeviceStatus',
          tag_des: '[PlcStatus]设备状态(1初始化 2待机 3报警 4停止 5运行)',
          value: '0'
        },
        RecipeUpd: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'RecipeUpd',
          tag_des: '[PlcStatus]配方修改',
          value: '0'
        },
        RecipeSelFinish: {
          client_code: `Plc`,
          group_code: 'PlcStatus',
          tag_code: 'RecipeSelFinish',
          tag_des: '[PlcStatus]配方选择完成',
          value: '0'
        }
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).slice(2, 10), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      alarmData: [],
      materialData: [],
      timer: null,
      groupData: [],
      productData: [],
      plcCraftData: [],
      stationAttr: '',
      client_id: '',
      station_code: '',
      tableData: {}
    }
  },
  cruds() {
    return CRUD({
      title: '配方维护',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'recipe_detail_id ',
      // 排序
      sort: ['recipe_detail_id asc'],
      // CRUD Method
      crudMethod: { ...eapRecipeDetail },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        down: false,
        reset: true
      }
    })
  },
  dicts: ['PROJECT_PARAMS_WARNING'],
  computed: {
    ...mapGetters(['user']),
    // 过滤配方详情，排除批次号、料号、加工数量和配方名称
    filteredRecipeDetails() {
      if (!this.selectedRecipeDetail || !this.selectedRecipeDetail.details) {
        return []
      }

      // 需要排除的参数编码
      const excludedCodes = ['LotID', 'PartNo', 'PlanCount', 'RecipeCode']

      // 过滤掉不需要显示的参数
      return this.selectedRecipeDetail.details.filter(item =>
        !excludedCodes.includes(item.parameter_code)
      )
    },
    getDeviceStatusClass() {
      return (status) => {
        switch (status) {
          case '0': // 未知
            return 'btnone0'
          case '1': // 初始化
            return 'btnone-initializing'
          case '2': // 待机
            return 'btnone-standby'
          case '3': // 报警
            return 'btnone-alarm'
          case '4': // 停止
            return 'btnone-stopped'
          case '5': // 运行
            return 'btnone-running'
          case '6': // PM
            return 'btnone-pm'
          default:
            return 'btnone0'
        }
      }
    }
  },
  mounted: function() {
    // 首先更根据工位号查询，获取到所要查询的点位实例
    this.getStationAttr()
    const that = this
    this.getMaterialInfo()
    this.getRecipeList()
    this.timer = setInterval(this.getAlarmData, 15000)

    // 添加定时查询CIM消息
    this.cimTimer = setInterval(this.getCIMMessages, 5000)

    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 608
      that.gatherHeight = document.documentElement.clientHeight - 424
    }
    this.$nextTick(() => {
      this.getCapacity()
      this.getOee()
      this.getreadbitRate()
    })
  },
  beforeDestroy() {
    clearInterval(this.timer)
    if (this.cimTimer) {
      clearInterval(this.cimTimer)
    }
  },
  created() {
    this.getCellIp()
  },
  methods: {
    // 获取配方列表
    getRecipeList() {
      const query = {
        station_code: this.$route.query.station_code,
        user_name: Cookies.get('userName')
      }
      eapRecipe.sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data && defaultQuery.data.length > 0) {
                // 没有重复的recipe_code，正常加载数据
                this.recipeList = defaultQuery.data.map(item => ({
                  recipe_id: item.recipe_id, // 使用recipe_id作为ID
                  recipe_name: item.recipe_name, // 使用recipe_name作为显示名称
                  device_code: item.device_code, // 设备编码
                  material_code: item.material_code, // 料号
                  recipe_version: item.recipe_version, // 配方版本
                  enable_flag: item.enable_flag, // 启用标志
                  isDuplicate: false // 标记非重复项
                }))
              console.log('配方列表加载成功，共', this.recipeList.length, '条数据')
            } else {
              this.recipeList = []
              console.log('未找到配方数据')
            }
          } else {
            this.$message({
              type: 'error',
              message: '获取配方列表失败: ' + defaultQuery.msg
            })
          }
        })
        .catch(error => {
          this.$message({
            type: 'error',
            message: '获取配方列表失败: ' + error
          })
        })
    },

    // 查找数组中的重复元素
    findDuplicates(array) {
      const counts = {}
      const duplicates = []

      // 计算每个元素出现的次数
      for (const item of array) {
        counts[item] = (counts[item] || 0) + 1
      }

      // 找出出现次数大于1的元素
      for (const item in counts) {
        if (counts[item] > 1) {
          duplicates.push(item)
        }
      }

      return duplicates
    },

    // 刷新配方列表
    refreshRecipeList() {
      // 重置配方参数配置不完整警告标志
      this.recipeConfigWarningShown = false
      // 重置配方参数配置完整性标志
      this.hasIncompleteRecipeConfig = false

      // 显示加载中提示
      const loading = this.$loading({
        lock: true,
        text: '正在刷新配方列表...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })

      // 调用获取配方列表方法
      const query = {
        station_code: this.$route.query.station_code,
        user_name: Cookies.get('userName')
      }

      eapRecipe.sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data && defaultQuery.data.length > 0) {
                // 没有重复的recipe_code，正常加载数据
                this.recipeList = defaultQuery.data.map(item => ({
                  recipe_id: item.recipe_id, // 使用recipe_id作为ID
                  recipe_name: item.recipe_name, // 使用recipe_name作为显示名称
                  device_code: item.device_code, // 设备编码
                  material_code: item.material_code, // 料号
                  recipe_version: item.recipe_version, // 配方版本
                  enable_flag: item.enable_flag, // 启用标志
                  isDuplicate: false // 标记非重复项
                }))

                // 显示成功消息
                this.$message({
                  type: 'success',
                  message: '配方列表刷新成功，共' + this.recipeList.length + '条数据'
                })

              // 如果当前选中的配方不在刷新后的列表中，清空选择
              if (this.selectedRecipe && !this.recipeList.some(item => item.recipe_id === this.selectedRecipe)) {
                this.selectedRecipe = null
                this.selectedRecipeDetail = null
              }

              console.log('配方列表刷新成功，共', this.recipeList.length, '条数据')
            } else {
              this.recipeList = []
              this.selectedRecipe = null
              this.selectedRecipeDetail = null
              this.$message({
                type: 'warning',
                message: '未找到配方数据'
              })
              console.log('未找到配方数据')
            }
          } else {
            this.$message({
              type: 'error',
              message: '刷新配方列表失败: ' + defaultQuery.msg
            })
          }
        })
        .catch(error => {
          this.$message({
            type: 'error',
            message: '刷新配方列表失败: ' + error
          })
        })
        .finally(() => {
          // 关闭加载提示
          loading.close()
        })
    },

    // 处理配方选择变更
    handleRecipeChange(recipeId) {
      if (!recipeId) {
        this.selectedRecipeDetail = null
        return
      }

      // 重置配方参数配置不完整警告标志
      this.recipeConfigWarningShown = false
      // 重置配方参数配置完整性标志
      this.hasIncompleteRecipeConfig = false

      // 只保存选中的配方ID，不加载详情
      // 找到选中的配方基本信息
      const recipeInfo = this.recipeList.find(item => item.recipe_id === recipeId)
      if (recipeInfo) {
        // 只保存基本信息，不包含详情
        this.selectedRecipeDetail = {
          ...recipeInfo,
          details: [] // 初始化为空数组，详情将在点击下发按钮时加载
        }
      }
    },

    // 加载配方详情
    loadRecipeDetails(recipeId) {
      return new Promise((resolve, reject) => {
        // 显示加载中状态
        this.recipeDetailLoading = true

        // 找到选中的配方基本信息
        const recipeInfo = this.recipeList.find(item => item.recipe_id === recipeId)
        if (!recipeInfo) {
          this.recipeDetailLoading = false
          reject(new Error('未找到选中的配方基本信息'))
          return
        }

        const query = {
          station_code: this.$route.query.station_code,
          recipe_id: recipeInfo.recipe_id,
          device_code: recipeInfo.device_code,
          user_name: Cookies.get('userName')
        }

        console.log('查询配方详情参数:', query)

        // 使用新的API接口查询配方详情
        eapRecipeDetail.sel(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              // 转换数据格式
              const details = defaultQuery.data || []

              // 检查是否有空的block_addr或target_tag_key
              const missingBlockAddr = []

              details.forEach(item => {
                // 检查block_addr是否为空
                if (!item.block_addr) {
                  missingBlockAddr.push({
                    tag_id: item.tag_id,
                    tag_code: item.tag_code,
                    tag_des: item.tag_des
                  })
                }
              })

              // 如果有缺失的配置，显示警告信息
              /**
              if (missingBlockAddr.length > 0 && !this.recipeConfigWarningShown) {
                let warningMsg = '配方参数配置不完整，请联系管理员补全scada_tag表配置：\n\n'

                if (missingBlockAddr.length > 0) {
                  warningMsg += '缺少block_addr配置的参数：\n'
                  missingBlockAddr.forEach(item => {
                    warningMsg += `${item.tag_id}- ${item.tag_code}（${item.tag_des}）\n`
                  })
                  warningMsg += '\n'
                }

                console.warn(warningMsg)

                // 显示警告消息，不自动关闭
                this.$message({
                  type: 'warning',
                  message: warningMsg.replace(/\n/g, '<br>'),
                  duration: 0, // 不自动关闭
                  showClose: true, // 显示关闭按钮
                  dangerouslyUseHTMLString: true, // 允许HTML格式
                  onClose: () => {
                    // 当消息关闭时，重置标志位，允许下次再次显示
                    this.recipeConfigWarningShown = false
                  }
                })

                // 设置标志位，防止重复显示
                this.recipeConfigWarningShown = true
              }
              **/

              // 直接使用API返回的数据结构，无需转换
              const formattedDetails = details.map(item => ({
                parameter_code: item.parameter_code,
                parameter_val: item.parameter_val || '',
                parameter_des: item.parameter_des,
                down_limit: item.down_limit,
                upper_limit: item.upper_limit,
                unit: item.unit,
                enable_flag: item.enable_flag,
                tag_key: item.tag_key,
                recipe_id: item.recipe_id,
                recipe_detail_id: item.recipe_detail_id,
                tag_id: item.tag_id,
                client_code: item.client_code,
                creation_date: item.creation_date
              }))

              this.selectedRecipeDetail = {
                ...recipeInfo,
                details: formattedDetails
              }

              console.log('配方详情加载成功，共', formattedDetails.length, '个参数')
              resolve(this.selectedRecipeDetail)
            } else {
              this.$message({
                type: 'error',
                message: '获取配方详情失败: ' + defaultQuery.msg
              })
              reject(new Error(defaultQuery.msg))
            }
          })
          .catch(error => {
            this.$message({
              type: 'error',
              message: '获取配方详情失败: ' + error
            })
            reject(error)
          })
          .finally(() => {
            // 无论成功或失败，都结束加载状态
            this.recipeDetailLoading = false
          })
      })
    },

    // 验证数量输入
    validateQuantity() {
      if (this.lot_count === '') {
        this.lot_count = '0'
        return
      }

      const count = parseInt(this.lot_count)
      if (isNaN(count) || count < 0) {
        this.$message({
          type: 'warning',
          message: '数量必须为大于等于0的整数'
        })
        this.lot_count = '0'
      }
    },

    // 显示配方确认对话框
    showRecipeConfirmDialog() {
      // 检查是否选择了配方
      if (!this.selectedRecipe) {
        this.$message({
          type: 'warning',
          message: '请先选择配方'
        })
        return
      }

      // 检查是否输入了批次号
      if (!this.lot_no) {
        this.$message({
          type: 'warning',
          message: '请输入批次号'
        })
        return
      }

      // 检查是否输入了料号
      if (!this.material_code) {
        this.$message({
          type: 'warning',
          message: '请输入料号'
        })
        return
      }

      // 检查设备状态是否为待机状态
      const deviceStatus = this.monitorData.DeviceStatus ? this.monitorData.DeviceStatus.value : null
      console.log('SFCOM下发配方流程 - 设备状态:', deviceStatus, '(2=待机状态)')
      if (deviceStatus !== '2') {
        this.$message({
          type: 'warning',
          message: '设备必须处于待机状态才能下发配方DeviceStatus=2'
        })
        return
      }

      // 显示加载中状态
      this.recipeDetailLoading = true

      // 加载配方详情
      this.loadRecipeDetails(this.selectedRecipe)
        .then(() => {
          // 加载成功后显示确认对话框
          this.recipeConfirmVisible = true
        })
        .catch(error => {
          console.error('加载配方详情失败:', error)
          // 即使加载失败也显示对话框，但会显示错误信息
          this.recipeConfirmVisible = true
        })
    },

    // 下发配方到设备
    downloadRecipe() {
      console.log('SFCOM下发配方流程 - 开始 ============================')
      console.log('SFCOM下发配方流程 - 配方ID:', this.selectedRecipe)
      console.log('SFCOM下发配方流程 - 配方名称:', this.selectedRecipeDetail ? this.selectedRecipeDetail.recipe_name : '未知')
      console.log('SFCOM下发配方流程 - 批次号:', this.lot_no)
      console.log('SFCOM下发配方流程 - 料号:', this.material_code)
      console.log('SFCOM下发配方流程 - 数量:', this.lot_count)

      // 再次检查设备状态是否为待机状态
      const deviceStatus = this.monitorData.DeviceStatus ? this.monitorData.DeviceStatus.value : null
      console.log('SFCOM下发配方流程 - 设备状态:', deviceStatus, '(2=待机状态)')

      if (deviceStatus !== '2') {
        console.log('SFCOM下发配方流程 - 设备不处于待机状态，无法下发配方')
        this.recipeConfirmVisible = false
        this.$message({
          type: 'warning',
          message: '设备必须处于待机状态才能下发配方'
        })
        console.log('SFCOM下发配方流程 - 结束 ============================')
        return
      }

      // 检查是否允许下发配方
      console.log('SFCOM下发配方流程 - RecipeUpd值:', this.monitorData.RecipeUpd ? this.monitorData.RecipeUpd.value : '未知')

      if (this.monitorData.RecipeUpd.value === '0') {
        console.log('SFCOM下发配方流程 - 设备运行中，不允许下发配方')
        this.recipeConfirmVisible = false
        this.$message({
          type: 'info',
          message: '设备运行中，不允许下发配方'
        })
        const newRow = {
          TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
          TagValue: '0'
        }
        const rowJson = [newRow]
        const sendJson = {
          Data: rowJson,
          ClientName: 'SCADA_WEB'
        }
        const sendStr = JSON.stringify(sendJson)
        const topic = `SCADA_WRITE/${this.stationAttr}Plc`
        console.log('SFCOM下发配方流程 - 发送取消下发配方请求')
        this.sendMessage(topic, sendStr)
        console.log('SFCOM下发配方流程 - 结束 ============================')
        return
      }

      // 在下发配方前重新查询配方详情
      console.log('SFCOM下发配方流程 - 重新查询配方详情')
      this.recipeDetailLoading = true

      // 使用Promise处理异步查询
      this.loadRecipeDetails(this.selectedRecipe)
        .then(() => {
          // 检查是否有配方参数配置不完整的情况
          if (this.hasIncompleteRecipeConfig) {
            console.log('SFCOM下发配方流程 - 配方参数配置不完整，无法下发配方')
            this.recipeConfirmVisible = false
            this.$message({
              type: 'error',
              message: '配方参数配置不完整，无法下发配方，请联系管理员补全scada_tag表的block_addr配置',
              duration: 0,
              showClose: true
            })
            console.log('SFCOM下发配方流程 - 结束 ============================')
            return
          }

          // 查询成功且配方参数配置完整，继续下发配方
          this.continueDownloadRecipe()
        })
        .catch(error => {
          console.error('SFCOM下发配方流程 - 重新查询配方详情失败:', error)
          this.recipeConfirmVisible = false
          this.$message({
            type: 'error',
            message: '获取配方详情失败，无法下发配方'
          })
          console.log('SFCOM下发配方流程 - 结束 ============================')
        })
    },

    // 继续下发配方流程
    continueDownloadRecipe() {
      // 准备下发数据
      console.log('SFCOM下发配方流程 - 准备下发数据')
      const allRowJson = []

      // 1. 下发配方参数（只下发有tag_key的参数）
      if (this.selectedRecipeDetail && this.selectedRecipeDetail.details) {
        console.log('SFCOM下发配方流程 - 配方详情项数量:', this.selectedRecipeDetail.details.length)
        this.selectedRecipeDetail.details.forEach((item, index) => {
          // 跳过没有tag_key的参数
          if (!item.tag_key) {
            console.log(`SFCOM下发配方流程 - 跳过参数[${index}]: ${item.parameter_code} (无tag_key)`)
            return
          }

          let paramValue = item.parameter_val
          
          // 根据参数类型设置对应的值
          if (item.parameter_code === 'LotID') {
            paramValue = this.lot_no
            console.log(`SFCOM下发配方流程 - 设置批次号参数: ${item.parameter_code} = ${paramValue}`)
          } else if (item.parameter_code === 'PartNo') {
            paramValue = this.material_code
            console.log(`SFCOM下发配方流程 - 设置料号参数: ${item.parameter_code} = ${paramValue}`)
          } else if (item.parameter_code === 'PlanCount') {
            paramValue = this.lot_count
            console.log(`SFCOM下发配方流程 - 设置数量参数: ${item.parameter_code} = ${paramValue}`)
          } else {
            console.log(`SFCOM下发配方流程 - 参数[${index}]: ${item.parameter_code} = ${paramValue}`)
          }

          const newRow = {
            TagKey: item.tag_key,
            TagValue: paramValue
          }
          console.log(newRow)
          allRowJson.push(newRow)
        })
      } else {
        console.log('SFCOM下发配方流程 - 警告: 未找到配方详情或配方详情为空')
      }

      // 2. 添加下发完成信号
      console.log('SFCOM下发配方流程 - 添加下发配方请求标志')
      allRowJson.push({
        TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
        TagValue: '1'
      })

      // 3. 一次性发送所有数据
      console.log('SFCOM下发配方流程 - 准备发送MQTT消息')
      const sendJson = {
        Data: allRowJson,
        ClientName: 'SCADA_WEB'
      }
      const sendStr = JSON.stringify(sendJson)
      const topic = `SCADA_WRITE/${this.stationAttr}Plc`
      console.log('SFCOM下发配方流程 - 发送MQTT消息')
      this.sendMessage(topic, sendStr)
      //{"TagKey":"SkAis/AisStatus/PlanCount","TagValue":"1"}
      const payload = {
        Data: [
          {
            TagKey: `${this.stationAttr}Ais/AisStatus/PlanCount`,
            TagValue: "1"
          }
        ],
        ClientName: "SCADA_WEB"
      };
      this.sendMessage(`SCADA_WRITE/${this.stationAttr}Ais`, JSON.stringify(payload));

      // 4. 间隔4秒后复位RecipeDownReq=0
      setTimeout(() => {
        const resetRowJson = [{
          TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
          TagValue: '0'
        }]
        const resetSendJson = {
          Data: resetRowJson,
          ClientName: 'SCADA_WEB'
        }
        const resetSendStr = JSON.stringify(resetSendJson)
        this.sendMessage(topic, resetSendStr)
        console.log('SFCOM下发配方流程 - 复位RecipeDownReq=0')
      }, 4000)

      // 关闭对话框并显示成功消息
      console.log('SFCOM下发配方流程 - 设置定时器关闭对话框')
      setTimeout(() => {
        this.recipeConfirmVisible = false
        this.$message({
          type: 'success',
          message: '下发配方成功'
        })
        console.log('SFCOM下发配方流程 - 结束 ============================')
      }, 500)
    },

    getStationAttr() {
      const query = {
        stationCodeDes: this.$route.query.station_code,
        user_name: Cookies.get('userName')
      }
      selStation(query).then(res => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.stationAttr = res.data[0].station_attr
            this.station_code = res.data[0].station_code
            this.client_id = res.data[0].station_short_code
            Object.keys(this.monitorData).forEach((key) => {
              this.monitorData[key].client_code = `${this.stationAttr}` + this.monitorData[key].client_code
            })
            this.getScadaData(this.client_id)
            return
          }
          this.stationAttr = ''
        }
      })
    },
    getScadaData(client_id) {
      const query = {
        client_id: client_id,
        enable_flag: 'Y',
        sort: 'tag_id',
        user_name: Cookies.get('userName')
      }
      scadaTagGroupTree(query).then((res) => {
        if (res.data.length > 0) {
          const data = res.data.find((item) => item.tag_group_code === 'PcRecipe')
          if (data && data.children.length > 0) {
            data.children.forEach((e) => {
              this.groupData.push({
                tag_key: `${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`,
                tag_des: e.tag_des,
                value: ''
              })
            })
            this.tableData = data.children.reduce((acc, e) => {
              acc[`${this.stationAttr}Plc/${e.tag_group_code}/${e.tag_code}`] = ''
              return acc
            }, {})
          }
        }
      })
      const params = {
        tableOrder: 'asc',
        tableOrderField: 'tag_id',
        tablePage: 1,
        tableSize: 1000,
        tag_group_id: client_id + '03',
        user_name: Cookies.get('userName')
      }
      selScadaTag(params).then((res) => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            res.data.forEach((item) => {
              this.dict.PROJECT_PARAMS_WARNING.forEach((e) => {
                if (`${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}` === e.value) {
                  const result = {
                    tag_des: item.tag_des,
                    tag_key: `${this.stationAttr}Plc/${item.tag_attr}/${item.tag_code}`,
                    tag_value: '',
                    unit: '',
                    down_limit: item.down_limit,
                    upper_limit: item.upper_limit,
                    status: ''
                  }
                  this.plcCraftData.push(result)
                }
              })
            })
          }
        }
      })
    },
    getMaterialInfo() {
      const query = {
        page: 1,
        size: 1000,
        sort: 'material_id desc',
        user_name: Cookies.get('userName')
      }
      selMaterial(query)
        .then((res) => {
          if (res.code === 0) {
            if (res.data.length > 0) {
              this.materialData = res.data
              return
            }
            this.materialData = []
            this.$message({
              type: 'warning',
              message: '当前未获取到物料，请先去维护物料'
            })
          }
        })
        .catch((error) => {
          this.materialData = []
          this.$message({ type: 'error', message: '查询失败' + error.msg })
        })
    },
    // 根据料号查询配方 - 已不再使用，由下拉列表选择配方替代
    clientCodeSelect() {
      // 保留方法以避免修改其他引用
    },

    choiceLotNum() {
      this.dialogVisible = true
    },
    handleDisabled(value) {
      if (this.disabled) return false
      const Arr = ['null', '', 'undefined', '0']
      if (!value || Arr.includes(value)) return true
    },
    // 处理设备模式切换
    handleModeSwitch(modeValue) {
      console.log('设备模式切换 - 开始 ============================')
      console.log('设备模式切换 - 模式值:', modeValue)

      // 构建Remote标签路径
      const remoteTagKey = `${this.stationAttr}Plc/PcStatus/Remote`
      // 构建LightContrl标签路径
      const lightControlTagKey = `${this.stationAttr}Plc/PcStatus/LightContrl`

      // 准备数据数组
      var rowJson = []

      // 添加Remote参数
      rowJson.push({
        TagKey: remoteTagKey,
        TagValue: modeValue
      })

      // 根据Remote值设置LightContrl
      var lightControlValue = '0'
      if (modeValue === '1') {
        // 蓝灯亮
        console.log('设备模式切换 - 设置为在线远程模式，LightContrl设为4')
        lightControlValue = '4'
      } else {
        // 在线本地或者离线，关闭蓝灯
        console.log('设备模式切换 - 设置为其他模式，LightContrl设为0')
        lightControlValue = '0'
      }

      // 添加LightContrl参数
      rowJson.push({
        TagKey: lightControlTagKey,
        TagValue: lightControlValue
      })

      // 构建发送数据
      var sendJson = {
        Data: rowJson,
        ClientName: 'SCADA_WEB'
      }

      // 序列化并发送
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + remoteTagKey.split('/')[0]

      console.log('设备模式切换 - 发送MQTT消息:', topic)
      console.log('设备模式切换 - 消息内容:', sendStr)

      this.sendMessage(topic, sendStr)
      console.log('设备模式切换 - 结束 ============================')
    },

    handleWrite(key, value) {
      console.log(this.monitorData)
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: key,
        TagValue: value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + key.split('/')[0]
      this.sendMessage(topic, sendStr)
    },

    // 获取CIM消息
    getCIMMessages() {
      if (!this.queryCim) return

      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.$route.query.station_id
      }

      eapCimMsgShow(query)
        .then(res => {
          if (res.code === 0 && res.count > 0) {
            const msgInfo = res.data[0]
            this.screen_code = msgInfo.screen_code
            this.cim_msg = msgInfo.cim_msg
            this.ConfirmBtnVisible = msgInfo.screen_control === '1'
            this.dialogCIMMsgVisible = true
            this.queryCim = false

            // 如果不需要手动确认，设置自动关闭定时器
            if (!this.ConfirmBtnVisible) {
              const timeoutMs = msgInfo.interval_second_time ? parseInt(msgInfo.interval_second_time) * 1000 : 15000
              setTimeout(() => {
                this.resetCIMDialog()
              }, timeoutMs)
            }
          }
        })
        .catch(err => {
          console.error('获取CIM消息失败:', err)
        })
    },

    // 处理确认CIM消息
    handleConfirmCIMMsg() {
      this.resetCIMDialog()
    },

    // 重置CIM消息对话框
    resetCIMDialog() {
      this.dialogCIMMsgVisible = false
      this.screen_code = ''
      this.cim_msg = ''
      this.ConfirmBtnVisible = false
      this.queryCim = true
    },
    handleOk() {
      console.log('SFCOM配方对话框确认 - 开始 ============================')

      var rowJson = []
      var sendJson = {}
      var sendStr = {}
      var topic = ''

      // 1.读取plc修改配方，判断是否允许下发配方
      console.log('SFCOM配方对话框确认 - RecipeUpd值:', this.monitorData.RecipeUpd ? this.monitorData.RecipeUpd.value : '未知')

      if (this.monitorData.RecipeUpd.value === '0') {
        console.log('SFCOM配方对话框确认 - 设备运行中，不允许下发配方')
        this.dialogVisible = false
        this.$message({ type: 'info', message: '设备运行中，不允许下发配方' })
        var newRow = {
          TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
          TagValue: '0'
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        sendStr = JSON.stringify(sendJson)
        topic = `SCADA_WRITE/${this.stationAttr}Plc`
        console.log('SFCOM配方对话框确认 - 发送取消下发配方请求')
        this.sendMessage(topic, sendStr)
        console.log('SFCOM配方对话框确认 - 结束 ============================')
        return
      }

      // 2.请求下发配方
      console.log('SFCOM配方对话框确认 - 准备下发配方参数')
      console.log('SFCOM配方对话框确认 - 配方参数数量:', this.crud.data ? this.crud.data.length : 0)

      this.crud.data.forEach((item, index) => {
        console.log(`SFCOM配方对话框确认 - 参数[${index}]:`, item.parameter_code, '=', item.parameter_val)
        var newRow2 = {
          TagKey: item.tag_key,
          TagValue: item.parameter_val
        }
        rowJson.push(newRow2)
      })

      // 3.添加PlanCount参数
      console.log('SFCOM配方对话框确认 - 添加PlanCount参数')
      var newRowPlanCount = {
        TagKey: `${this.stationAttr}Plc/PcRecipe/PlanCount`,
        TagValue: this.lot_count || '0'
      }
      rowJson.push(newRowPlanCount)

      // 4.下发完成请求下发信号
      console.log('SFCOM配方对话框确认 - 添加下发配方请求标志')
      var newRow3 = {
        TagKey: `${this.stationAttr}Plc/PcStatus/RecipeDownReq`,
        TagValue: '1'
      }
      rowJson.push(newRow3)

      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      sendStr = JSON.stringify(sendJson)
      topic = `SCADA_WRITE/${this.stationAttr}Plc`

      console.log('SFCOM配方对话框确认 - 准备发送MQTT消息')
      console.log('SFCOM配方对话框确认 - 主题:', topic)
      console.log('SFCOM配方对话框确认 - 数据项数量:', rowJson.length)

      this.sendMessage(topic, sendStr)

      console.log('SFCOM配方对话框确认 - 设置定时器关闭对话框')
      setTimeout(() => {
        this.dialogVisible = false
        this.$message({ type: 'success', message: '下发配方成功' })
        console.log('SFCOM配方对话框确认 - 结束 ============================')
      }, 500)
    },
    getCapacity() {
      this.capacityDom = this.$echarts.init(
        document.getElementById('capacityDom')
      )
      var that = this
      this.capacityDom.setOption(this.capacityOption)
      window.addEventListener('resize', function() {
        that.capacityDom.resize()
      })
    },
    getOee() {
      this.oeeDom = this.$echarts.init(document.getElementById('oeeDom'))
      var that = this
      this.oeeDom.setOption(this.oeeOption)
      window.addEventListener('resize', function() {
        that.oeeDom.resize()
      })
    },
    getreadbitRate() {
      this.readbitRateDom = this.$echarts.init(
        document.getElementById('readbitRateDom')
      )
      var that = this
      this.readbitRateDom.setOption(this.readbitRateOption)
      window.addEventListener('resize', function() {
        that.readbitRateDom.resize()
      })
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            setTimeout(() => {
              this.toStartWatch()
              this.getTagValue()
              this.getAlarmData()
            }, 3000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: '查询异常', type: 'error' })
        })
    },
    getAlarmData() {
      var method = '/cell/core/scada/CoreScadaAlarmSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      var queryData = {
        tablePage: 1,
        tableSize: 10
      }
      axios
        .post(path, queryData, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.alarmData = defaultQuery.data
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    getTagValue() {
      var readTagArray = []
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.$route.query.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      this.groupData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        })
      })
      this.plcCraftData.forEach((item) => {
        readTagArray.push({
          tag_key: item.tag_key
        })
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code =
                    client_code + '_' + this.$route.query.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter((item) => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
              result.forEach((e) => {
                this.plcCraftData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.tag_value = e.tag_value
                    if (
                      typeof +e.tag_value === 'number' &&
                      +e.tag_value >= item.down_limit &&
                      +e.tag_value <= item.upper_limit
                    ) {
                      item.status = 'OK'
                    } else {
                      item.status = 'NG'
                    }
                  }
                })
                this.groupData.forEach((item) => {
                  if (item.tag_key === e.tag_key) {
                    item.value = e.tag_value
                  }
                })
              })
            }
          }
        })
        .catch((ex) => {
          this.$message({ message: '查询异常：' + ex, type: 'error' })
        })
    },
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', () => {
        console.log('MQTT连接成功，开始初始化数据...')
        this.mqttConnStatus = true
        var aisClientCode = `${this.stationAttr}Ais`
        var plcClientCode = `${this.stationAttr}Plc`
        var eapClientCode = `${this.stationAttr}Eap`
        this.topicSubscribe('SCADA_STATUS/' + aisClientCode)
        this.topicSubscribe('SCADA_BEAT/' + aisClientCode)
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        this.topicSubscribe('SCADA_STATUS/' + eapClientCode)
        this.topicSubscribe('SCADA_BEAT/' + eapClientCode)
        this.groupData.forEach((item) => {
          this.topicSubscribe('SCADA_CHANGE/' + item.tag_key)
        })
        Object.keys(this.monitorData).forEach((key) => {
          // 跳过clientHeartbeats对象
          if (key === 'clientHeartbeats') return

          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.$route.query.station_code
          }
          this.topicSubscribe(
            'SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code
          )
        })
        this.$message({
          message: '连接成功',
          type: 'success'
        })
        // 记录当前登录账户
        if (this.user && this.user.nickName) {
          this.handleWrite(`${this.stationAttr}Ais/AisStatus/CurrentUser`, this.user.nickName)
        }

        // 在MQTT连接成功后加载配方列表
        console.log('MQTT连接成功，开始加载配方列表...')
        this.getRecipeList()
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: '连接失败',
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: '连接断开，正在重连。。。',
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {})
      this.clientMqtt.on('close', () => {})
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 处理心跳消息
          if (topic.indexOf('SCADA_BEAT/') >= 0) {
            // 从主题中提取客户端代码
            const clientCode = topic.split('/')[1]
            console.log(`收到心跳消息 - 客户端: ${clientCode}`)

            // 更新对应客户端的心跳状态
            if (this.monitorData.clientHeartbeats && this.monitorData.clientHeartbeats[clientCode]) {
              this.monitorData.clientHeartbeats[clientCode].heartbeat = true

              // 设置心跳超时，5秒后如果没有新的心跳，则将状态设为false
              setTimeout(() => {
                if (this.monitorData.clientHeartbeats && this.monitorData.clientHeartbeats[clientCode]) {
                  this.monitorData.clientHeartbeats[clientCode].heartbeat = false
                  console.log(`心跳超时 - 客户端: ${clientCode}`)
                }
              }, 5000)
            }
            return
          }

          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            this.plcCraftData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.tag_value = jsonData.TagNewValue
                if (
                  typeof +jsonData.TagNewValue === 'number' &&
                  +jsonData.TagNewValue >= item.down_limit &&
                  +jsonData.TagNewValue <= item.upper_limit
                ) {
                  item.status = 'OK'
                } else {
                  item.status = 'NG'
                }
              }
            })

            Object.keys(this.monitorData).forEach((key) => {
              // 跳过clientHeartbeats对象
              if (key === 'clientHeartbeats') return

              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              if (this.aisMonitorMode === 'AIS-SERVER') {
                client_code =
                  client_code + '_' + this.$route.query.station_code
              }
              var tag_key = client_code + '/' + group_code + '/' + tag_code
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue
              }
            })
            this.groupData.forEach((item) => {
              if (item.tag_key === jsonData.TagKey) {
                item.value = jsonData.TagNewValue
              }
            })
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      // 添加详细日志，记录下发配方的信息
      console.log('SFCOM下发配方 - 开始 ============================')
      console.log('SFCOM下发配方 - 主题:', topic)
      console.log('SFCOM下发配方 - 消息内容:', msg)

      try {
        // 尝试解析消息内容，以便更好地记录
        const msgObj = JSON.parse(msg)
        console.log('SFCOM下发配方 - 消息对象:', msgObj)

        if (msgObj.Data && Array.isArray(msgObj.Data)) {
          console.log('SFCOM下发配方 - 数据项数量:', msgObj.Data.length)

          // 记录每个数据项
          msgObj.Data.forEach((item) => {
            // 检查是否是下发配方请求
            if (item.TagKey && item.TagKey.includes('/RecipeDownReq')) {
              console.log(`SFCOM下发配方 - 发现下发配方请求:`, item)
            }
          })
        }
      } catch (e) {
        console.log('SFCOM下发配方 - 消息内容不是JSON格式:', e)
      }

      if (!this.mqttConnStatus) {
        console.error('SFCOM下发配方失败 - MQTT未连接')
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        console.log('SFCOM下发配方 - 结束 ============================')
        return
      }

      // qos消息发布服务质量
      console.log('SFCOM下发配方 - 正在发送MQTT消息...')
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.log('SFCOM下发配方成功 - MQTT消息已发送')
          console.warn('MQTT发送成功：' + topic)
          // this.$message({ message: '写入成功', type: 'success' })
        } else {
          console.error('SFCOM下发配方失败 - MQTT发送错误:', error)
          this.$message({ message: '操作失败！', type: 'error' })
        }
        console.log('SFCOM下发配方 - 结束 ============================')
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: '请启动监控',
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: '请启动监控',
            type: 'error'
          })
          return
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    }
  }
}
</script>
<style lang="less" scoped>
.app-container {
  padding: 10px;
  ::v-deep .el-header {
    padding: 0;
  }
  .header {
    ::v-deep .el-card__body {
      padding: 10px 15px 0 !important;
    }
  }
  .wrapTextSelect {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrapElForm {
    display: flex;
  }
  .pieChart {
    width: 100%;
    display: flex;
    div {
      width: 33%;
    }
    #capacityDom {
      height: 300px;
    }
    #capacityDom {
      height: 300px;
    }
    #readbitRateDom {
      height: 300px;
    }
  }
  .active {
    ::v-deep .el-input__inner {
      background-color: #ffff00;
    }
  }
  .dialog-footer {
    text-align: center;
  }
  .statuHead {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .wrappstyle {
    display: flex;
    align-items: center;
    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;
      span {
        font-size: 12px;
        font-weight: 700;
      }
      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }

    p:last-child {
      margin-right: 0 !important;
    }

    .el-divider--vertical {
      width: 2px;
      height: 2em;
    }
  }
  .btnone {
    background: #50d475;
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
  }

  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
    font-size: 18px;
  }

  .btnone-initializing {
    background: #409EFF; /* 蓝色 - 初始化 */
    border-color: #409EFF;
    color: #fff;
    font-size: 18px;
  }

  .btnone-standby {
    background: #50d475; /* 绿色 - 待机 */
    border-color: #50d475;
    color: #fff;
    font-size: 18px;
  }

  .btnone-alarm {
    background: #F56C6C; /* 红色 - 报警 */
    border-color: #F56C6C;
    color: #fff;
    font-size: 18px;
  }

  .btnone-stopped {
    background: #E6A23C; /* 橙色 - 停止 */
    border-color: #E6A23C;
    color: #fff;
    font-size: 18px;
  }

  .btnone-running {
    background: #67C23A; /* 深绿色 - 运行 */
    border-color: #67C23A;
    color: #fff;
    font-size: 18px;
  }

  .btnone-pm {
    background: #909399; /* 灰色 - PM */
    border-color: #909399;
    color: #fff;
    font-size: 18px;
  }

  .btnone:active {
    background: #13887c;
  }

  /* 配方确认对话框样式 */
  .recipe-confirm-dialog {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }

  /* 确保对话框的最小宽度 */
  .recipe-confirm-dialog {
    min-width: 750px !important;
  }

  /* 使用深度选择器确保样式应用到内部元素 */
  .recipe-confirm-dialog ::v-deep .el-dialog {
    min-width: 750px !important;
  }

  .recipe-confirm-dialog .el-dialog__body {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    max-height: calc(100vh - 100px);
  }
  .el-dialog__body {
    padding: 0px 20px;
    color: #606266;
    font-size: 14px;
    word-break: break-all;
  }
  /* 确保表格显示最多20行，超出滚动 */
  .recipe-confirm-dialog .el-table {
    max-height: none !important;
    font-size: 14px;
  }

  .recipe-confirm-dialog .el-table__body-wrapper {
    overflow-y: auto;
    max-height: 300px; /* 约20行的高度 */
  }

  /* 表格标题和内容字体 */
  .recipe-confirm-dialog .el-table th {
    font-size: 16px !important;
    padding: 12px 0 !important;
  }

  .recipe-confirm-dialog .el-table td {
    font-size: 16px !important;
    padding: 10px 0 !important;
  }

  .recipe-confirm-dialog .el-dialog__header {
    padding: 15px 20px;
  }

  .no-recipe-selected {
    text-align: center;
    padding: 30px;
    color: #909399;
    font-size: 16px;
  }
  .el-descriptions .is-bordered {
    table-layout: auto;
    font-size: 24px !important;
}
  /* 配方信息字体样式 */
  .recipe-info-descriptions {
    font-size: 22px; /* 基础字体大小 */
  }

  .recipe-info-descriptions .el-descriptions-item__label {
    font-size: 24px !important;
    font-weight: bold !important;
    padding: 15px 12px !important;
    line-height: 1.5 !important;
  }

  .recipe-info-descriptions .el-descriptions-item__content {
    font-size: 22px !important;
    padding: 15px 12px !important;
    line-height: 1.5 !important;
  }
  .wholeline {
    width: 20px;
    height: 20px;
    border-radius: 50%;
  }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(
        0.3em 0.25em at 50% 25%,
        rgb(255, 255, 255) 25%,
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.25em 0.25em at 30% 75%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        0.3em 0.3em at 60% 80%,
        rgba(255, 255, 255, 0.5),
        rgba(255, 255, 255, 0)
      ),
      radial-gradient(
        100% 100%,
        rgba(255, 255, 255, 0) 30%,
        rgba(255, 255, 255, 0.3) 40%,
        rgba(0, 0, 0, 0.5) 50%
      );
  }
  .wholeline1 {
    width: 20px;
    height: 20px;
  }
  .wholelinenormal1,
  .deviceGreen {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .deviceRed {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelineerror1,
  .deviceYellow {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #eeff00;
    box-shadow: 0 0 0.75em #eeff00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }
  .wholelinegray1 {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    //   background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  /* 重复配方项样式 */
  ::v-deep .duplicate-recipe {
    background-color: #fef0f0 !important;
    color: #f56c6c !important;
    font-weight: bold !important;
  }

  ::v-deep .duplicate-recipe.selected,
  ::v-deep .duplicate-recipe:hover {
    background-color: #fde2e2 !important;
    color: #f56c6c !important;
  }
  .dialogTable {
    ::v-deep .el-dialog {
      margin-top: 5vh !important;
    }
  }
}
</style>

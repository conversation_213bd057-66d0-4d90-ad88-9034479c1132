<template>
  <div></div>
</template>

<script>
/**
 * 通知管理器组件
 * 提供统一的弹窗通知功能，支持位置、类型等配置
 */
export default {
  name: 'NotificationManager',
  data() {
    return {
      // 通知配置
      defaultConfig: {
        position: 'top-right',
        type: 'info',
        duration: 0,
        showClose: true
      }
    }
  },
  methods: {
    /**
     * 显示通知
     * @param {Object} options 通知选项
     * @param {String} options.title 通知标题
     * @param {String} options.message 通知内容
     * @param {String} options.type 通知类型：success、warning、info、error
     * @param {String} options.position 通知位置：top-right、top-left、bottom-right、bottom-left
     * @param {Number} options.duration 显示时长（毫秒），0表示不自动关闭
     * @param {Boolean} options.showClose 是否显示关闭按钮
     * @param {Function} options.onClick 点击回调
     * @param {Function} options.onClose 关闭回调
     */
    showNotification(options = {}) {
      // 合并默认配置
      const config = Object.assign({}, this.defaultConfig, options)

      // 验证并修正参数
      config.type = this.validateType(config.type)
      config.position = this.validatePosition(config.position)

      console.log('显示通知配置:', config)

      // 如果位置是center，使用MessageBox显示在正中间
      if (config.position === 'center') {
        this.showCenterNotification(config)
      } else {
        // 使用Element UI的通知组件
        this.$notify({
          title: config.title || '系统通知',
          message: config.message || '',
          type: config.type,
          position: config.position,
          duration: config.duration,
          showClose: config.showClose,
          onClick: config.onClick,
          onClose: config.onClose,
          customClass: 'custom-notification'
        })
      }
    },

    /**
     * 显示正中间通知
     */
    showCenterNotification(config) {
      const iconMap = {
        'success': 'success',
        'warning': 'warning',
        'error': 'error',
        'info': 'info'
      }

      const messageBoxConfig = {
        title: config.title || '系统通知',
        message: config.message || '',
        type: iconMap[config.type] || 'info',
        showClose: config.showClose !== false,
        customClass: 'center-notification-dialog',
        callback: (action) => {
          if (config.onClose) {
            config.onClose()
          }
          if (action === 'confirm' && config.onClick) {
            config.onClick()
          }
        }
      }

      // 如果设置了自动关闭时间，使用定时器
      if (config.duration > 0) {
        messageBoxConfig.showCancelButton = false
        messageBoxConfig.showConfirmButton = false
        messageBoxConfig.closeOnClickModal = false
        messageBoxConfig.closeOnPressEscape = false

        this.$msgbox(messageBoxConfig).catch(() => {})

        // 自动关闭
        setTimeout(() => {
          this.$msgbox.close()
          if (config.onClose) {
            config.onClose()
          }
        }, config.duration)
      } else {
        // 手动关闭
        messageBoxConfig.confirmButtonText = '确定'
        messageBoxConfig.showCancelButton = false
        this.$msgbox(messageBoxConfig).catch(() => {})
      }
    },

    /**
     * 显示消息通知（兼容旧版本）
     * @param {Object} message 消息对象
     */
    showMessageNotification(message) {
      const notificationType = this.getNotificationType(message.screen_control)
      let duration = 0 // 默认不自动关闭

      // 根据screen_control判断关闭行为
      if (message.screen_control === '0') {
        // screen_control = 0: 可以自动关闭
        if (message.interval_second_time && message.interval_second_time > 0) {
          // 如果有自定义时间，使用自定义时间
          duration = message.interval_second_time * 1000
        } else {
          // 否则使用默认5秒
          duration = 5000
        }
      } else {
        // screen_control = 1: 不能自动关闭，需要手动点击关闭
        duration = 0
      }

      // 使用新的弹窗属性，如果没有则使用默认值
      const position = message.popup_position || 'top-right'
      const type = message.popup_type || this.getNotificationType(message.screen_control)

      this.showNotification({
        title: `${message.cim_from || 'SYSTEM'}`,
        message: message.cim_msg,
        type: type,
        position: position,
        duration: duration,
        onClick: () => {
          this.$emit('message-click', message)
        }
      })
    },

    /**
     * 验证通知类型
     */
    validateType(type) {
      const validTypes = ['success', 'warning', 'info', 'error']
      return validTypes.includes(type) ? type : 'info'
    },

    /**
     * 验证通知位置
     */
    validatePosition(position) {
      const validPositions = ['top-right', 'top-left', 'bottom-right', 'bottom-left', 'center']
      return validPositions.includes(position) ? position : 'top-right'
    },

    /**
     * 根据screen_control获取通知类型（兼容旧版本）
     */
    getNotificationType(screenControl) {
      switch (screenControl) {
        case '0':
          return 'info'
        case '1':
          return 'warning'
        default:
          return 'info'
      }
    },

    /**
     * 快捷方法：成功通知
     */
    success(title, message, options = {}) {
      this.showNotification({
        title,
        message,
        type: 'success',
        ...options
      })
    },

    /**
     * 快捷方法：警告通知
     */
    warning(title, message, options = {}) {
      this.showNotification({
        title,
        message,
        type: 'warning',
        ...options
      })
    },

    /**
     * 快捷方法：信息通知
     */
    info(title, message, options = {}) {
      this.showNotification({
        title,
        message,
        type: 'info',
        ...options
      })
    },

    /**
     * 快捷方法：错误通知
     */
    error(title, message, options = {}) {
      this.showNotification({
        title,
        message,
        type: 'error',
        ...options
      })
    }
  }
}
</script>

<style scoped>
/* 组件本身不需要样式，因为使用的是Element UI的通知组件 */
</style>

<style>
/* 自定义通知样式 - 调整字体大小 */
.custom-notification .el-notification__title {
  font-size: 16px !important;
  font-weight: 600 !important;
  margin-bottom: 6px !important;
}

.custom-notification .el-notification__content {
  font-size: 14px !important;
  line-height: 1.5 !important;
}

/* 正中间弹窗样式 */
.center-notification-dialog .el-message-box__title {
  font-size: 16px !important;
  font-weight: 600 !important;
}

.center-notification-dialog .el-message-box__content {
  font-size: 14px !important;
  line-height: 1.5 !important;
}

.center-notification-dialog .el-message-box__message {
  font-size: 14px !important;
}
</style>

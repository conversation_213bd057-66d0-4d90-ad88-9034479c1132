<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-8 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.timermain.timerMainCode')">
                <!-- 主Timer事件编码： -->
                <el-input v-model="query.event_timer_main_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12" style="margin-left: 10px">
              <el-form-item :label="$t('lang_pack.timermain.timerMainDes')">
                <!-- 主Timer事件描述： -->
                <el-input v-model="query.event_timer_main_des" clearable size="small" />
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-4 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />
      <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="1000px">
        <el-tabs v-model="activeName">
          <el-tab-pane :label="$t('lang_pack.mainmain.basicAttribute')" name="first">
            <!-- 基础属性 -->
            <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="140px" :inline="true">
              <el-form-item label="TIMER模板" prop="event_mod_timer_id">
                <!-- TIMER事件模板 -->
                <el-select v-model="form.event_mod_timer_id" filterable clearable>
                  <el-option v-for="item in timerModMainData" :key="item.event_mod_timer_id" :label="item.event_mod_timer_code + ' ' + item.event_mod_timer_des" :value="item.event_mod_timer_id" />
                </el-select>
              </el-form-item>
              <el-form-item :label="$t('lang_pack.mainmain.station')" prop="station_id">
                <!-- 工位 -->
                <el-select v-model="form.station_id" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id" />
                </el-select>
              </el-form-item>
              <el-form-item label="TIMER编码" prop="event_timer_main_code">
                <!-- TIMER编码 -->
                <el-input v-model="form.event_timer_main_code" />
              </el-form-item>
              <el-form-item label="TIMER描述" prop="event_timer_main_des">
                <!-- TIMER描述 -->
                <el-input v-model="form.event_timer_main_des" />
              </el-form-item>
              <el-form-item label="执行次数" prop="execute_count">
                <!-- 执行次数 -->
                <el-input v-model="form.execute_count" type="number" />
              </el-form-item>

              <el-form-item :label="$t('lang_pack.mainmain.examplesCollections')" prop="client_id_list">
                <!-- 实例集合 -->
                <!-- <el-select v-model="form.client_id_list" filterable clearable multiple>
                  <el-option v-for="item in clientData" :key="item.client_id" :label="item.client_des" :value="item.client_id + ''" />
                </el-select> -->
                <!-- @focus="$refs.popupDialog.open()" -->
                <el-input v-model="form.client_id_list" readonly="readonly">
                  <div slot="append">
                    <el-button slot="reference" @click="$refs.popupDialog.open(form.client_id_list)">选择</el-button>
                  </div>
                </el-input>
              </el-form-item>

              <el-form-item label="同步执行" prop="sync_flag">
                <el-select v-model="form.sync_flag">
                  <el-option
                    label="Y"
                    value="Y"
                  />
                  <el-option
                    label="N"
                    value="N"
                  />
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.mainmain.pollingTime')" prop="cycle_time">
                <!-- 轮询时间 -->
                <el-input v-model="form.cycle_time" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
                <!-- 有效标识 -->
                <el-radio-group v-model="form.enable_flag">
                  <el-radio v-for="item in dict.ENABLE_FLAG" :key="item.id" :label="item.value">{{ item.label }}</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>

            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
          </el-tab-pane>
          <el-tab-pane :label="$t('lang_pack.logicfunc.attributeGroup')" name="second" :disabled="this.form.flow_main_id === 0">
            <!-- 属性组 -->
            <attrGroup :readonly=true :client_id_list="currentClientIdList" 
            :event_timer_main_id="this.form.event_timer_main_id" 
            :event_mod_timer_id="this.form.event_mod_timer_id" />
          </el-tab-pane>
        </el-tabs>
      </el-drawer>

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            highlight-current-row
            :height="height"
            @header-dragend="crud.tableHeaderDragend()"
            @row-click="handleRowClick"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="45" align="center" />
            <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
            <el-table-column v-if="1 == 0" width="10" prop="event_timer_main_id" label="id" />

            <el-table-column :show-overflow-tooltip="true" prop="event_mod_timer_code" label="Timer模版代码" width="280" />

            <el-table-column :show-overflow-tooltip="true" prop="station_id" :label="$t('lang_pack.mainmain.station')" width="280">
              <!-- 工位 -->
              <template slot-scope="scope">
                {{ getStationDes(scope.row.station_id) }}
              </template>
            </el-table-column>

            <el-table-column :show-overflow-tooltip="true" prop="event_timer_main_code" label="Timer事件编码" width="280" />
            <!-- Timer编码 -->
            <el-table-column :show-overflow-tooltip="true" prop="event_timer_main_des" label="TIMER事件描述" width="280" />
            <!-- Timer描述 -->

            <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" prop="enable_flag">
              <!-- 有效标识 -->
              <template slot-scope="scope">
                {{ dict.label.ENABLE_FLAG[scope.row.enable_flag] }}
              </template>
            </el-table-column>

            <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
          <!-- 实例弹窗的公用组件 -->
          <popupDialog ref="popupDialog" :obj.sync="exampleObj" @updateAdd="updateAdd" />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import { sel as selModTimer } from '@/api/core/event/eventTimerModMain'
import crudEventTimer from '@/api/core/event/eventTimerMain'
import { sel as selStation } from '@/api/core/factory/sysStation'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import conditionGroup from '@/views/core/flow/main/condition-group'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import popupDialog from '@/components/popupDialog/index'
import attrGroup from '@/views/core/event/mod/timer/attr-group'

const defaultForm = {
  event_timer_main_id: '',
  event_mod_timer_id: '',
  station_id: '',
  event_timer_main_code: '',
  event_timer_main_des: '',
  client_id_list: '',
  cycle_time: '',
  sync_flag: '',
  execute_count: 0,
  enable_flag: 'Y'
}
export default {
  name: 'RCS_EVENT_TIMER',
  components: {
    conditionGroup,
    crudOperation,
    rrOperation,
    udOperation,
    pagination,
    popupDialog,
    attrGroup
  },
  cruds() {
    return CRUD({
      title: 'TIMER_MAIN',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'event_timer_main_id',
      // 排序
      sort: ['event_timer_main_id asc'],

      query: { msg: 'YES' },

      // CRUD Method
      crudMethod: { ...crudEventTimer },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        down: false,
        reset: true
      }
    })
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      activeName: 'first',
      permission: {
        add: ['admin', 'rcs_flow_main:add'],
        edit: ['admin', 'rcs_flow_main:edit'],
        del: ['admin', 'rcs_flow_main:del'],
        down: ['admin', 'rcs_flow_main:down']
      },
      rules: {
        event_mod_timer_id: [{ required: true, message: '请选择事件模板', trigger: 'blur' }],
        station_id: [{ required: true, message: '请选择工位', trigger: 'blur' }],
        event_timer_main_code: [{ required: true, message: '请输入主TIMER事件编码', trigger: 'blur' }],
        event_timer_main_des: [{ required: true, message: '请输入主TIMER事件描述', trigger: 'blur' }],
        client_id_list: [{ required: true, message: '请选择实例集合', trigger: 'blur' }],
        sync_flag: [{ required: true, message: '请输入是否同步执行', trigger: 'blur' }],
        execute_count: [{ required: true, message: '请输入执行次数', trigger: 'blur' }],
        cycle_time: [{ required: true, message: '请输入轮询时间', trigger: 'blur' }]
      },
      customPopover: false,
      currentClientIdList: '',
      clientData: [],
      stationData: [],
      timerModMainData: [],
      exampleObj: {
        title: '实例集合',
        tableLable: [
          { prop: 'client_des', label: '描述' }
        ]
      }
    }
  },
  mounted: function() {
    const that = this
    that.$refs.table.doLayout()
    const query = {
      user_name: Cookies.get('userName'),
      sort: 'event_mod_timer_id',
      enable_flag: 'Y'
    }
    selModTimer(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.timerModMainData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created: function() {
    const query = {
      user_name: Cookies.get('userName'),
      sort: 'client_id',
      enable_flag: 'Y'
    }
    selStation(query)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.stationData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
  },
  methods: {
    handleRowClick(row, column, event) {
      this.form.event_mod_timer_id = row.event_mod_timer_id
      this.currentClientIdList = row.client_id_list
      this.form.event_timer_main_id = row.event_timer_main_id
    },
    // 获取角色的中文描述
    getStationDes(station_id) {
      var item = this.stationData.find(item => item.station_id === station_id)
      if (item !== undefined) {
        return item.station_code + ' ' + item.station_des
      }
      return station_id
    },

    // 开始 "新建/编辑" - 之前
    [CRUD.HOOK.beforeToCU](crud) {
      crud.form.client_id_list = crud.form.client_id_list.length === 0 ? '' : (crud.form.client_id_list.split(',')).toString()
      this.activeName = 'first'
      return true
    },
    updateAdd(value) {
      const list = []
      if (value.length > 0) {
        value.forEach((e) => {
          list.push(e.client_id)
        })
      }
      this.form.client_id_list = list.toString()
    }
  }
}
</script>
<style lang="scss">
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px;
}
.step-attr-dialog .el-dialog__body {
  padding: 0px;
  overflow-y: auto;
}
</style>

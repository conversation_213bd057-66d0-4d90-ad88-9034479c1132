import request from '@/utils/request'

// Hmi历史消息查询
export function sel(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeHmiShowHisSelect',
    method: 'post',
    data
  })
}

// Hmi当前消息查询（用于实时通知 - 原版本）
export function selCurrentMessage(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeHmiShowCurrentSelect',
    method: 'post',
    data
  })
}
 

// Hmi历史消息查询
export function selHmiMessagesNew(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeHmiShowHisSelectNew',
    method: 'post',
    data
  })
}

// 更新消息状态
export function updateMessageStatus(data) {
  return request({
    url: 'aisEsbWeb/eap/core/EapMeHmiShowUpdateStatus',
    method: 'post',
    data
  })
}

export default { sel, selCurrentMessage,  selHmiMessagesNew, updateMessageStatus }

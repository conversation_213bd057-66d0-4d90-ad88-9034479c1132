<template>
  <!--子菜单明细-->
  <el-card shadow="never">
    <el-drawer append-to-body :wrapper-closable="false" :title="crud.status.title" :visible="crud.status.cu > 0" :before-close="crud.cancelCU" size="650px">
      <el-tabs v-model="activeName" @tab-click="handletabsclick">
        <el-tab-pane label="基础属性" name="first">
          <el-form ref="form" class="el-form-wrap" :model="form" :rules="rules" size="small" label-width="110px" :inline="true">
            <el-form-item :label="$t('lang_pack.mqmain.eventModMq')" prop="event_mod_mq_sub_id">
              <!--子MQ事件模板 -->
              <el-select v-model="form.event_mod_mq_sub_id" filterable clearable>
                <el-option v-for="item in eventModMqSubs" :key="item.event_mod_mq_sub_id" :label="'modSubId:' + item.event_mod_mq_sub_id + ' ' + item.event_mod_mq_sub_des" :value="item.event_mod_mq_sub_id" />
              </el-select>
            </el-form-item>

            <el-form-item :label="$t('lang_pack.maintenanceMenu.mqSubDes')" prop="event_mq_sub_des">
            <!-- MQ事件描述 -->
            <el-input v-model="form.event_mq_sub_des" />
            </el-form-item>

            <el-form-item :label="$t('lang_pack.commonPage.validIdentificationt')">
              <!-- 有效标识 -->
              <fastCode fastcode_group_code="ENABLE_FLAG" :fastcode_code.sync="form.enable_flag" control_type="radio" size="mini" width="120px" />
            </el-form-item>
          </el-form>
          <el-divider />
          <div style="text-align: center">
            <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{ $t('lang_pack.commonPage.cancel') }}</el-button>
            <!-- 取消 -->
            <el-button type="primary" size="small" icon="el-icon-check" :loading="crud.status.cu === 2" @click="crud.submitCU">{{ $t('lang_pack.commonPage.confirm') }}</el-button>
            <!-- 确认 -->
          </div>
        </el-tab-pane>
        <el-tab-pane :label="$t('lang_pack.logicfunc.attributeGroup')" name="second" :disabled="this.form.event_mq_sub_id === 0">
          <div id="scrollbar1" :style="'height:' + height + 'px'">
            <el-scrollbar style="height: 100%">
              <attrGroup :event_mq_sub_id="this.form.event_mq_sub_id" :client_id_list="client_id_list" :readonly="readonly"
              :event_mod_mq_sub_id="this.form.event_mod_mq_sub_id" />
            </el-scrollbar>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-drawer>

    <el-row :gutter="20">
      <el-col :span="24" class="elTableItem">
        <!--表格渲染-->
        <el-table
          ref="table"
          v-loading="crud.loading"
          border
          size="small"
          :data="crud.data"
          style="width: 100%;"
          :height="height"
          highlight-current-row
          @header-dragend="crud.tableHeaderDragend()"
          @selection-change="crud.selectionChangeHandler"
          @row-click="handleRowClick"
        >
          <el-table-column type="selection" width="45" align="center" />
          <!-- 因为在想后台传数据时会用到当前行的id,所以将id隐藏了v-show="false" -->
          <el-table-column v-if="1 == 0" width="10" prop="event_mod_mq_sub_id" label="id" />
          <el-table-column v-if="1 == 0" width="10" prop="event_mod_mq_id" label="event_mod_mq_id" />
          <el-table-column :show-overflow-tooltip="true" prop="event_mq_sub_des" width="420" :label="$t('lang_pack.maintenanceMenu.mqSubDes')" />
          <!-- 子MQ事件 -->
          <el-table-column :label="$t('lang_pack.commonPage.validIdentificationt')" align="center" prop="enable_flag">
            <!-- 有效标识 -->
            <template slot-scope="scope">
              <!--取到当前单元格-->
              {{ scope.row.enable_flag === 'Y' ? '有效' : '无效' }}
            </template>
          </el-table-column>

          <el-table-column :label="$t('lang_pack.commonPage.operate')" align="center" width="120" fixed="right">
            <!-- 操作 -->
            <template slot-scope="scope">
              <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" />
            </template>
          </el-table-column>
        </el-table>
        <!--分页组件-->
        <pagination />
      </el-col>
    </el-row>
  </el-card>
</template>

<script>
import crudEventMqModSub from '@/api/core/event/eventMqMainSub'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import { sel as selModSub } from '@/api/core/event/eventMqModSub'
import pagination from '@crud/Pagination'
import attrGroup from '@/views/core/event/mod/mq/attr-group'
const defaultForm = {
  event_mq_sub_id: 0,
  event_mq_main_id: 0,
  event_mod_mq_sub_id: 0,
  event_mq_sub_des:'',
  enable_flag: 'Y'
}

export default {
  name: 'EVENT_MQ_MAIN_SUB',
  components: { crudOperation, rrOperation, udOperation, pagination, attrGroup },
  cruds() {
    return CRUD({
      title: '子MQ事件明细',
      // 登录用户
      userName: Cookies.get('userName'),
      // 菜单组ID
      query: { event_mq_main_id: '', event_mod_mq_id: '' },
      // 唯一字段
      idField: 'event_mq_sub_id',
      // 排序
      sort: ['event_mq_sub_id asc'],
      // CRUD Method
      crudMethod: { ...crudEventMqModSub },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      // 自定义属性
      props: {
        pageSize: 50
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  props: {
    event_mod_mq_id: {
      type: [String, Number],
      default: -1
    },
    event_mq_main_id: {
      type: [String, Number],
      default: -1
    },
    client_id_list: {
      type: [String],
      default: ''
    },
    readonly: {
      type: Boolean,
      default: false
    }
  },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 250,
      permission: {
        add: ['admin', 'rcs_event_mq_sub:add'],
        edit: ['admin', 'rcs_event_mq_sub:edit'],
        del: ['admin', 'rcs_event_mq_sub:del'],
        down: ['admin', 'rcs_event_mq_sub:down']
      },

      activeName: 'first',
      eventModMqSubs: [],
      selectValue: '',
      rules: {
        // 提交验证规则
        event_mq_sub_des: [
          { required: true, message: '请输入子MQ事件描述', trigger: 'blur' }
        ],
        event_mod_mq_sub_index: [
          { required: true, message: '请输入顺序', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    event_mq_main_id: {
      immediate: true,
      deep: true,
      handler() {
        this.query.event_mod_mq_id = this.event_mod_mq_id
        this.query.event_mq_main_id = this.event_mq_main_id
        this.crud.toQuery()
        this.getModMqSubs(this.query)
      }
    }
  },

  mounted: function() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 250
    }
  },

  methods: {
    getModMqSubs(query) {
      selModSub(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.eventModMqSubs = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: '查询异常',
            type: 'error'
          })
        })
    },

    handletabsclick() {
      // this.$refs.functionI.$refs.codeEditor._refresh()
    },
    handleRowClick(row, column, event) {
      this.form.event_mq_sub_id = row.event_mq_sub_id
      this.form.event_mod_mq_sub_id = row.event_mod_mq_sub_id
      this.selectValue = row.event_mq_main_id
    },

    // 提交前做的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      crud.form.event_mq_main_id = this.query.event_mq_main_id
      return true
    }

  }
}
</script>
<style lang="less" scoped>
.el-table {
  border-radius: 10px;
}
.el-card {
  border: 0 !important;
  overflow: inherit;
}
.box-card1 {
  min-height: calc(100vh - 260px);
}
</style>

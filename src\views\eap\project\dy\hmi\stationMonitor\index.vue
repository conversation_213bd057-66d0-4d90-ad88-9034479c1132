<template>
  <div id="loadMonitor">
    <el-container style="height: 100%;width: 100%;">
      <el-header>
        <div class="statuHead">
          <div>
            <el-button
              :class="monitorData.OnOffLine.value === '1' ? 'btnone' : 'btnone0'"
              type="success "
              @click="handleOnOffLineRoleCheck"
            >{{ monitorData.OnOffLine.value === '1' ? $t('lang_pack.hmiMain.onLine') : $t('lang_pack.hmiMain.offline') }}</el-button>
            <el-button
              :class="monitorData.SysModel.value === '1' ? 'btnone' : 'btnone0'"
              type="success "
              @click="handleSysModelRoleCheck"
            >{{ monitorData.SysModel.value === '1' ? $t('lang_pack.hmiMain.EAP') : $t('lang_pack.hmiMain.AIS') }}</el-button>
            <el-button
              :class="monitorData.PortOn1.value === '1' ? 'btnone' : 'btnone0'"
              type="success"
              @click="handlePortOnSwitchRoleCheck(1)"
            >{{ $t('lang_pack.hmiMain.prot1') }}{{ monitorData.PortOn1.value === '1' ? $t('lang_pack.hmiMain.enable') : $t('lang_pack.hmiMain.disabled') }}</el-button>
            <el-button
              :class="monitorData.PortOn2.value === '1' ? 'btnone' : 'btnone0'"
              type="success"
              @click="handlePortOnSwitchRoleCheck(2)"
            >{{ $t('lang_pack.hmiMain.prot2') }}{{ monitorData.PortOn2.value === '1' ? $t('lang_pack.hmiMain.enable') : $t('lang_pack.hmiMain.disabled') }}</el-button>

            <el-button
              :class="monitorData.Port1Status.value === '1' ? 'btnone' : 'btnone0'"
              type="success"
              @click="handleAgvSwitchRoleCheck(1)"
            >{{ monitorData.Port1Status.value === '1' ? $t('lang_pack.hmiMain.protAgv1') : $t('lang_pack.hmiMain.protMgv1') }}</el-button>
            <el-button
              :class="monitorData.Port2Status.value === '1' ? 'btnone' : 'btnone0'"
              type="success"
              @click="handleAgvSwitchRoleCheck(2)"
            >{{ monitorData.Port2Status.value === '1' ? $t('lang_pack.hmiMain.protAgv2') : $t('lang_pack.hmiMain.protMgv2') }}</el-button>

            <el-button class="btntwo" type="primary" @click="openManualPage">{{ $t('lang_pack.hmiMain.manual') }}</el-button>
          </div>
          <div>
            <div class="wrappstyle">
              <p><span
                   :class="controlStatus.unload_status === '1' ? 'wholeline wholelinenormal' :
                     (controlStatus.unload_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">{{ $t('lang_pack.hmiMain.reOnline') }}</span>
              </p>
              <p><span
                   :class="controlStatus.eap_status === '1' ? 'wholeline wholelinenormal' :
                     (controlStatus.eap_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">EAP</span>
              </p>
              <p><span
                   :class="controlStatus.plc_status === '1' ? 'wholeline wholelinenormal' :
                     (controlStatus.plc_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">PLC</span>
              </p>
              <p><span
                   :class="controlStatus.panel_status === '1' ? 'wholeline wholelinenormal' :
                     (controlStatus.panel_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">{{ $t('lang_pack.hmiMain.CCD') }}</span>
              </p>
              <p><span
                   :class="controlStatus.pallet1_status === '1' ? 'wholeline wholelinenormal' :
                     (controlStatus.pallet1_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">{{ $t('lang_pack.hmiMain.CCD1') }}</span>
              </p>
              <p><span
                   :class="controlStatus.pallet2_status === '1' ? 'wholeline wholelinenormal' :
                     (controlStatus.pallet2_status === '2' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">{{ $t('lang_pack.hmiMain.CCD2') }}</span>
              </p>
              <p><span
                   :class="monitorData.EndEQPAlive.value === '1' ? 'wholeline wholelinenormal' :
                     (monitorData.EndEQPAlive.value === '' ? 'wholeline wholelineerror' : 'wholeline wholelinegray')"
                 />
                <span class="statuText">{{ $t('lang_pack.hmiMain.DownStatus') }}</span>
              </p>
            </div>
          </div>
        </div>
        <transition name="el-zoom-in-center">
          <span v-show="messageShow" :class="'message message-' + MessageLevel"><i
            :class="MessageLevel === 'warning' ? 'el-icon-warning' : MessageLevel === 'error' ? 'el-icon-error' : 'el-icon-info'"
          />&nbsp;{{
            messageContent }}</span>
        </transition>
      </el-header>
      <el-main>
        <div class="peopleInfo">
          <ul>
            <li>
              <span>{{ $t('lang_pack.hmiMain.employee') }}</span>
              <span>{{ loginInfo.user_name }}</span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.name') }}</span>
              <span> {{ loginInfo.nick_name }}</span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.department') }}</span>
              <span>{{ loginInfo.dept_id }}</span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.status') }}</span>
              <span
                :class="monitorData.DeviceStatus.value === '1' ? 'deviceRun' :
                  monitorData.DeviceStatus.value === '2' ? 'deviceStop' :
                  monitorData.DeviceStatus.value === '3' ? 'deviceIdle' :
                  monitorData.DeviceStatus.value === '4' ? 'deviceDown' :
                  monitorData.DeviceStatus.value === '5' ? 'devicePm' :'deviceInit'"
                class="normal"
              >{{
                monitorData.DeviceStatus.value === '1' ? $t('lang_pack.hmiMain.deviceRun') :
                monitorData.DeviceStatus.value === '2' ? $t('lang_pack.hmiMain.deviceStop') :
                monitorData.DeviceStatus.value === '3' ? $t('lang_pack.hmiMain.deviceIdle') :
                monitorData.DeviceStatus.value === '4' ? $t('lang_pack.hmiMain.deviceDown') :
                monitorData.DeviceStatus.value === '5' ? $t('lang_pack.hmiMain.devicePm') :
                $t('lang_pack.hmiMain.deviceInit') }}</span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.production') }}</span>
              <span :class="monitorData.OnOffLine.value!=='1' ? 'lixian' : monitorData.SysModel.value === '1' ? 'zaixian' : 'lixian'"> {{
                monitorData.OnOffLine.value!=='1' ? $t('lang_pack.hmiMain.offline') :
                monitorData.SysModel.value === '1' ? $t('lang_pack.hmiMain.EAP') : $t('lang_pack.hmiMain.AIS') }}</span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.plate') }}</span>
              <span :class="monitorData.PanelModel.value === '1' ? 'zaixian' : 'lixian'">{{
                monitorData.PanelModel.value === '1' ? $t('lang_pack.hmiMain.panel') : $t('lang_pack.hmiMain.NoPanel') }}</span>
            </li>
            <li class="longString" />
            <li>
              <span>{{ $t('lang_pack.hmiMain.work') }}</span>
              <span>{{ monitorData.PlcWorkPortIndex.value || '0' }}</span>
            </li>
            <li class="lastLi">
              <el-button type="primary" @click="openUserLogin">{{ $t('lang_pack.hmiMain.Login') }}</el-button>
              <el-button type="danger" @click="handleUserLogout">{{ $t('lang_pack.hmiMain.LogOut') }}</el-button>
            </li>
          </ul>
        </div>
        <div class="wraptable NativeTable">
          <table class="table3 marginR">
            <tbody>
              <tr class="trtitle">
                <td class="tdName" colspan="4" style="width:50%"><span>{{ $t('lang_pack.hmiMain.prot1Step') }}</span></td>
                <td class="tdName" colspan="2"><span>{{ $t('lang_pack.hmiMain.prot1info') }}</span></td>
              </tr>
              <tr class="trtitle">
                <td colspan="2"><span>{{ stepInfo.port1.subDes }}</span></td>
                <td colspan="2"><span>{{ stepInfo.port1.stepDes }}</span></td>
                <td colspan="2" class="stepInfo">
                  <span :style="stepInfo.port1.logCode === 0 ? 'color:#67C273' : 'color:#DB0024'">
                      {{ stepInfo.port1.stepLog }}</span>
                  </td>
              </tr>
              <tr class="trtitle">
                <td colspan="2">
                  <table style="width:100%;height:100%;">
                    <tbody>
                      <tr class="trtitle">
                        <td class="tdName"><span>{{ $t('lang_pack.hmiMain.portStatus') }}</span></td>
                      </tr>
                      <tr class="trtitle">
                        <td><span>{{ monitorData.EapPortStatus1.value }}</span></td>
                      </tr>
                    </tbody>
                  </table>
                </td>
                <td colspan="2">
                  <table style="width:100%;height:100%;">
                    <tbody>
                      <tr class="trtitle">
                        <td class="tdName"><span>{{ $t('lang_pack.hmiMain.carrierStatus') }}</span></td>
                      </tr>
                      <tr class="trtitle">
                        <td><span>{{ monitorData.EapCarryStatus1.value }}</span></td>
                      </tr>
                    </tbody>
                  </table>
                </td>
                <td colspan="2" class="qianzhi w150"><el-button type="warning" @click="handlePortBackRequest(1)">{{ $t('lang_pack.hmiMain.forced') }}</el-button></td>
              </tr>
            </tbody>
          </table>
          <table class="table3">
            <tbody>
              <tr class="trtitle">
                <td class="tdName" colspan="4" style="width:50%"><span>{{ $t('lang_pack.hmiMain.prot2Step') }}</span></td>
                <td class="tdName" colspan="2"><span>{{ $t('lang_pack.hmiMain.prot2Step') }}</span></td>
              </tr>
              <tr class="trtitle">
                <td colspan="2"><span>{{ stepInfo.port2.subDes }}</span></td>
                <td colspan="2"><span>{{ stepInfo.port2.stepDes }}</span></td>
                <td colspan="2" class="stepInfo"><span :style="stepInfo.port2.logCode === 0 ? 'color:#67C273' : 'color:#DB0024'">{{
                  stepInfo.port2.stepLog }}</span></td>
              </tr>
              <tr class="trtitle">
                <td colspan="2">
                  <table style="width:100%;height:100%;">
                    <tbody>
                      <tr class="trtitle">
                        <td class="tdName"><span>{{ $t('lang_pack.hmiMain.portStatus') }}</span></td>
                      </tr>
                      <tr class="trtitle">
                        <td><span>{{ monitorData.EapPortStatus2.value }}</span></td>
                      </tr>
                    </tbody>
                  </table>
                </td>
                <td colspan="2">
                  <table style="width:100%;height:100%;">
                    <tbody>
                      <tr class="trtitle">
                        <td class="tdName"><span>{{ $t('lang_pack.hmiMain.carrierStatus') }}</span></td>
                      </tr>
                      <tr class="trtitle">
                        <td><span>{{ monitorData.EapCarryStatus2.value }}</span></td>
                      </tr>
                    </tbody>
                  </table>
                </td>
                <td colspan="2" class="qianzhi w150"><el-button type="warning" @click="handlePortBackRequest(2)">{{ $t('lang_pack.hmiMain.forced') }}</el-button></td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="wraptable NativeTable2">
          <table class="table3 marginR" style="width:49.9%;">
            <tbody>
              <tr class="trtitle">
                <td class="tdName wd"><span>{{ $t('lang_pack.taskTable.batchNumber') }}</span></td>
                <td class="wdban" colspan="2"><span> {{ portTaskInfo.lot_num }}</span></td>
                <td class="tdName wd"><span>{{ $t('lang_pack.taskTable.area') }}</span></td>
                <td class="wdban"><span class="jianju">{{ portTaskInfo.face_code }}</span></td>
                <td class="tdName wd"><span>{{ $t('lang_pack.taskTable.NoBowPlate') }}</span></td>
                <td class="wdban"><span>{{ portTaskInfo.inspect_flag }}</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName wd"><span>{{ $t('lang_pack.hmiMain.complete') }}</span></td>
                <td colspan="2"><span>{{ portTaskInfo.finish_ok_count }}</span>/<span>{{ portTaskInfo.plan_lot_count
                }}</span></td>
                <td class="tdName wd"><span>NG</span></td>
                <td class="wdban"><span class="jianju">{{ portTaskInfo.finish_ng_count }}</span></td>
                <td class="tdName wd"><span>{{ $t('lang_pack.hmiMain.firstPlan') }}</span></td>
                <td class="wdban"><span>{{ portTaskInfo.inspect_finish_count }}</span>/<span>{{
                  portTaskInfo.inspect_count
                }}</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName wd"><span>{{ $t('lang_pack.hmiMain.workMode') }}</span></td>
                <td class="wdban" colspan="2"><span>{{ portTaskInfo.pdb_rule }}</span></td>
                <td class="tdName wd"><span>{{ $t('lang_pack.hmiMain.LWH') }}</span></td>
                <td class="wdban" colspan="4"><span>{{ portTaskInfo.panel_length }}/{{ portTaskInfo.panel_width }}/{{
                  portTaskInfo.panel_tickness }}</span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName wd"><span>{{ $t('lang_pack.hmiMain.vehicle') }}</span></td>
                <td colspan="2"><span>{{ portTaskInfo.pallet_num }}</span></td>
                <td class="tdName wd"><span>{{ $t('lang_pack.hmiMain.Tray') }}</span></td>
                <td colspan="4"><span>
                  {{ monitorData.PlcWorkPortIndex.value === '1' ? monitorData.PalletCcd1.value :
                    monitorData.PalletCcd2.value
                  }}
                </span></td>
              </tr>
              <tr class="trtitle">
                <td class="tdName wd"><span>{{ $t('lang_pack.hmiMain.plateCode') }}</span></td>
                <td colspan="8"><span>{{ monitorData.PanelCcd.value }}</span></td>
              </tr>
            </tbody>
          </table>
          <div class="showImg">
            <img v-if="ccdImage" :src="ccdImage" alt="">
            <img v-else src="@/assets/images/hmiLoginLeft.gif" alt="">
          </div>
          <div class="indicatorlstyle">
            <div id="indicatorr" class="indicatorrone" />
          </div>
        </div>
        <div class="wraptable NativeTable3">
          <el-table
            border
            size="small"
            :data="moTableData"
            style="width: 100%;margin-right: 5px;"
            height="450"
            :highlight-current-row="true"
          >
            <!-- <el-table-column prop="group_lot_num" :label="$t('lang_pack.hmiMain.fmale')" width="300" /> -->
            <el-table-column prop="lot_num" :label="$t('lang_pack.taskTable.batchNumber')" width="280" />
            <el-table-column prop="material_code" :label="$t('lang_pack.workOrder.materialNumber')" width="220" />
            <el-table-column prop="port_code" :label="$t('lang_pack.hmiMain.blowOff')" width="70" />
            <el-table-column prop="plan_lot_count" :label="$t('lang_pack.hmiMain.plan')" width="70" />
            <el-table-column prop="finish_count" :label="$t('lang_pack.hmiMain.put')" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.finish_ok_count }}/{{ scope.row.finish_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="task_status" :label="$t('lang_pack.hmiMain.putStatus')" width="125">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.task_status2 == '生产中' ? '' : (scope.row.task_status2 == '正常完板' ? 'success' : 'danger'))"
                  class="elTag"
                >
                  {{ scope.row.task_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="sb_finish_count" :label="$t('lang_pack.hmiMain.received')" width="180">
              <template slot-scope="scope">
                <span>{{ scope.row.sb_finish_ok_count }}/{{ scope.row.sb_finish_count }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="sb_port_code" :label="$t('lang_pack.hmiMain.close')" width="80" />
            <el-table-column prop="sb_task_status" :label="$t('lang_pack.hmiMain.receivingState')" width="120">
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.sb_task_status2 == '未同步' ? 'info' : (scope.row.sb_task_status2 == '计划中' ? '' : (scope.row.sb_task_status2 == '生产中' ? '' : (scope.row.sb_task_status2 == '正常完板' ? 'success' : 'danger'))))"
                  class="elTag"
                >
                  {{ scope.row.sb_task_status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
          <el-table
            border
            :data="tableData"
            style="width: 100%"
            height="450"
          >
            <el-table-column prop="panel_index" width="80" :label="$t('lang_pack.hmiMain.serial')" />
            <el-table-column prop="panel_barcode" :label="$t('lang_pack.hmiMain.plateCode')" width="300" />
            <!-- <el-table-column prop="tray_barcode" :label="$t('lang_pack.hmiMain.Tray')" /> -->
            <el-table-column prop="inspect_flag" :label="$t('lang_pack.hmiMain.firstPiece')" width="100" />
            <el-table-column prop="panel_status" :label="$t('lang_pack.hmiMain.panelStatus')" width="120" >
              <template slot-scope="scope">
                <el-tag
                  :type="(scope.row.panel_status == 'NG' ? 'danger' : (scope.row.panel_status == 'OK' ? 'success' : 'warning'))"
                  class="elTag"
                >
                  {{ scope.row.panel_status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="panel_ng_msg" :label="$t('lang_pack.hmiMain.abnormal')" />
            <el-table-column prop="item_date" :label="$t('lang_pack.hmiMain.time')" width="130" />
            <el-table-column prop="dummy_flag" :label="$t('lang_pack.hmiMain.sideboard')" width="100" style="border-right:1px solid red !important" />
          </el-table>
        </div>
      </el-main>
    </el-container>
    <el-dialog :title="$t('lang_pack.vie.employeeLogin')" :visible.sync="userDialogVisible" width="650px" top="65px" class="elDialog dialog_hmi">
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.employee') }}
      <el-input ref="userId" v-model="userId" clearable size="mini" style="width:100%" @input="handleInput" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.name') }}
      <el-input ref="nickName" v-model="nickName" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.hmiMain.department') }}
      <el-input ref="deptId" v-model="deptId" clearable size="mini" style="width:100%" />
      <i class="el-icon-tickets" />{{ $t('lang_pack.vie.classes') }}
      <el-input ref="shiftId" v-model="shiftId" clearable size="mini" style="width:100%" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="userDialogVisible = false">{{ $t('lang_pack.vie.cancel') }}</el-button>
        <el-button type="primary" @click="handleUserLogin">{{ $t('lang_pack.SignOn') }}</el-button>
      </span>
    </el-dialog>
    <el-dialog :title="$t('lang_pack.hmiMain.manual')" :visible.sync="manualDialogVisible" width="850px" top="65px" class="elDialog dialog_hmi">
      <div style="margin-bottom:10px;">
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('pallet_manual_scan')"
        >{{ $t('lang_pack.hmiMain.vehicleInput') }}</el-button>
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('manual_lot_scan')"
        >{{ $t('lang_pack.hmiMain.manualBatch') }}</el-button>
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('manual_order')"
        >{{ $t('lang_pack.hmiMain.localTask') }}</el-button>
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('inspect_confirm')"
        >{{ $t('lang_pack.hmiMain.firstCheck') }}</el-button>
      </div>
      <div style="margin-bottom:10px;">
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('panel_manual_scan')"
        >{{ $t('lang_pack.hmiMain.panelInput') }}</el-button>
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('panel_manual_judge')"
        >{{ $t('lang_pack.hmiMain.panelJudge') }}</el-button>
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('change_recipe')"
        >{{ $t('lang_pack.hmiMain.confirmation') }}</el-button>
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('wip_manual_finish_count')"
        >{{ $t('lang_pack.hmiMain.forFinish') }}</el-button>
      </div>
      <div>
        <el-button
          type="primary"
          style="line-height:30px;font-size:24px;"
          plain
          @click="handleManualEvent('wip_manual_noread_count')"
        >{{ $t('lang_pack.hmiMain.forFinish2') }}</el-button>
        <el-button
          style="line-height:30px;font-size:24px;color:white;background: #ed2106;"
          type="primary"
          plain
          @click="handleClearSession"
        >{{ $t('lang_pack.hmiMain.btnClearSession') }}</el-button>
        <el-button
          class="btntwo"
          type="primary"
          @click="sendEapPingTest"
        >{{ $t('lang_pack.hmiMain.eapPing') }}
        </el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="msgDialogTitle"
      :visible.sync="mgsDialogVisible"
      :modal-append-to-body="false"
      width="800px"
      top="65px"
      :close-on-click-modal="false"
      class="elDialog dialog_hmi"
      @close="handleCloseDialog"
    >
      <palletManualScan
        v-if="palletManualScanShow"
        ref="palletManualScan"
        :tag_key_list="tagKeyList"
        :dy-check-code="DyCheckCode"
        @sendMessage="sendMessage"
      />
      <manualLotList
        v-if="manualLotListShow"
        ref="manualLotList"
        :tag_key_list="tagKeyList"
        :dy-check-code="DyCheckCode"
        @sendMessage="sendMessageByUpEap"
      />
      <manualOrder v-if="manualOrderShow" ref="manualOrder" :tag_key_list="tagKeyList" :dy-check-code="DyCheckCode" @sendMessage="sendMessage" />
      <inspectConfirm
        v-if="inspectConfirmShow"
        ref="inspectConfirm"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessage"
      />

      <panelManualScan
        v-if="panelManualScanShow"
        ref="panelManualScan"
        :tag_key_list="tagKeyList"
        :dy-check-code="DyCheckCode"
        @sendMessage="sendMessageByCheck"
      />
      <manualPanelStatus
        v-if="panelManualConfirmShow"
        ref="panelManualConfirm"
        class="manualPanelStatus"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessageByCheck"
      />
      <manualFinishCount
        v-if="manualFinishCountShow"
        ref="manualFinishCount"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessage"
      />
      <manualNoReadCount
        v-if="manualNoReadCountShow"
        ref="manualNoReadCount"
        :tag_key_list="tagKeyList"
        @sendMessage="sendMessage"
      />
      <changeRecipe v-if="changeRecipeShow" ref="changeRecipe" :tag_key_list="tagKeyList" @sendMessage="sendMessage" />
    </el-dialog>
    <el-dialog :title="$t('lang_pack.hmiMain.CIMMessage')" width="50%" top="20px" :visible.sync="dialogCIMMsgVisible" :close-on-click-modal="false" class="elDialog">
      <table class="table">
        <tr>
          <td class="label" style="width:100px;">{{ $t('lang_pack.messageReport.screen_code') + '：' }}</td>
          <td class="content">{{ screen_code }}</td>
        </tr>
        <tr>
          <td class="label">{{ $t('lang_pack.messageReport.cim_msg') + '：' }}</td>
          <td class="content">{{ cim_msg }}</td>
        </tr>
        <tr v-if="ConfirmBtnVisible">
          <td colspan="2" style="text-align:center;">
            <el-button
              class="filter-item"
              size="mini"
              type="primary"
              icon="el-icon-check"
              @click="handleConfirmCIMMsg"
            >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
          </td>
        </tr>
      </table>
    </el-dialog>
    <el-dialog :title="$t('lang_pack.Prompt')" width="50%" top="20px" :visible.sync="warningMsgDialogVisible" :close-on-click-modal="false" class="elDialog">
      <span>{{ tagValueSecond + $t('lang_pack.hmiMain.inSeconds') }}</span>
    </el-dialog>
    <el-dialog :title="$t('lang_pack.Prompt')" width="50%" top="20px" :visible.sync="jsonMsgFlag" :close-on-click-modal="false" class="elDialog">
      <span>{{ jsonMsg }}</span>
    </el-dialog>
    <roleCheck
      v-if="roleCheckShow"
      ref="roleCheck"
      class="userinfo_hmi"
      :role_user_id="userId"
      :role_func_code="role_func_code"
      @roleCheck="roleCheck"
    />
  </div>
</template>

<script>
import '@/assets/styles/trigger.scss'
import autofit from 'autofit.js'
import { selCellIP } from '@/api/core/center/cell'
import { selLoginInfo, userLogin, userLogout } from '@/api/eap/project/dy/eapDyMeStationUser'
import { carrierIDReport, eapPing, dockingModeChangeRequest, cimModeChangeReport } from '@/api/eap/project/dy/eapDyApsPlan'
import { stationTaskSel, loadCurrentTaskSel, unLoadStatusSel, eapCimMsgShow, eapApsPlanCancel, eapApsJudgeIsCanAbort } from '@/api/eap/eapApsPlan'
import { sel as selSysParameter } from '@/api/core/system/sysParameter'
import Cookies from 'js-cookie'
import roleCheck from '@/views/core/hmi/roleCheck'
import manualOrder from '@/views/eap/project/dy/loadMonitor/manualOrder'
import changeRecipe from '@/views/eap/core/loadMonitor/changeRecipe'
import palletManualScan from '@/views/eap/core/loadMonitor/palletManualScan'
import inspectConfirm from '@/views/eap/core/loadMonitor/inspectConfirm'
import panelManualScan from '@/views/eap/core/loadMonitor/panelManualScan'
import panelManualConfirm from '@/views/eap/core/loadMonitor/panelManualConfirm'
import manualLotList from '@/views/eap/project/dy/loadMonitor/manualLotList'
import manualFinishCount from '@/views/eap/core/loadMonitor/manualFinishCount'
import manualPanelStatus from '@/views/eap/project/dy/loadMonitor/manualPanelStatus'
import manualNoReadCount from '@/views/eap/project/dy/loadMonitor/manualNoReadCount'

import axios from 'axios'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
import tooltipShow from 'roc-tooltip-show'
export default {
  name: 'EAP_LOAD_MONITOR_HMI',
  components: { manualOrder, changeRecipe, palletManualScan, inspectConfirm, panelManualScan, panelManualConfirm, manualLotList, roleCheck, manualFinishCount, manualPanelStatus, manualNoReadCount },
  // 数据模型
  data() {
    return {
      timer: '',
      timer1: '',
      height: document.documentElement.clientHeight - 340,
      prodLineData: [],
      stationData: [],
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      manualDialogVisible: false,
      userDialogVisible: false,
      userId: '',
      nickName: '',
      deptId: '',
      shiftId: '',
      role_func_code: '',
      select_port_index: '',
      loginInfo: {
        user_name: '',
        nick_name: '',
        dept_id: '',
        shift_id: ''
      },
      portTaskInfo: {},
      port1TaskInfo: {},
      port2TaskInfo: {},
      messageList: [],
      stationFlowTaskList: [], // 工位流程任务列表
      messageContent: '',
      MessageLevel: 'info',
      messageShow: false,
      roleCheckShow: false,
      // CIM消息相关
      queryCim: true,
      dialogCIMMsgVisible: false,
      ConfirmBtnVisible: false,
      screen_code: '',
      cim_msg: '',
      // 监控弹窗相关
      msgDialogTitle: this.$t('lang_pack.Prompt'),
      mgsDialogVisible: false,
      manualOrderShow: false,
      changeRecipeShow: false,
      palletManualScanShow: false,
      inspectConfirmShow: false,
      panelManualScanShow: false,
      panelManualConfirmShow: false,
      manualLotListShow: false,
      manualFinishCountShow: false,
      manualNoReadCountShow: false,
      tagKeyList: {},
      currentFuncCode: '',
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random()
            .toString(16)
            .substr(2, 8), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      // 启用监听数据模式 AIS-PC=电脑模式,AIS-SERVER=服务器模式
      // AIS-SERVER模式，监听的Client Code需要拼接上工位编码,例如：LoadPlc_OP1010
      aisMonitorMode: 'AIS-PC',
      dyVersion: '2',
      // 监听数据
      monitorData: {
        OnOffLine: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'OnOffLine', tag_des: '[PlcConfig]在线/离线模式', value: '' },
        DeviceStatus: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'DeviceStatus', tag_des: '[PlcStatus]设备状态(1运行、2停止 、3待料、4异常、5保养)', value: '' },
        SysModel: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'SysModel', tag_des: '[PlcConfig]AIS本地/EAP远程模式切换', value: '' },
        PanelModel: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'PanelModel', tag_des: '[PlcConfig]Panel模式(1有/0无)', value: '' },
        PortOn1: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'PortOn1', tag_des: '[PlcConfig]端口1启用状态(1启用/0禁用)', value: '' },
        PortOn2: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'PortOn2', tag_des: '[PlcConfig]端口2启用状态(1启用/0禁用)', value: '' },
        Port1Status: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'Port1Status', tag_des: '[PlcConfig]端口1搬运模式(1自动/0手动)', value: '' },
        Port2Status: { client_code: 'LoadPlc', group_code: 'PlcConfig', tag_code: 'Port2Status', tag_des: '[PlcConfig]端口2搬运模式(1自动/0手动)', value: '' },
        PortStatus1: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'Port1Status', tag_des: '[PlcStatus]端口1状态', value: '' },
        PortStatus2: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'Port2Status', tag_des: '[PlcStatus]端口2状态', value: '' },
        PanelCcd: { client_code: 'LoadPanelCcd', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[板件CCD]条码', value: '' },
        PalletCcd1: { client_code: 'LoadEap', group_code: 'EapStatus', tag_code: 'PalletNum1', tag_des: '[载具CCD1]条码', value: '' },
        PalletCcd2: { client_code: 'LoadEap', group_code: 'EapStatus', tag_code: 'PalletNum2', tag_des: '[载具CCD2]条码', value: '' },
        TrayCcd1: { client_code: 'LoadTrayCcd1', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[天盖CCD1]条码', value: '' },
        TrayCcd2: { client_code: 'LoadTrayCcd2', group_code: 'CcdStatus', tag_code: 'ReadBarCode', tag_des: '[天盖CCD2]条码', value: '' },
        AisPort1BackRequest: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'AisPort1BackRequest', tag_des: '[PlcStatus]AIS请求端口1强制退载具', value: '' },
        AisPort2BackRequest: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'AisPort2BackRequest', tag_des: '[PlcStatus]AIS请求端口2强制退载具', value: '' },
        PlcWorkPortIndex: { client_code: 'LoadPlc', group_code: 'PlcStatus', tag_code: 'PlcWorkPortIndex', tag_des: '[PlcStatus]当前作业端口', value: '' },
        EapPortStatus1: { client_code: 'LoadEap', group_code: 'EapStatus', tag_code: 'PortStatus1', tag_des: '[EapStatus]端口1状态', value: '' },
        EapCarryStatus1: { client_code: 'LoadEap', group_code: 'EapStatus', tag_code: 'CarryStatus1', tag_des: '[EapStatus]载具1状态', value: '' },
        EapPortStatus2: { client_code: 'LoadEap', group_code: 'EapStatus', tag_code: 'PortStatus2', tag_des: '[EapStatus]端口2状态', value: '' },
        EapCarryStatus2: { client_code: 'LoadEap', group_code: 'EapStatus', tag_code: 'CarryStatus2', tag_des: '[EapStatus]载具2状态', value: '' },
        EapApiTimeOutSeconds: { client_code: 'LoadAis', group_code: 'AisConfig', tag_code: 'EapApiTimeOutSeconds', tag_des: '[AisConfig]调用EAP的接口超时时间', value: '' },
        EndEQPAlive: { client_code: 'LoadDownDevice', group_code: 'DownStatus', tag_code: 'EndEQPAlive', tag_des: '[下游状态]下游EQP Alive', value: '' }
      },
      manualOrder: { client_code: 'LoadAis', WebLotNum: 'AisStatus/WebLotNum', WebLotPortCode: 'AisStatus/WebLotPortCode', WebLotRequest: 'AisStatus/WebLotRequest' },
      changeRecipe: { client_code: 'LoadAis', WebChangeRecipeFinish: 'AisStatus/WebChangeRecipeFinish' },
      palletManualScan: { client_code: 'LoadAis', WebPalletNum: 'AisStatus/WebPalletNum', WebPalletConfirmModel: 'AisStatus/WebPalletConfirmModel', WebPalletInfoRequest: 'AisStatus/WebPalletInfoRequest' },
      inspectConfirm: { client_code: 'LoadAis', WebInspectConfirmStatus: 'AisStatus/WebInspectConfirmStatus' },
      panelManualScan: { client_code: 'LoadAis', WebPanelNum: 'AisStatus/WebPanelNum', WebPanelletConfirmModel: 'AisStatus/WebPanelletConfirmModel', WebPanelInfoRequest: 'AisStatus/WebPanelInfoRequest' },
      panelManualConfirm: { client_code: 'LoadAis', WebPanelNum: 'AisStatus/WebPanelNum', WebPanelletConfirmModel: 'AisStatus/WebPanelletConfirmModel', WebPanelInfoRequest: 'AisStatus/WebPanelInfoRequest', WebPanelMsg: '' },
      manualLotList: { client_code: 'LoadAis', WebLotNum: 'AisStatus/WebLotNum', WebLotPortCode: 'AisStatus/WebLotPortCode', WebLotRequest: 'AisStatus/WebLotRequest', PalletNum: '', PortCode: '' },
      manualFinishCountList: { client_code: 'LoadAis', WebWipShortCount: 'AisStatus/WebWipShortCount', WebWipNoReadCount: 'AisStatus/WebWipNoReadCount', WebWipManual: 'AisStatus/WebWipManual', WebWipPlanCount: '' },

      tableData: [],
      moTableData: [],
      ccdImage: '',
      // 步序信息
      stepInfo: {
        port1: { subDes: '', stepDes: '', stepLog: '', logCode: 0 },
        port2: { subDes: '', stepDes: '', stepLog: '', logCode: 0 }
      },
      showMsg: true, // 是否提示消息
      indicatorrOption: {
        tooltip: {
          trigger: 'item',
          'textStyle': {
            'fontSize': 24
          }
        },
        legend: {
          top: '3%',
          'textStyle': {
            'fontSize': 24
          }
        },
        color: ['#79a0f1', '#fac958'],
        series: [
          {
            label: {
              show: true,
              position: 'inside',
              formatter(ar) {
                return ar.value
              }
            },
            type: 'pie',
            radius: '65%',
            // 饼图位置参数
            center: ['50%', '55%'],
            data: [
              { value: '0', name: this.$t('lang_pack.hmiMain.dailyOnline') },
              { value: '0', name: this.$t('lang_pack.hmiMain.dailyOffLine') }
            ],
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      },
      indicatorr: null,
      // 控制器状态与心跳
      controlStatus: {
        eap_status: '0',
        plc_status: '0',
        panel_status: '0',
        pallet1_status: '0',
        pallet2_status: '0',
        unload_status: '0'
      },
      loadeapStatus: '',
      tagValueSecond: 0,
      tagValueTimer: null,
      warningMsgDialogVisible: false,
      delayCount: 0,
      jsonMsgFlag: false,
      jsonMsg: '',
      DyCheckCode: '' // 判断定颖手动画面输入框长度限制
    }
  },
  mounted: function() {
    autofit.init({
      designHeight: 1080,
      designWidth: 1920,
      renderDom: '#loadMonitor',
      resize: true
    }, false) // 可关闭控制台运行提示输出
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 340
    }
    this.timer = setInterval(this.messageScroll, 5000)
    this.timer1 = setInterval(this.getStationTaskInfo, 5000)
    // 在通过mounted调用即可
    this.echartsInit()
  },
  created: function() {
    // 获取系统参数信息
    var queryParameter = {
      userName: Cookies.get('userName'),
      cell_id: '0',
      parameter_code: 'AIS_MONITOR_MODE',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.aisMonitorMode = defaultQuery.data[0].parameter_val
          }
        }
      }).catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.queryException'),
          type: 'error'
        })
      })
      // 查询定颖的版本
    var queryParameter2 = {
      userName: Cookies.get('userName'),
      cell_id: '0',
      parameter_code: 'Dy_Version',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter2)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.dyVersion = defaultQuery.data[0].parameter_val
          }
        }
      }).catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.queryException'),
          type: 'error'
        })
      })
    var queryParameter3 = {
      userName: Cookies.get('userName'),
      cell_id: '0',
      parameter_code: 'DyCheckCode',
      enable_flag: 'Y'
    }
    selSysParameter(queryParameter3)
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data !== '') {
            this.DyCheckCode = defaultQuery.data[0].parameter_val
          }
        }
      }).catch(() => {
        this.$message({
          message: this.$t('lang_pack.vie.queryException'),
          type: 'error'
        })
      })
    this.getStationData()
  },
  // 离开此页面时销毁mqtt链接
  beforeDestroy() {
    clearInterval(this.timer)
    clearInterval(this.timer1)
    if (this.clientMqtt) {
      this.clientMqtt.end()
      this.mqttConnStatus = false
    }
  },
  methods: {
    handleInput(e) {
      if (this.DyCheckCode) {
        if (e.length === 16) {
          this.$message({ type: 'info', message: `员工号长度不能超过16位` })
          return
        }
        if (e.length > 16) {
          this.$message({ type: 'error', message: `员工号长度大于16位,已清空` })
          this.userId = ''
          return
        }
      }
    },
    // 初始化echarts
    echartsInit() {
      var that = this
      this.indicatorr = this.$echarts.init(document.getElementById('indicatorr'))
      // 用法示例
      const options = {
        interval: 2000,
        loopSeries: false,
        seriesIndex: 0,
        updateData: null
      }
      tooltipShow(this.indicatorr, this.indicatorrOption, options)
      window.addEventListener('resize', function() {
        that.indicatorr.resize()
      })
      this.indicatorr.setOption(this.indicatorrOption)
    },
    getStationData() {
      this.currentStation = {
        prod_line_id: this.$route.query.prod_line_id,
        prod_line_code: '',
        prod_line_des: '',
        station_id: this.$route.query.station_id,
        station_code: this.$route.query.station_code,
        station_des: '',
        cell_id: this.$route.query.cell_id
      }
      this.getCellIp()
      this.getLoginInfo()
      this.getStationTaskInfo()
    },
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: this.currentStation.cell_id,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            this.toStartWatch()
            this.getTagValue()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    messageScroll() {
      if (this.messageList.length > 0) {
        this.messageShow = false
        setTimeout(() => {
          this.messageShow = true
          this.messageList.push(this.messageList[0]) // 将数组的第一个元素追加到数组最后面
          this.messageList.shift() // 然后删除数组的第一个元素
          this.messageContent = this.messageList[0].content
          this.MessageLevel = this.messageList[0].level
        }, 300)
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      if (this.queryCim) {
        eapCimMsgShow(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              if (defaultQuery.count !== 0) {
                var msgInfo = defaultQuery.data[0]
                this.screen_code = msgInfo.screen_code
                this.cim_msg = msgInfo.cim_msg
                var interval_second_time = msgInfo.interval_second_time
                if (msgInfo.screen_control === '0') {
                  this.ConfirmBtnVisible = false
                  var _time = setTimeout(() => {
                    this.queryCim = true
                    this.dialogCIMMsgVisible = false
                    clearTimeout(_time)
                  }, interval_second_time * 1000)
                } else {
                  this.ConfirmBtnVisible = true
                }
                this.queryCim = false
                this.dialogCIMMsgVisible = true
              }
            }
          })
          .catch(() => {
          })
      }
      this.getLoginInfo()
    },
    // 初始化读取Redis Tag值
    getTagValue() {
      var readTagArray = []
      Object.keys(this.monitorData).forEach(key => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        if (this.aisMonitorMode === 'AIS-SERVER') {
          client_code = client_code + '_' + this.currentStation.station_code
        }
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
        // path = 'http://*************:8089' + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code = client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter(item => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value = item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    },
    // 获取当前登录信息
    getLoginInfo() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      selLoginInfo(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const loginInfo = defaultQuery.data[0]
              this.loginInfo.user_name = loginInfo.user_name
              this.loginInfo.nick_name = loginInfo.nick_name
              this.loginInfo.dept_id = loginInfo.dept_id
              this.loginInfo.shift_id = loginInfo.shift_id
            } else {
              this.loginInfo.user_name = '---'
              this.loginInfo.nick_name = '---'
              this.loginInfo.dept_id = '---'
              this.loginInfo.shift_id = '---'
            }
          } else {
            this.loginInfo.user_name = '---'
            this.loginInfo.nick_name = '---'
            this.loginInfo.dept_id = '---'
            this.loginInfo.shift_id = '---'
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.loginInfo.user_name = '---'
          this.loginInfo.nick_name = '---'
          this.loginInfo.dept_id = '---'
          this.loginInfo.shift_id = '---'
          this.$message({ message: this.$t('lang_pack.hmiMain.procedure'), type: 'error' })
        })
    },
    // 获取工位当前任务计划
    getStationTaskInfo() {
      if (this.currentStation.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        work_port_index: this.monitorData.PlcWorkPortIndex.value,
        on_off_value: this.monitorData.OnOffLine.value
      }
      stationTaskSel(query)
        .then(res => {
          this.portTaskInfo = ''
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.result !== '') {
              const resultData = JSON.parse(defaultQuery.result)
              this.tableData = resultData.planDInfo
              if (resultData.planSumInfo.length > 0) {
                const sumInfo = resultData.planSumInfo[0]
                this.indicatorrOption.series[0].data[0].value = sumInfo.online_sum_count
                this.indicatorrOption.series[0].data[1].value = sumInfo.offline_sum_count
                this.indicatorr.setOption(this.indicatorrOption)
              } else {
                this.indicatorrOption.series[0].data[0].value = 0
                this.indicatorrOption.series[0].data[1].value = 0
                this.indicatorr.setOption(this.indicatorrOption)
              }
              if (resultData.planInfo.length > 0) {
                const taskInfo = resultData.planInfo[0]
                var item = {}
                item.lot_num = taskInfo.lot_num
                item.material_code = taskInfo.material_code
                item.face_code = taskInfo.face_code === 2 ? this.$t('lang_pack.hmiMain.reverse') : this.$t('lang_pack.hmiMain.side')
                item.pdb_rule = taskInfo.pdb_rule === 0 ? this.$t('lang_pack.hmiMain.productionBoard') : this.$t('lang_pack.hmiMain.dummy')
                item.inspect_count = taskInfo.inspect_count
                item.inspect_flag = taskInfo.inspect_count === 0 ? this.$t('lang_pack.hmiMain.isNo') : this.$t('lang_pack.hmiMain.have')
                item.inspect_finish_count = taskInfo.inspect_finish_count
                item.plan_lot_count = taskInfo.plan_lot_count
                item.finish_count = taskInfo.finish_count
                item.finish_ng_count = taskInfo.finish_ng_count
                item.pallet_num = taskInfo.pallet_num
                item.panel_length = taskInfo.panel_length
                item.panel_width = taskInfo.panel_width
                item.panel_tickness = taskInfo.panel_tickness
                this.portTaskInfo = item
              } else {
                this.portTaskInfo = {}
              }
            } else {
              this.portTaskInfo = {}
              this.tableData = []
              this.indicatorrOption.series[0].data[0].value = 0
              this.indicatorrOption.series[0].data[1].value = 0
              this.indicatorr.setOption(this.indicatorrOption)
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          // this.$message({ message: '获取工位当前任务计划异常', type: 'error' })
        })

      this.getFlowTaskData()
      this.getCurrentTaskInfo()
      this.getUnLoadStatusInfo()
    },
    // 获取最近任务列表
    getCurrentTaskInfo() {
      if (this.currentStation.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      loadCurrentTaskSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data !== '') {
              const resultData = defaultQuery.data
              this.moTableData = resultData
            } else {
              this.moTableData = []
            }
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          // this.$message({ message: '获取工位当前任务计划异常', type: 'error' })
        })
    },
    // 获取离线状态
    getUnLoadStatusInfo() {
      if (this.currentStation.station_id === '') {
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id
      }
      unLoadStatusSel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.result !== '') {
              const resultData = JSON.parse(defaultQuery.result)
              var unload_onoff_value = resultData.unload_onoff_value
              if (unload_onoff_value === '1') {
                this.controlStatus.unload_status = '1'
              } else {
                this.controlStatus.unload_status = '2'
              }
            }
          }
        })
        .catch(() => {
        })
    },

    // 获取流程步序相关信息
    getFlowTaskData() {
      if (this.cellIp === '' || this.webapiPort === '') {
        return
      }
      this.stepInfo = {
        port1: { subDes: '', stepDes: '', stepLog: '', logCode: 0 },
        port2: { subDes: '', stepDes: '', stepLog: '', logCode: 0 }
      }
      var method = '/cell/core/flow/CoreFlowTaskListSelect'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      const data = {
        station_id: this.currentStation.station_id
      }

      //加入语言传递
      var langSel='zh-CN'
      const language = ['zh-CN', 'en-US', 'zh-TW', 'th']
      if (language.includes(localStorage.getItem('language'))) {
        langSel = localStorage.getItem('language') || 'zh-CN'
      }

      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json',
            'Ais-Languages':langSel
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.data.code === 0 && defaultQuery.data.data !== '') {
            const flowTaskList = defaultQuery.data.data
            if (flowTaskList.length > 0) {
              this.stationFlowTaskList = flowTaskList
              const port1LoadBackFlow = flowTaskList.filter(item => item.flow_main_des.indexOf('PORT1') >= 0)
              if (port1LoadBackFlow.length > 0) {
                this.stepInfo.port1.subDes = port1LoadBackFlow[0].flow_mod_sub_des
                this.stepInfo.port1.stepDes = port1LoadBackFlow[0].step_mod_des
                this.stepInfo.port1.stepLog = port1LoadBackFlow[0].log_msg
                this.stepInfo.port1.logCode = port1LoadBackFlow[0].log_code
              }
              // const port1LoadPlaceFlow = flowTaskList.filter(item => item.flow_main_des.indexOf('PORT1') >= 0 && item.flow_main_des.indexOf('放板流程') >= 0)
              // if (port1LoadPlaceFlow.length > 0) {
              //  this.stepInfo.port1.subDes = port1LoadPlaceFlow[0].flow_mod_sub_des
              //  this.stepInfo.port1.stepDes = port1LoadPlaceFlow[0].step_mod_des
              //  this.stepInfo.port1.stepLog = port1LoadPlaceFlow[0].log_msg
              //  this.stepInfo.port1.logCode = port1LoadPlaceFlow[0].log_code
              // }

              const port2LoadBackFlow = flowTaskList.filter(item => item.flow_main_des.indexOf('PORT2') >= 0)
              if (port2LoadBackFlow.length > 0) {
                this.stepInfo.port2.subDes = port2LoadBackFlow[0].flow_mod_sub_des
                this.stepInfo.port2.stepDes = port2LoadBackFlow[0].step_mod_des
                this.stepInfo.port2.stepLog = port2LoadBackFlow[0].log_msg
                this.stepInfo.port2.logCode = port2LoadBackFlow[0].log_code
              }
              // const port2LoadPlaceFlow = flowTaskList.filter(item => item.flow_main_des.indexOf('PORT2') >= 0 && item.flow_main_des.indexOf('放板流程') >= 0)
              // if (port2LoadPlaceFlow.length > 0) {
              //  this.stepInfo.port2.subDes = port2LoadPlaceFlow[0].flow_mod_sub_des
              //  this.stepInfo.port2.stepDes = port2LoadPlaceFlow[0].step_mod_des
              //  this.stepInfo.port2.stepLog = port2LoadPlaceFlow[0].log_msg
              //  this.stepInfo.port2.logCode = port2LoadPlaceFlow[0].log_code
              // }
            }
          }
        })
        .catch(ex => {
          this.flowTaskData = []
          this.$message({ message: this.$t('lang_pack.vie.queryException') + '：' + ex, type: 'error' })
        })
    },
    // 打开登录操作
    openUserLogin() {
      this.userId = ''
      this.userDialogVisible = true
      this.$nextTick(x => {
        // 正确写法
        this.$refs.userId.focus()
      })
    },
    // 处理登录
    handleUserLogin() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        user_code: this.userId,
        dept_id: this.deptId,
        shift_id: this.shiftId,
        nick_name: this.nickName,
        check_out_flag: 'N'
      }
      // 倒计时功能
      this.delayCount = 45
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: 'Data Processing（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = 'Data Processing（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      userLogin(query)
        .then(res => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const loginInfo = defaultQuery.data[0]
            this.loginInfo.user_name = loginInfo.user_name
            this.loginInfo.nick_name = loginInfo.nick_name
            this.loginInfo.dept_id = loginInfo.dept_id
            this.loginInfo.shift_id = loginInfo.shift_id
            this.userDialogVisible = false
            this.$message({ message: this.$t('lang_pack.vie.loginSuccess'), type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
          if (defaultQuery.code === -1) {
            this.tagValueSecond = Number(this.monitorData['EapApiTimeOutSeconds'].value)
            if (this.tagValueSecond > 0) {
              this.tagValueTimer && clearInterval(this.tagValueTimer)
              this.warningMsgDialogVisible = true
              this.timer = setInterval(() => {
                this.tagValueSecond--
                if (this.tagValueSecond === 0) {
                  this.warningMsgDialogVisible = false
                  clearInterval(this.timer)
                  this.tagValueTimer = null
                }
              }, 1000)
              return
            }
          }
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 处理登出
    handleUserLogout() {
      this.$confirm(this.$t('lang_pack.vie.AreYouSureToLogOut'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          debugger
          const query = {
            user_name: Cookies.get('userName'),
            station_id: this.currentStation.station_id,
            user_code: this.loginInfo.user_name,
            dept_id: '',
            shift_id: '',
            nick_name: '',
            check_out_flag: 'Y'
          }

          // 倒计时功能
          this.delayCount = 45
          const ths = this
          const loading = this.$loading({
            lock: true,
            text: 'Data Processing（' + this.delayCount + '）',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          })
          this.timer3 = setInterval(function() {
            ths.delayCount--
            loading.text = 'Data Processing（' + ths.delayCount + '）'
            if (ths.delayCount === 0) {
              loading.close()
              clearInterval(this.timer3)
            }
          }, 1000)

          userLogout(query)
            .then(res => {
              // 清除计时器
              this.delayCount = 0
              loading.close()
              clearInterval(this.timer3)

              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.loginInfo.user_name = ''
                this.loginInfo.nick_name = ''
                this.loginInfo.dept_id = ''
                this.loginInfo.shift_id = ''
                this.$message({ message: this.$t('lang_pack.hmiMain.logoutSuccess'), type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
            })
            .catch(() => {
              // 清除计时器
              this.delayCount = 0
              loading.close()
              clearInterval(this.timer3)
              this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
            })
        })
        .catch(() => {
        })
    },
    // 打开手动操作页面
    openManualPage() {
      this.manualDialogVisible = true
    },
    // 处理手动操作事件
    handleManualEvent(type) {
      var station_code = ''
      if (this.aisMonitorMode === 'AIS-SERVER') {
        station_code = '_' + this.currentStation.station_code
      }
      if (type === 'manual_order') {
        this.tagKeyList = {
          WebLotNum: this.manualOrder.client_code + station_code + '/' + this.manualOrder.WebLotNum,
          WebLotPortCode: this.manualOrder.client_code + station_code + '/' + this.manualOrder.WebLotPortCode,
          WebLotRequest: this.manualOrder.client_code + station_code + '/' + this.manualOrder.WebLotRequest
        }
        // 先清空工单号和请求信号置0
        var sendJson = {}
        var rowJson = []
        var newRow = {
          TagKey: this.tagKeyList.WebLotNum,
          TagValue: ''
        }
        rowJson.push(newRow)
        newRow = {
          TagKey: this.tagKeyList.WebLotRequest,
          TagValue: '0'
        }
        rowJson.push(newRow)
        sendJson.Data = rowJson
        sendJson.ClientName = 'SCADA_WEB'
        var sendStr = JSON.stringify(sendJson)
        var topic = 'SCADA_WRITE/' + this.tagKeyList.WebLotNum.split('/')[0]
        this.showMsg = false
        this.sendMessage(topic, sendStr)
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.workOrderEntry')
        this.manualOrderShow = true
      } else if (type === 'change_recipe') {
        this.tagKeyList = {
          WebChangeRecipeFinish: this.changeRecipe.client_code + station_code + '/' + this.changeRecipe.WebChangeRecipeFinish
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.clickOK')
        this.changeRecipeShow = true
      } else if (type === 'pallet_manual_scan') {
        this.tagKeyList = {
          WebPalletNum: this.palletManualScan.client_code + station_code + '/' + this.palletManualScan.WebPalletNum,
          WebPalletConfirmModel: this.palletManualScan.client_code + station_code + '/' + this.palletManualScan.WebPalletConfirmModel,
          WebPalletInfoRequest: this.palletManualScan.client_code + station_code + '/' + this.palletManualScan.WebPalletInfoRequest
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.palletInput')
        this.palletManualScanShow = true
      } else if (type === 'inspect_confirm') {
        this.tagKeyList = {
          WebInspectConfirmStatus: this.inspectConfirm.client_code + station_code + '/' + this.inspectConfirm.WebInspectConfirmStatus
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.firstCheck')
        this.inspectConfirmShow = true
      } else if (type === 'panel_manual_scan') {
        this.tagKeyList = {
          WebPanelNum: this.panelManualScan.client_code + station_code + '/' + this.panelManualScan.WebPanelNum,
          WebPanelletConfirmModel: this.panelManualScan.client_code + station_code + '/' + this.panelManualScan.WebPanelletConfirmModel,
          WebPanelInfoRequest: this.panelManualScan.client_code + station_code + '/' + this.panelManualScan.WebPanelInfoRequest,
          WebCheckFlag: 'Y'
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.panelInput')
        this.panelManualScanShow = true
      } else if (type === 'panel_manual_confirm') {
        this.tagKeyList = {
          WebPanelNum: this.panelManualConfirm.client_code + station_code + '/' + this.panelManualConfirm.WebPanelNum,
          WebPanelletConfirmModel: this.panelManualConfirm.client_code + station_code + '/' + this.panelManualConfirm.WebPanelletConfirmModel,
          WebPanelInfoRequest: this.panelManualConfirm.client_code + station_code + '/' + this.panelManualConfirm.WebPanelInfoRequest
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.mixedBatch')
        this.panelManualConfirmShow = true
      } else if (type === 'panel_manual_judge') {
        this.tagKeyList = {
          WebPanelNum: this.panelManualConfirm.client_code + station_code + '/' + this.panelManualConfirm.WebPanelNum,
          WebPanelletConfirmModel: this.panelManualConfirm.client_code + station_code + '/' + this.panelManualConfirm.WebPanelletConfirmModel,
          WebPanelInfoRequest: this.panelManualConfirm.client_code + station_code + '/' + this.panelManualConfirm.WebPanelInfoRequest,
          WebPanelMsg: ''
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.panelJudge')
        this.panelManualConfirmShow = true
      } else if (type === 'manual_lot_scan') {
        this.tagKeyList = {
          WebLotNum: this.manualLotList.client_code + station_code + '/' + this.manualLotList.WebLotNum,
          WebLotPortCode: this.manualLotList.client_code + station_code + '/' + this.manualLotList.WebLotPortCode,
          WebLotRequest: this.manualLotList.client_code + station_code + '/' + this.manualLotList.WebLotRequest,
          PalletNum: '',
          PortCode: ''
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.forValidation')
        this.manualLotListShow = true
      } else if (type === 'wip_manual_finish_count') {
        this.tagKeyList = {
          WebWipShortCount: this.manualFinishCountList.client_code + station_code + '/' + this.manualFinishCountList.WebWipShortCount,
          WebWipNoReadCount: this.manualFinishCountList.client_code + station_code + '/' + this.manualFinishCountList.WebWipNoReadCount,
          WebWipManual: this.manualFinishCountList.client_code + station_code + '/' + this.manualFinishCountList.WebWipManual
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.forFinish')
        this.manualFinishCountShow = true
      } else if (type === 'wip_manual_noread_count') {
        this.tagKeyList = {
          WebWipShortCount: this.manualFinishCountList.client_code + station_code + '/' + this.manualFinishCountList.WebWipShortCount,
          WebWipNoReadCount: this.manualFinishCountList.client_code + station_code + '/' + this.manualFinishCountList.WebWipNoReadCount,
          WebWipManual: this.manualFinishCountList.client_code + station_code + '/' + this.manualFinishCountList.WebWipManual
        }
        this.msgDialogTitle = this.$t('lang_pack.hmiMain.forFinish')
        this.manualNoReadCountShow = true
      }
      this.mgsDialogVisible = true
    },
    // 处理端口强制退载具
    handlePortBackRequest(portIndex) {
      // 泰国定颖3.0需要验证用户登入信息
      if (this.dyVersion === '3') {
        if (this.loginInfo.user_name === '---' || this.loginInfo.user_name === '') {
          this.$message({ message: 'please Login In First', type: 'warning' })
          return
        }
      }
      this.$confirm(this.$t('lang_pack.hmiMain.Identify') + portIndex.toString() + this.$t('lang_pack.hmiMain.mandatory'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          var tag_key = ''
          var client_code = ''
          var group_code = ''
          var tag_code = ''
          if (portIndex === 1) {
            client_code = this.monitorData.AisPort1BackRequest.client_code
            group_code = this.monitorData.AisPort1BackRequest.group_code
            tag_code = this.monitorData.AisPort1BackRequest.tag_code
          } else if (portIndex === 2) {
            client_code = this.monitorData.AisPort2BackRequest.client_code
            group_code = this.monitorData.AisPort2BackRequest.group_code
            tag_code = this.monitorData.AisPort2BackRequest.tag_code
          }
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          tag_key = client_code + '/' + group_code + '/' + tag_code
          var sendJson = {}
          var rowJson = []
          var newRow = {
            TagKey: tag_key,
            TagValue: '1'
          }
          rowJson.push(newRow)
          sendJson.Data = rowJson
          sendJson.ClientName = 'SCADA_WEB'
          var sendStr = JSON.stringify(sendJson)
          var topic = 'SCADA_WRITE/' + client_code
          this.sendMessage(topic, sendStr)
        })
        .catch(() => {
        })
    },

    //清空任务与缓存
    handleClearSession() {
      this.$confirm(this.$t('lang_pack.hmiMain.ClearSession'), this.$t('lang_pack.vie.prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.vie.cancel'),
        type: 'warning'
      })
        .then(() => {
          const query = {
              user_name: Cookies.get('userName'),
              station_id: this.currentStation.station_id
          }
          eapApsPlanCancel(query)
              .then(res => {
              const defaultQuery = JSON.parse(JSON.stringify(res))
              if (defaultQuery.code === 0) {
                this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
              } else {
                this.$message({ message: defaultQuery.msg, type: 'error' })
              }
              })
              .catch(() => {
                this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
              })
        })
        .catch(() => {
        })
    },

    // 端口状态切换权限验证
    handlePortOnSwitchRoleCheck(portIndex) {
      var tag_value = ''
      if (portIndex === 1) {
        tag_value = this.monitorData.PortOn1.value
      } else if (portIndex === 2) {
        tag_value = this.monitorData.PortOn2.value
      }
      if (tag_value === '1') {
        const query = {
          user_name: Cookies.get('userName'),
          station_id: this.currentStation.station_id,
          work_port_index: portIndex
        }
        eapApsJudgeIsCanAbort(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              // 允许操作
              this.select_port_index = portIndex
              this.role_func_code = 'handlePortOnSwitch'
              this.roleCheckShow = true
            } else {
              this.$message({ message: defaultQuery.msg, type: 'error' })
            }
          })
          .catch(() => {
            this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
          })
      } else {
        this.handlePortOnSwitch(portIndex)
      }
    },
    // 处理端口状态切换
    handlePortOnSwitch(portIndex) {
      var tag_key = ''
      var client_code = ''
      var group_code = ''
      var tag_code = ''
      var tag_value = ''
      if (portIndex === 1) {
        client_code = this.monitorData.PortOn1.client_code
        group_code = this.monitorData.PortOn1.group_code
        tag_code = this.monitorData.PortOn1.tag_code
        tag_value = this.monitorData.PortOn1.value === '1' ? '0' : '1'
      } else if (portIndex === 2) {
        client_code = this.monitorData.PortOn2.client_code
        group_code = this.monitorData.PortOn2.group_code
        tag_code = this.monitorData.PortOn2.tag_code
        tag_value = this.monitorData.PortOn2.value === '1' ? '0' : '1'
      }
      if (this.aisMonitorMode === 'AIS-SERVER') {
        client_code = client_code + '_' + this.currentStation.station_code
      }
      tag_key = client_code + '/' + group_code + '/' + tag_code

      // 先做判断
      var DeviceOnReadyTag = client_code + '/PlcStatus/DeviceOnReady'// 设备启动就绪(设备线上模式且启动)
      var readTagArray = []
      var readTag = {}
      readTag.tag_key = DeviceOnReadyTag
      readTagArray.push(readTag)
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              var DeviceOnReadyValue = result[0].tag_value === undefined ? '' : result[0].tag_value

              // 取消对设备状态的判断
              // if (DeviceOnReadyValue === '1') {
              //  this.$message({
              //    message: this.$t('lang_pack.hmiMain.machineProduction'),
              //    type: 'error'
              //  })
              //  return
              // }

              // 再写入
              var sendJson = {}
              var rowJson = []
              var newRow = {
                TagKey: tag_key,
                TagValue: tag_value
              }
              rowJson.push(newRow)
              sendJson.Data = rowJson
              sendJson.ClientName = 'SCADA_WEB'
              var sendStr = JSON.stringify(sendJson)
              var topic = 'SCADA_WRITE/' + client_code
              this.sendMessage(topic, sendStr)
              // 如果切成离线模式，取消执行取消流程图的接口，根据工位号和端口号查询所有执行中的流程图，全部取消
              if (tag_value === '0') {
                var flowTaskList = []
                if (this.stationFlowTaskList != null && this.stationFlowTaskList.length > 0) {
                  if (portIndex === 1) {
                    flowTaskList = this.stationFlowTaskList.filter(item => item.flow_main_des.indexOf('PORT1') >= 0)
                  } else if (portIndex === 2) {
                    flowTaskList = this.stationFlowTaskList.filter(item => item.flow_main_des.indexOf('PORT2') >= 0)
                  }
                  if (flowTaskList != null && flowTaskList.length > 0) {
                    flowTaskList.forEach(val => {
                      var method = '/cell/core/flow/CoreFlowCancel'
                      var path = ''
                      if (process.env.NODE_ENV === 'development') {
                        path = 'http://localhost:' + this.webapiPort + method
                      } else {
                        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                      }
                      const data = {
                        me_flow_task_id: val.me_flow_task_id,
                        flow_task_status: 'AUTO_CANCEL'
                      }
                      axios
                        .post(path, data, {
                          headers: {
                            'Content-Type': 'application/json'
                          }
                        })
                        .then(res => {
                          const defaultQuery = JSON.parse(JSON.stringify(res))
                          if (defaultQuery.data.code === 0) {
                            this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                          }
                        })
                        .catch(ex => {
                          this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + ':' + ex, type: 'error' })
                        })
                    })
                  }
                }
              }
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('lang_pack.hmiMain.abnormalError') + '：' + ex, type: 'error' })
        })
    },

    // AGV与MGV模式切换权限验证
    handleAgvSwitchRoleCheck(portIndex) {
      // 从MGV切AGV不需要密码
      var tag_value = ''
      if (portIndex === 1) {
        tag_value = this.monitorData.Port1Status.value
      } else if (portIndex === 2) {
        tag_value = this.monitorData.Port2Status.value
      }
      this.$confirm('Change Agv Mode?', this.$t('lang_pack.vie.prompt'), {
          confirmButtonText: this.$t('lang_pack.vie.determine'),
          cancelButtonText: this.$t('lang_pack.vie.cancel'),
          type: 'warning'
      })
      .then(() => {
          if (tag_value === '0') {
            this.handleAgvSwitch(portIndex)
          } else {
            this.handleAgvSwitch(portIndex)
            //不需要权限
            //this.select_port_index = portIndex
            //this.role_func_code = 'handleAgvSwitch'
            //this.roleCheckShow = true
          }
      })
      .catch(() => {
      })
    },
    // AGV与MGV模式切换
    handleAgvSwitch(portIndex) {
      var tag_value = ''
      var port_code = ''
      var dock_mode = ''
      if (portIndex === 1) {
        tag_value = this.monitorData.Port1Status.value
        port_code = '01'
      } else if (portIndex === 2) {
        tag_value = this.monitorData.Port2Status.value
        port_code = '02'
      }
      if (tag_value === '1') {
        dock_mode = 'MGV'
      } else {
        dock_mode = 'AGV'
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_code: this.currentStation.station_code,
        port_code: port_code,
        dock_mode: dock_mode
      }
      dockingModeChangeRequest(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            // 允许操作
            var tag_key = ''
            var client_code = ''
            var group_code = ''
            var tag_code = ''
            var tag_value2 = ''
            if (portIndex === 1) {
              client_code = this.monitorData.Port1Status.client_code
              group_code = this.monitorData.Port1Status.group_code
              tag_code = this.monitorData.Port1Status.tag_code
              tag_value2 = this.monitorData.Port1Status.value === '1' ? '0' : '1'
            } else {
              client_code = this.monitorData.Port2Status.client_code
              group_code = this.monitorData.Port2Status.group_code
              tag_code = this.monitorData.Port2Status.tag_code
              tag_value2 = this.monitorData.Port2Status.value === '1' ? '0' : '1'
            }
            if (this.aisMonitorMode === 'AIS-SERVER') {
              client_code = client_code + '_' + this.currentStation.station_code
            }
            tag_key = client_code + '/' + group_code + '/' + tag_code
            // 再写入
            var sendJson = {}
            var rowJson = []
            var newRow = {
              TagKey: tag_key,
              TagValue: tag_value2
            }
            rowJson.push(newRow)
            sendJson.Data = rowJson
            sendJson.ClientName = 'SCADA_WEB'
            var sendStr = JSON.stringify(sendJson)
            var topic = 'SCADA_WRITE/' + client_code
            this.sendMessage(topic, sendStr)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 需要包裹一层
    handleSysModelSwitchPre() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        station_code: this.currentStation.station_code,
        station_attr: 'Load',
        auto_value: this.monitorData.OnOffLine.value,
        model_value: this.monitorData.SysModel.value === '1' ? '0' : '1'
      }
      // 倒计时功能
      this.delayCount = 45
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: 'Data Processing（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = 'Data Processing（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)
      // 请求是否允许切换
      cimModeChangeReport(query)
        .then(res => {
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            // 允许操作
            this.handleSysModelSwitch()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 处理本地远程状态切换
    handleSysModelSwitch() {
      var tag_key = ''
      var client_code = this.monitorData.SysModel.client_code
      var group_code = this.monitorData.SysModel.group_code
      var tag_code = this.monitorData.SysModel.tag_code
      var tag_value = this.monitorData.SysModel.value === '1' ? '0' : '1'
      if (this.aisMonitorMode === 'AIS-SERVER') {
        client_code = client_code + '_' + this.currentStation.station_code
      }
      tag_key = client_code + '/' + group_code + '/' + tag_code
      // 再写入
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: tag_value
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + client_code
      this.sendMessage(topic, sendStr)
    },

    // 切在线离线时权限验证
    handleOnOffLineRoleCheck() {
      if (this.monitorData.OnOffLine.value === '1') {
        const query = {
          user_name: Cookies.get('userName'),
          station_id: this.currentStation.station_id,
          work_port_index: ''
        }
        eapApsJudgeIsCanAbort(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              // 允许操作
              this.role_func_code = 'handleOnOffLine'
              this.roleCheckShow = true
            } else {
              this.$message({ message: defaultQuery.msg, type: 'error' })
            }
          })
          .catch(() => {
            this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
          })
      } else {
        this.handleOnOffLineSwitchPre()
      }
    },
    // 切本地远程时权限验证
    handleSysModelRoleCheck() {
      if (this.monitorData.OnOffLine.value === '1' && this.monitorData.SysModel.value === '1') {
        const query = {
          user_name: Cookies.get('userName'),
          station_id: this.currentStation.station_id,
          work_port_index: ''
        }
        eapApsJudgeIsCanAbort(query)
          .then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              // 允许操作
              this.role_func_code = 'handleSysModel'
              this.roleCheckShow = true
            } else {
              this.$message({ message: defaultQuery.msg, type: 'error' })
            }
          })
          .catch(() => {
            this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
          })
      } else {
        this.handleSysModelSwitchPre()
      }
    },

    // 权限验证共用方法
    roleCheck(roleFuncCode, status) {
      this.roleCheckShow = false
      if (roleFuncCode === 'handleOnOffLine') {
        if (status === 'OK') {
          this.handleOnOffLineSwitchPre()
        }
      } else if (roleFuncCode === 'handleSysModel') {
        if (status === 'OK') {
          this.handleSysModelSwitchPre()
        }
      } else if (roleFuncCode === 'handlePortOnSwitch') {
        if (status === 'OK') {
          this.handlePortOnSwitch(this.select_port_index)
        }
      } else if (roleFuncCode === 'handleAgvSwitch') {
        if (status === 'OK') {
          this.handleAgvSwitch(this.select_port_index)
        }
      }
    },
    // 需要包裹一层
    handleOnOffLineSwitchPre() {
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        station_code: this.currentStation.station_code,
        station_attr: 'Load',
        auto_value: this.monitorData.OnOffLine.value === '1' ? '0' : '1',
        model_value: this.monitorData.SysModel.value
      }
      // 倒计时功能
      this.delayCount = 45
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: 'Data Processing（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = 'Data Processing（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)
      // 请求是否允许切换
      cimModeChangeReport(query)
        .then(res => {
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            // 允许操作
            this.handleOnOffLineSwitch()
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 处理在线离线模式切换
    handleOnOffLineSwitch() {
      var tag_key = ''
      var tag_key_sysmodel = ''
      var client_code = ''
      var client_code_sysmodel = ''
      var group_code = ''
      var tag_code = ''
      var tag_value = ''
      client_code = this.monitorData.OnOffLine.client_code
      group_code = this.monitorData.OnOffLine.group_code
      tag_code = this.monitorData.OnOffLine.tag_code
      tag_value = this.monitorData.OnOffLine.value === '1' ? '0' : '1'
      client_code_sysmodel = this.monitorData.SysModel.client_code

      if (this.aisMonitorMode === 'AIS-SERVER') {
        client_code = client_code + '_' + this.currentStation.station_code
        client_code_sysmodel = client_code_sysmodel + '_' + this.currentStation.station_code
      }
      tag_key = client_code + '/' + group_code + '/' + tag_code
      tag_key_sysmodel = client_code_sysmodel + '/' + this.monitorData.SysModel.group_code + '/' + this.monitorData.SysModel.tag_code

      // 先做判断
      var DeviceOnReadyTag = client_code + '/PlcStatus/DeviceOnReady'// 设备启动就绪(设备线上模式且启动)
      var readTagArray = []
      var readTag = {}
      readTag.tag_key = DeviceOnReadyTag
      readTagArray.push(readTag)
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              var DeviceOnReadyValue = result[0].tag_value === undefined ? '' : result[0].tag_value

              // 定颖取消对生产状态的判断
              // if (DeviceOnReadyValue === '1') {
              //  this.$message({
              //    message: this.$t('lang_pack.hmiMain.machineProduction'),
              //    type: 'error'
              //  })
              //  return
              // }

              // 再写入
              var sendJson = {}
              var rowJson = []
              var newRow = {
                TagKey: tag_key,
                TagValue: tag_value
              }
              rowJson.push(newRow)
              sendJson.Data = rowJson
              sendJson.ClientName = 'SCADA_WEB'
              var sendStr = JSON.stringify(sendJson)
              var topic = 'SCADA_WRITE/' + client_code
              this.sendMessage(topic, sendStr)
              // 若是在线切离线,且为EAP远程模式,则也需要同步将EAP远程模式切换到AIS本地模式
              if (tag_value === '0' && this.monitorData.SysModel.value === '1' && this.dyVersion !== '3') {
                sendJson = {}
                rowJson = []
                newRow = {
                  TagKey: tag_key_sysmodel,
                  TagValue: '0'
                }
                rowJson.push(newRow)
                sendJson.Data = rowJson
                sendJson.ClientName = 'SCADA_WEB'
                sendStr = JSON.stringify(sendJson)
                topic = 'SCADA_WRITE/' + client_code_sysmodel
                this.sendMessage(topic, sendStr)
              }

              // 如果切成离线模式，取消执行取消流程图的接口，根据工位号查询所有执行中的流程图，全部取消
              if (tag_value === '0') {
                // 1.取消任务与缓存
                const query = {
                  user_name: Cookies.get('userName'),
                  station_id: this.currentStation.station_id
                }
                eapApsPlanCancel(query)
                  .then(res => {
                    const defaultQuery = JSON.parse(JSON.stringify(res))
                    if (defaultQuery.code === 0) {
                    } else {
                      this.$message({ message: defaultQuery.msg, type: 'error' })
                    }
                  })
                  .catch(() => {
                    this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
                  })

                // 2.取消流程图
                if (this.stationFlowTaskList != null && this.stationFlowTaskList.length > 0) {
                  this.stationFlowTaskList.forEach(val => {
                    var method = '/cell/core/flow/CoreFlowCancel'
                    var path = ''
                    if (process.env.NODE_ENV === 'development') {
                      path = 'http://localhost:' + this.webapiPort + method
                    } else {
                      path = 'http://' + this.cellIp + ':' + this.webapiPort + method
                    }
                    const data = {
                      me_flow_task_id: val.me_flow_task_id,
                      flow_task_status: 'AUTO_CANCEL'
                    }
                    axios
                      .post(path, data, {
                        headers: {
                          'Content-Type': 'application/json'
                        }
                      })
                      .then(res => {
                        const defaultQuery = JSON.parse(JSON.stringify(res))
                        if (defaultQuery.data.code === 0) {
                          this.$message({ message: this.$t('lang_pack.hmiMain.cancelFlowchart'), type: 'warn' })
                        }
                      })
                      .catch(ex => {
                        this.$message({ message: this.$t('lang_pack.hmiMain.cancelException') + '：' + ex, type: 'error' })
                      })
                  })
                }
              }
            }
          }
        })
        .catch(ex => {
          this.$message({ message: this.$t('lang_pack.hmiMain.abnormalError') + '：' + ex, type: 'error' })
        })
    },
    // ----------------------------------【MQTT】----------------------------------
    // 启动监控
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', e => {
        this.mqttConnStatus = true
        // 当MQTT连接成功后，注册CLIENT相关TOPIC
        this.topicSubscribe('AISWEB_MSG/' + this.currentStation.station_code)
        // 注册监控CCD照片
        this.topicSubscribe('SCADA_CHANGE/Ccd/CcdStatus/PanelPic')
        // 监控通讯状态
        var eapClientCode = 'LoadEap'
        var plcClientCode = 'LoadPlc'
        var panelCcdClientCode = 'LoadPanelCcd'
        var palletCcd1ClientCode = 'LoadPalletCcd11'
        var palletCcd2ClientCode = 'LoadPalletCcd21'
        if (this.aisMonitorMode === 'AIS-SERVER') {
          eapClientCode = eapClientCode + '_' + this.currentStation.station_code
          plcClientCode = plcClientCode + '_' + this.currentStation.station_code
          panelCcdClientCode = panelCcdClientCode + '_' + this.currentStation.station_code
          palletCcd1ClientCode = palletCcd1ClientCode + '_' + this.currentStation.station_code
          palletCcd2ClientCode = palletCcd2ClientCode + '_' + this.currentStation.station_code
        }
        this.topicSubscribe('SCADA_STATUS/' + eapClientCode)
        this.topicSubscribe('SCADA_BEAT/' + eapClientCode)
        this.topicSubscribe('SCADA_STATUS/' + plcClientCode)
        this.topicSubscribe('SCADA_BEAT/' + plcClientCode)
        this.topicSubscribe('SCADA_STATUS/' + panelCcdClientCode)
        this.topicSubscribe('SCADA_BEAT/' + panelCcdClientCode)
        this.topicSubscribe('SCADA_STATUS/' + palletCcd1ClientCode)
        this.topicSubscribe('SCADA_BEAT/' + palletCcd1ClientCode)
        this.topicSubscribe('SCADA_STATUS/' + palletCcd2ClientCode)
        this.topicSubscribe('SCADA_BEAT/' + palletCcd2ClientCode)
        // 其他标准点位监控
        Object.keys(this.monitorData).forEach(key => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          if (this.aisMonitorMode === 'AIS-SERVER') {
            client_code = client_code + '_' + this.currentStation.station_code
          }
          this.topicSubscribe('SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code)
        })
        this.$message({
          message: this.$t('lang_pack.vie.cnneSuccess'),
          type: 'success'
        })
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('lang_pack.vie.cnneFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('lang_pack.vie.connDisconRecon'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      this.clientMqtt.on('close', () => {
        // this.clientMqtt.end()
        // this.$message({
        //  message: '服务连接断开',
        //  type: 'error'
        // })
      })
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return

          if (topic.indexOf('AISWEB_MSG/' + this.currentStation.station_code) >= 0) {
            this.handleMessage(jsonData)
          } else if (topic.indexOf('SCADA_BEAT/') >= 0) { // 心跳
            var heartBeatValue = jsonData.Beat
            if (topic.indexOf('SCADA_BEAT/LoadEap') >= 0) {
              if (this.controlStatus.eap_status !== '2') {
                this.controlStatus.eap_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPlc') >= 0) {
              if (this.controlStatus.plc_status !== '2') {
                this.controlStatus.plc_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPanelCcd') >= 0) {
              if (this.controlStatus.panel_status !== '2') {
                this.controlStatus.panel_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPalletCcd11') >= 0) {
              if (this.controlStatus.pallet1_status !== '2') {
                this.controlStatus.pallet1_status = heartBeatValue
              }
            } else if (topic.indexOf('SCADA_BEAT/LoadPalletCcd21') >= 0) {
              if (this.controlStatus.pallet2_status !== '2') {
                this.controlStatus.pallet2_status = heartBeatValue
              }
            }
          } else if (topic.indexOf('SCADA_STATUS/') >= 0) { // 通讯结果状态
            var statusValue = jsonData.Status
            if (topic.indexOf('SCADA_STATUS/LoadEap') >= 0) {
              this.loadeapStatus = jsonData.Status

              if (statusValue === '0') {
                this.controlStatus.eap_status = '2'
                this.$message({ message: this.$t('lang_pack.hmiMain.EAPCommunication'), type: 'error' })
              } else {
                if (this.controlStatus.eap_status === '2') {
                  this.controlStatus.eap_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPlc') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.plc_status = '2'
                this.$message({ message: this.$t('lang_pack.hmiMain.PLCCommunication'), type: 'error' })
              } else {
                if (this.controlStatus.plc_status === '2') {
                  this.controlStatus.plc_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPanelCcd') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.panel_status = '2'
                this.$message({ message: this.$t('lang_pack.hmiMain.CCDinterrupted'), type: 'error' })
              } else {
                if (this.controlStatus.panel_status === '2') {
                  this.controlStatus.panel_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPalletCcd11') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.pallet1_status = '2'
                this.$message({ message: this.$t('lang_pack.hmiMain.CCD1Interrupted'), type: 'error' })
              } else {
                if (this.controlStatus.pallet1_status === '2') {
                  this.controlStatus.pallet1_status = '1'
                }
              }
            } else if (topic.indexOf('SCADA_STATUS/LoadPalletCcd2') >= 0) {
              if (statusValue === '0') {
                this.controlStatus.pallet2_status = '2'
                this.$message({ message: this.$t('lang_pack.hmiMain.CCD2Interrupted'), type: 'error' })
              } else {
                if (this.controlStatus.pallet2_status === '2') {
                  this.controlStatus.pallet2_status = '1'
                }
              }
            }
          } else if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            if (topic === 'SCADA_CHANGE/Ccd/CcdStatus/PanelPic') {
              this.ccdImage = 'data:image/png;base64,' + jsonData.TagNewValue
            } else {
              Object.keys(this.monitorData).forEach(key => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                if (this.aisMonitorMode === 'AIS-SERVER') {
                  client_code = client_code + '_' + this.currentStation.station_code
                }
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                if (tag_key === jsonData.TagKey) {
                  this.monitorData[key].value = jsonData.TagNewValue
                }
              })
            }
          }
        } catch (e) { console.log(e) }
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, error => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('lang_pack.vie.pleaseStartMonitor'),
            type: 'error'
          })
          return
        }
        if (!error) {
          console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          if (this.showMsg) {
            this.handleCloseDialog()
            this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
          }
          // 执行完成后都复位为true
          this.showMsg = true
        } else {
          // 执行完成后都复位为true
          this.showMsg = true
          this.$message({ message: this.$t('lang_pack.commonPage.operationfailure'), type: 'error' })
        }
      })
    },

    // 发送消息函数
    sendMessageByCheck(topic, msg, checkFlag, panelBarCode) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }

      // 不验证条码--定颖定制
      // 校验板件条码
      // if (checkFlag) {
      //   const query = {
      //     user_name: Cookies.get('userName'),
      //     station_id: this.currentStation.station_id,
      //     station_code: this.currentStation.station_code,
      //     panel_barcode: panelBarCode,
      //     work_port_index: this.monitorData.PlcWorkPortIndex.value
      //   }
      //   loadPlanPanelCheck(query)
      //     .then(res => {
      //       const defaultQuery = JSON.parse(JSON.stringify(res))
      //       if (defaultQuery.code === 0) {
      //         // qos消息发布服务质量
      //         this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
      //           if (!error) {
      //             if (this.showMsg) {
      //               this.handleCloseDialog()
      //               this.$message({ message: '操作成功！', type: 'success' })
      //             }
      //             // 执行完成后都复位为true
      //             this.showMsg = true
      //           } else {
      //             // 执行完成后都复位为true
      //             this.showMsg = true
      //             this.$message({ message: '操作失败！', type: 'error' })
      //           }
      //         })
      //       } else {
      //         this.$message({ message: defaultQuery.msg, type: 'error' })
      //       }
      //     })
      //     .catch(() => {
      //       this.$message({ message: '操作异常', type: 'error' })
      //     })
      //   return
      // }

      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
        if (!error) {
          if (this.showMsg) {
            this.handleCloseDialog()
            this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
          }
          // 执行完成后都复位为true
          this.showMsg = true
        } else {
          // 执行完成后都复位为true
          this.showMsg = true
          this.$message({ message: this.$t('lang_pack.commonPage.operationfailure'), type: 'error' })
        }
      })
    },

    // 定制:人工扫描任务并提交到EAP验证
    sendMessageByUpEap(topic, msg, palletNum, lotList, portCodeIndex) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('lang_pack.vie.pleaseStartMonitor'),
          type: 'error'
        })
        return
      }
      const query = {
        user_name: Cookies.get('userName'),
        station_id: this.currentStation.station_id,
        port_index: portCodeIndex,
        pallet_num: palletNum,
        lot_list: JSON.stringify(lotList)
      }

      // 倒计时功能
      this.delayCount = 45
      const ths = this
      const loading = this.$loading({
        lock: true,
        text: 'Data Processing（' + this.delayCount + '）',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      })
      this.timer3 = setInterval(function() {
        ths.delayCount--
        loading.text = 'Data Processing（' + ths.delayCount + '）'
        if (ths.delayCount === 0) {
          loading.close()
          clearInterval(this.timer3)
        }
      }, 1000)

      carrierIDReport(query)
        .then(res => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)

          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            // qos消息发布服务质量
            this.clientMqtt.publish(topic, msg, { qos: 0 }, error => {
              if (!error) {
                if (this.showMsg) {
                  this.handleCloseDialog()
                  this.$message({ message: this.$t('lang_pack.commonPage.operationSuccessful'), type: 'success' })
                }
                // 执行完成后都复位为true
                this.showMsg = true
              } else {
                // 执行完成后都复位为true
                this.showMsg = true
                this.$message({ message: this.$t('lang_pack.commonPage.operationfailure'), type: 'error' })
              }
            })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          // 清除计时器
          this.delayCount = 0
          loading.close()
          clearInterval(this.timer3)
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // 测试EAP通讯
    sendEapPingTest() {
      eapPing({})
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            this.$message({ message: this.$t('lang_pack.commonPage.pingSuccessful'), type: 'success' })
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.operationException'), type: 'error' })
        })
    },
    // CIM消息处理功能
    handleConfirmCIMMsg() {
      this.queryCim = true
      this.dialogCIMMsgVisible = false
    },

    // 处理弹框操作
    handleMessage(json) {
      if (json.web_name !== 'load_monitor') return
      //不使用RCS弹窗Title,避免影响多语言
      //this.msgDialogTitle = json.func_des
      this.tagKeyList = json.func_paras
      this.currentFuncCode = json.func_code
      // 若是员工登入
      if (json.func_code === 'user_login_in') {
        if (!this.userDialogVisible) {
          this.loginInfo.user_name = '---'
          this.loginInfo.nick_name = '---'
          this.loginInfo.dept_id = '---'
          this.loginInfo.shift_id = '---'
          this.openUserLogin()
        }
        return
      }
      if (json.func_code === 'show_msg') {
        this.handleShowMsg(json)
      } else {
        if (json.func_code === 'manual_order') {
          this.manualOrderShow = true
        } else if (json.func_code === 'change_recipe') {
          this.changeRecipeShow = true
        } else if (json.func_code === 'pallet_manual_scan') {
          this.palletManualScanShow = true
        } else if (json.func_code === 'inspect_confirm') {
          this.inspectConfirmShow = true
        } else if (json.func_code === 'panel_manual_scan') {
          this.panelManualScanShow = true
        } else if (json.func_code === 'panel_manual_confirm') {
          this.panelManualConfirmShow = true
        } else if (json.func_code === 'panel_manual_judge') {
          this.panelManualConfirmShow = true
        } else if (json.func_code === 'manual_lot_scan') {
          this.manualLotListShow = true
        } else if (json.func_code === 'wip_manual_finish_count') {
          this.manualFinishCountShow = true
        } else if (json.func_code === 'wip_manual_noread_count') {
          this.manualNoReadCountShow = true
        }
        this.mgsDialogVisible = true
      }
    },
    // 处理显示消息提醒
    handleShowMsg(json) {
      var msg_level = 'info'
      if (json.msg_level === 'INFO') {
        msg_level = 'info'
      } else if (json.msg_level === 'ERROR') {
        msg_level = 'error'
      } else if (json.msg_level === 'WARN') {
        msg_level = 'warning'
      }
      if (json.func_dlg === 'Y') {
        this.jsonMsgFlag = true
        this.jsonMsg = json.msg
      } else {
        this.$message({
          message: json.msg,
          type: msg_level,
          duration: json.dlg_second * 1000
        })
      }
    },
    // 处理关闭弹窗
    handleCloseDialog() {
      //设置500ms再执行
      var _time = setTimeout(() => {
          this.manualOrderShow = false
          this.changeRecipeShow = false
          this.palletManualScanShow = false
          this.inspectConfirmShow = false
          this.panelManualScanShow = false
          this.panelManualConfirmShow = false
          this.manualLotListShow = false
          this.mgsDialogVisible = false
          this.manualFinishCountShow = false
          this.manualNoReadCountShow = false
          clearTimeout(_time)
      }, 500)
    }
  }
}
</script>

<style>
body,
html {
  overflow: hidden;
  position: relative;
  padding: 0;
  margin: 0;
  height: 100%;
  width: 100%;
  /* background:red; */
}

#loadMonitor {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  transform-origin: 0 0;
  padding: 5px 5px 5px 5px;
}

#loadMonitor .el-header {
  /* background-image: linear-gradient(to right, #D0DEFA,#D0DEFA,#9EBAF4); */
  color: #333;
  height: 70px !important;
  line-height: 70px;
  padding-left: 5px !important;
}

#loadMonitor .el-main {
  background-color: #fff;
  color: #333;
  padding: 5px;
}

#loadMonitor .el-descriptions {
  background-color: #9EBAF4;
}

#loadMonitor .table-descriptions-label {
  width: 150px;
}

#loadMonitor .table-descriptions-content {
  width: 150px;
  color: #333;
  font-weight: 600;
}

#loadMonitor .port-title {
  background-image: linear-gradient(to right, #D0DEFA, #D0DEFA, #9EBAF4);
  color: #333;
  line-height: 45px;
  font-weight: 600;
  padding-left: 15px;
}

#loadMonitor .port-table-descriptions-label {
  width: 150px;
}

#loadMonitor .port-table-descriptions-content {
  color: #333;
  font-weight: 600;
}

#loadMonitor .message {
  color: #000;
  font-weight: 600;
  padding: 5px 10px 5px 10px;
  border-radius: 10px;
}

#loadMonitor .message-info {
  background-color: #7AA1EF;
}

#loadMonitor .message-warning {
  background-color: #FBB85A;
}

#loadMonitor .message-error {
  background-color: #F56C6C;
}
</style>
<style lang="scss" scoped>
@import '~@/assets/styles/dy/dialog_hmi.scss';
.manualPanelStatus{
  ::v-deep .el-dialog__headerbtn {
  display: none;
  }
}
#loadMonitor .el-header {
  .btnone {
    background: #50d475;
    border-color: #50d475;
  }

  .btnone0 {
    background: #959595;
    border-color: #e8efff;
    color: #ffffff;
  }

  .btnone:active {
    background: #13887c;
  }

  .btntwo {
    background: #5a6671;
    border: 1px solid #5a6671;
  }

  .btnwarn {
    background: #ed2106;
    border: 1px solid #5a6671;
  }

  .btntwo:active {
    background: #096ca8;
  }

  button {
    border-radius: 0.25rem;
    height: 42px;
    font-size: 18px;
  }
}

table.table3 {
  text-align: center;
  border-collapse: collapse;
  width: 100%;
  margin-top: 5px;
}

.table3 tbody td {
  color: #000000;
  font-size: 13px;
  font-weight: normal;
  border: 1px solid #9596a5;
  height: 30px;
  background-color: #ffffff;
  //white-space: nowrap;
}

.table3 tbody .stepInfo {
  display: flex;
  flex-wrap: wrap !important;
  overflow: hidden;
}

.tdName {
  background-color: #f2f2f2 !important;
  width: 160px;
  color: #5c717d;
  /*box-shadow: inset 2px 2px 2px 0px rgb(255 255 255 / 50%), inset -7px -7px 10px 0px rgb(0 0 0 / 10%), 7px 7px 20px 0px rgb(0 0 0 / 10%), 4px 4px 5px 0px rgb(0 0 0 / 10%);
  text-shadow: 2px 2px 3px rgb(255 255 255 / 50%), -4px -4px 6px rgb(116 125 136 / 20%);*/
}

.loginStyle {
  width: 150px;

  button {
    width: 162px;
    height: 40px;
    font-weight: 700;
    font-size: 16px;
    background-color: #229f99;
    border: 0;
    border-radius: 0px;
  }

  button:active {
    background-color: #0a8c86;
  }
}

.normal {
  color: #67C23A;
  font-weight: 700;
}

.zaixian {
  color: #81ff42 !important;
}

.deviceInit {
  color: #070707 !important;
}
.deviceRun {
  color: #81ff42 !important;
}
.deviceStop {
  color: #ff42dcbc !important;
}
.deviceIdle {
  color: #f2ff42 !important;
}
.deviceDown {
  color: #ff4242 !important;
}
.devicePm {
  color: #ffd042 !important;
}

.operatingEMode {
  color: #67C23A !important;
}

.lixian {
  color: #000000 !important
}

.showImg {
  background: #f2f6ff !important;
  color: #333333;
  width: 29%;
  height: 215px ;
  border: 1px solid #9596a5;
  margin-top: 5px;
  margin-right: 5px;
  text-align: center;

  img {
    height: 213px !important;
  }
}

.qianzhi {
  button {
    color: #ffffff;
    font-size: 16px;
    height: 36px;
    // width: 129px;
    border-radius: 0.25rem;
    border: 0;
    margin: 10px 0;
    word-spacing: -2px;
    background: #3398cb;
  }

  button:active {
    background-color: #096ca8;
  }

}

.wraptable {
  display: flex;
}

.jianju {
  width: 45px;
  display: inline-block;
}

::v-deep .el-table {
  margin-top: 5px;
  border: 1px solid #9596a5 !important;
}

::v-deep .el-table th {
  color: #000000;
  font-size: 13px;
  font-weight: normal;
  background-color: #f2f2f2 !important;
  outline: none;
  height: 30px !important;
  padding: 0;
  border-bottom: 1px solid #9596a5 !important;
}

::v-deep .el-table_1_column_1,
::v-deep .el-table_2_column_7 {
  border-left: 0 !important;
}

::v-deep .el-table .cell {
  text-align: center;
}

::v-deep .el-table td.el-table__cell div {
  font-size: 14px;
  font-weight: 700;
}

.peopleInfo {
  margin-bottom: 5px;
  box-shadow: 0 2px 10px #d1d1d1;
  padding: 15px 0;
  border-radius: 0;
  background: #79a0f1;

  ul {
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: space-around;

    li {
      // width: 7.5%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0;
      margin: 0;
      list-style: none;
      position: relative;

      // ::after{
      //   content: '';
      //   position: absolute;
      //   right: 0;
      //   top: 10px;
      //   width: 1px;
      //   height: 26px;
      //   background-color: #d8e4fd;
      // }
      span:nth-of-type(1) {
        font-size: 18px;
        color: #d8e4fd;
        margin-bottom: 4px;
      }

      span:nth-of-type(2) {
        font-size: 22px;
        color: #000000;
        font-weight: normal;
      }

      button:nth-of-type(1) {
        background: #66cbfe;
        word-spacing: 5px;
        font-size: 22px;
        padding: 7px 0;
      }

      button:nth-of-type(2) {
        background: #ff6157;
        margin-left: 18px;
        border-radius: 0.25rem;
        word-spacing: 5px;
        font-size: 22px;
        padding: 7px 0;
      }
    }

    .longString {
      width: 1px;
      height: 26px;
      background-color: #d8e4fd;
      margin-top: 5px;
    }

    .lastLi {
      ::after {
        content: '';
        position: absolute;
        right: 0;
        top: 10px;
        width: 0;
        height: 0;
        background-color: #9a9a9a;
      }
      button{
        width: 104px;
      }
    }

    .lastLi {
      display: flex;
      flex-direction: inherit;
      align-items: center;
      width: 15%;
    }

    list-style: none;
  }
}

::v-deep .wd {
  width: 100px;
}

::v-deep .wdban {
  width: 60px;
}

.marginR {
  margin-right: 5px !important;
}

.noborderR {
  border-right: 0 !important;
}

.indicatorlstyle {
  width: 20.5%;
  border: 1px solid #9596a5;
  margin-top: 5px;

  .indicatorrone {
    width: 100%;
    height: 213px;
  }
}

.w150 {
  width: 150px;
}

.statuHead {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .wrappstyle {
    display: flex;
    align-items: center;

    p {
      margin: 0 16px !important;
      display: flex;
      flex-direction: column;
      align-items: center;

      span {
        font-size: 18px;
        font-weight: 700;
      }

      .statuText {
        line-height: 30px;
        height: 30px;
      }
    }

    p:last-child {
      margin-right: 0 !important;
    }
  }

  .wholeline {
    width: 20px;
    height: 20px;
    margin-top: 5px;
    border-radius: 50%;
  }

  .wholelinenormal {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #0d0;
    box-shadow: 0 0 0.75em #0f0, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .wholelineerror {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #f00;
    box-shadow: 0 0 0.75em #f00, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .wholelinegray {
    -webkit-animation: heart 1s linear infinite;
    animation: heart 1s linear infinite;
    background-color: #606266;
    box-shadow: 0 0 0.75em #606266, 0 0.5em 0.75em rgba(0, 0, 0, 0.3);
    background-image: radial-gradient(0.3em 0.25em at 50% 25%, rgb(255, 255, 255) 25%, rgba(255, 255, 255, 0)), radial-gradient(0.25em 0.25em at 30% 75%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(0.3em 0.3em at 60% 80%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0)), radial-gradient(100% 100%, rgba(255, 255, 255, 0) 30%, rgba(255, 255, 255, 0.3) 40%, rgba(0, 0, 0, 0.5) 50%);
  }

  .jianpan img {
    width: 35px;
    margin-top: 10px;
    margin-left: 10px;
    cursor: pointer;
  }
}
</style>

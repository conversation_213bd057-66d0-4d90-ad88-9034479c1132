<template>
  <el-dialog :append-to-body="true" modal-append-to-body :title="rgvData.tagIndex+'#平车超高扫描'" :visible.sync="dialogVisible" :width="modalWidth" :before-close="handleClose">
    <div class="fault-info">
      <div :class="Object.keys(rgvData).length > 0 && rgvData.GroundCabinet && rgvData.GroundCabinet.tagValue ==='1' ? 'red-light' : 'green-light'" />
      <div>超高报警</div>
    </div>
    <el-form ref="query" :inline="true" size="small" label-width="130px">
      <div class="wrapElForm">
        <div class="wrapElFormFirst col-md-10 col-12">
          <div class="formChild col-md-12 col-12">
            <el-form-item label="当前料框的重量:">
              <el-input :value="parseInt(rgvData.Tag008.tagValue)" disabled clearable size="small" />
            </el-form-item>
          </div>
          <div class="formChild col-md-12 col-12">
            <el-form-item label="料框码:">
              <el-input v-model="materialCode" clearable size="small" />
            </el-form-item>
          </div>
        </div>
      </div>
    </el-form>

    <div class="code-dialog-footer">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确认</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  name: 'CARDETAIL',
  data() {
    return {
      height: document.documentElement.clientHeight - 400,
      dialogVisible: false,
      modalWidth: '25%',
      rgvData: {
        'Tag008': {
          'tagValue': ''
        }
      },
      materialCode: ''
    }
  },
  methods: {
    open(data) {
      this.rgvData = data.rgvData
      this.dialogVisible = true
    },
    handleClose(done) {
      done()
    },
    handleSubmit() {
      if (!this.materialCode) {
        this.$message.error('请输入料框码')
        return
      }
      const key1 = ['01', '02', '07', '08'].includes(this.rgvData.tagIndex) ? 'QRcode1' : 'QRcode3'
      const key2 = ['01', '02', '07', '08'].includes(this.rgvData.tagIndex) ? 'QRcode2' : 'QRcode4'
      var topic = 'SCADA_WRITE/SlAisSimPlc'
      const sendStr = JSON.stringify({ 'Data': [{ 'TagKey': 'SlAisSimPlc/WmsStatus/' + key1, 'TagValue': this.materialCode }, { 'TagKey': 'SlAisSimPlc/WmsStatus/' + key2, 'TagValue': '1' }], 'ClientName': 'SCADA_WEB' })
      this.$parent.$parent.$parent.$parent.$parent.$parent.sendMqttMessage(topic, sendStr)
      this.$message.success('料框码写入成功')
      this.materialCode = ''
      this.dialogVisible = false
    }
  }
}
</script>
<style lang="less" scoped>
.car-info{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    span{
        display: block;
        width: 100px;
        text-align: right;
        margin-right: 10px;
        color: #fff;
        font-size: 18px;
    }
    ::v-deep .el-input{
        width: 150px;
        border: 2px solid #57d6f6;
        border-radius: 5px;
        .el-input__inner{
            background-color: #084aa0;
            color: #fff;
        }
    }
    ::v-deep .el-radio__inner{
        width: 20px;
        height: 20px;
    }
}
.code-dialog-footer {
  width: 100%;
  height: auto;
  padding: 10px;
  text-align: center;
}
.fault-info{
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    color: #fff;
    font-size: 20px;
}
/* 小绿灯的样式 */
.green-light {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #75f94c;
    animation: greenPulse 2s infinite;
    margin:0 40px;
}

/* 小红灯的样式 */
.red-light {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: red;
    animation: redPulse 2s infinite;
    margin:0 40px;
}
/* 小绿灯的动画 */
@keyframes greenPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 255, 0, 0.7);
    }

    50% {
        box-shadow: 0 0 0 15px rgba(0, 255, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(0, 255, 0, 0);
    }
}

/* 小红灯的动画 */
@keyframes redPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0.7);
    }

    50% {
        box-shadow: 0 0 0 15px rgba(255, 0, 0, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(255, 0, 0, 0);
    }
}
::v-deep .el-form-item--small .el-form-item__label{
    color: #fff;
}
</style>

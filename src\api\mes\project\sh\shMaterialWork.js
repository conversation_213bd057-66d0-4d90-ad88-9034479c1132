import request from '@/utils/request'
// 查询返修策略
export function sel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShRepairWorkSel',
    method: 'post',
    data
  })
}
// 提交返修工位
export function add(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShRepairWorkIns',
    method: 'post',
    data
  })
}
// 提交返修策略
export function MesShRepairWorkRecordIns(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShRepairWorkRecordIns',
    method: 'post',
    data
  })
}

// 删除返修策略
export function MesShRepairWorkDel(data) {
  return request({
    url: 'aisEsbWeb/mes/core/MesShRepairWorkDel',
    method: 'post',
    data
  })
}
export default { add, sel, MesShRepairWorkDel, MesShRepairWorkRecordIns }

<template>
  <el-container style="height:100%">
    <el-header>
      <div class="headerFirst">
        <div class="headerLogo">
          <div><img :src="logo" class="hmi-logo"></div>
          <div class="headerStaion">
            <span class="spanFisrt" @click="openStationChoose"><svg-icon icon-class="tree" />{{ $t('lang_pack.andonevent.prodLineCode') +'：' }}{{ currentStation.prod_line_code + ' ' + currentStation.prod_line_des }}<i class="el-icon-more arrowStyle" /></span>
            <span @click="openStationChoose"><svg-icon icon-class="monitor" />{{ $t('lang_pack.andonevent.stationCode') +'：' }}{{ currentStation.station_code + ' ' + currentStation.station_des }}<i class="el-icon-more arrowStyle" /></span>
          </div>
        </div>

        <div class="headerSecond">
          <div class="headerTwo">
            <span><svg-icon icon-class="people" />{{ $t('lang_pack.header.user') }}{{ user.nickName }}</span>
            <span><i class="el-icon-time" />{{ $t('lang_pack.header.onlineDuration') }}{{ onlineTime }}{{ $t('lang_pack.header.min') }}</span>
          </div>
          <div>
            <el-tooltip :content="$t('lang_pack.header.signOut')" effect="dark" placement="bottom">
              <span class="loginout" @click="open"><i class="el-icon-switch-button" /></span>
            </el-tooltip>
            <el-tooltip :content="$t('lang_pack.header.lockScreen')" effect="dark" placement="bottom">
              <div class="right-menu-item" style="cursor: pointer" @click="lockScreen">
                <i class="el-icon-lock" style="font-weight:700;" />
              </div>
            </el-tooltip>
            <el-tooltip :content="$t('lang_pack.header.screen')" effect="dark" placement="bottom">
              <screenfull id="screenfull" class="right-menu-item hover-effect" />
            </el-tooltip>
          </div>
        </div>
      </div>
      <!-- <span style="color: #fff;">用户信息:{{ userMsg() }}</span> -->
      <el-dialog :modal="false" :title="$t('lang_pack.header.stationSelect')" :visible.sync="dialogVisible" width="80%" top="65px">
        <div style="width:100%">
          <div v-for="(item, index) in stationData" :key="index" style="margin-bottom:20px;">
            <el-tag :key="index" type="info" :disable-transitions="false" style="line-height:40px;height:40px;">
              {{ item.prod_line_code + ' ' + item.prod_line_des }}
            </el-tag>
            <div v-if="item.station_list.length > 0" style="margin-top:10px;">
              <el-tag v-for="(item1, index1) in item.station_list" :key="index1" :disable-transitions="false" style="margin:10px 10px 10px 0;line-height:50px;height:50px;cursor: pointer;" @click="handleStationChoose(item, item1)">
                {{ item1.station_code + ' ' + item1.station_des }}
              </el-tag>
            </div>
          </div>
        </div>
      </el-dialog>
    </el-header>
    <el-main style="background-color: #f1f1f1">
      <div v-if="greenbgShow" class="greenStyle" :style="'background-image:url(' + greenbg + ');'">
        <div class="drop drop1">
          <h1 style="text-shadow: 0 5px rgb(0 0 0 / 30%);">{{ $t('lang_pack.header.greenNewEnergy') }}</h1>
          <div class="drop drop2" />
          <div class="drop drop3" />
          <div class="drop drop4" />
        </div>
      </div>
      <el-tabs id="hmiTabs" v-model="editableTabsValue1" :closable="true" @tab-remove="handleRemove">
        <el-tab-pane v-for="(item, index) in editableTabs1" :key="index" :label="item.title" :name="item.name" style="padding: 0px; background-color: #e6e8ee">
          <elFrame v-if="elFrameFlag" :name="item.name" :src="item.path + '?prod_line_id=' + currentStation.prod_line_id + '&station_id=' + currentStation.station_id + '&station_code=' + currentStation.station_code+'&cell_id='+currentStation.cell_id" />
        </el-tab-pane>
      </el-tabs>
    </el-main>
    <el-footer style="height: 65px">
      <table style="width: 100%; border: 0px">
        <tr>
          <td style="width: 20px">
            <el-button
              v-show="hmiMenuData.length !== 0"
              class="filter-item"
              size="medium"
              type="primary"
              icon="el-icon-arrow-left"
              style="
                    height: 40px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
              plain
              @click="marginLeft !== 0 ? (marginLeft += 100) : 0"
            />
          </td>
          <td>
            <el-scrollbar ref="scrollbar" style="width: 100%">
              <div :style="'width: 400%;margin-left:' + marginLeft + 'px;'">
                <template v-for="(item, index) in hmiMenuData">
                  &nbsp;

                  <el-button
                    :key="item.id"
                    class="filter-item itemBtns"
                    size="medium"
                    type="primary"
                    :icon="item.menu_icon"
                    style="
                            height: 40px;
                            margin-top: 5px;
                            font-weight: bold;
                            margin-left: 0px;
                          "
                    plain
                    @click="handlesSelect(item, index, item)"
                  >
                    {{ item.current_menu_des === '' ? item.menu_des : item.current_menu_des }}
                  </el-button>
                </template>
              </div>
            </el-scrollbar>
          </td>
          <td style="width: 20px">
            <el-button
              v-show="hmiMenuData.length !== 0"
              class="filter-item"
              size="medium"
              type="primary"
              icon="el-icon-arrow-right"
              style="
                    height: 40px;
                    margin-top: 5px;
                    font-weight: bold;
                    margin-left: 0px;
                    padding: 0px;
                    width: 20px;
                  "
              plain
              @click="marginLeft -= 100"
            />
          </td>
        </tr>
      </table>
    </el-footer>
    <div v-if="lockShow" class="zhezhao">
      <el-form class="userInfo" @submit.native.prevent>
        <h3 class="title">{{ $t('lang_pack.SystemName') }}</h3>
        <el-form-item>
          <el-input
            v-model="userForm.newPw"
            :placeholder="$t('lang_pack.header.loginPassword')"
            type="password"
            auto-complete="off"
            show-password
            @keyup.enter.native="unLock()"
          >
            <div slot="prefix" style="margin-left: 3px">
              <i class="el-icon-lock" /></div></el-input>
          <span class="jianpan" @click="showKeyboard">
            <img :src="keyboard">
          </span>
        </el-form-item>
        <el-form-item class="wrapbtn">
          <!-- <el-button
          size="medium"
          type="warning"
          @click="logout"
        ><i class="el-icon-unlock" />退屏重新登录</el-button> -->
          <el-button
            :loading="loading"
            size="medium"
            type="primary"
            style="width: 100%"
            @click="unLock"
          ><i class="el-icon-unlock" />{{ $t('lang_pack.header.unlock') }}</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 全键盘组件 -->
    <div v-if="isShow" class="keyboard-mask">
      <SimpleKeyboard :input="input" @onChange="onChange" @onKeyPress="onKeyPress" />
    </div>
  </el-container>
</template>

<script>
import md5 from 'js-md5'
import { sel } from '@/api/core/system/sysLogo'
import elFrame from '@/views/core/hmi/iframe_wx'
import { sel as selHmiInfo } from '@/api/hmi/main'
import { selCurrentMessage } from '@/api/eap/eapStationHmiShow'
import Cookies from 'js-cookie'
import { mapGetters } from 'vuex'
import keyboard from '@/assets/images/keyboard.png'
import greenbg from '@/assets/images/greenbg3.jpg'
import SimpleKeyboard from '@/components/SimpleKeyboard/SimpleKeyboard.vue'
import screenfull from '@/components/Screenfull/index.vue'
export default {
  name: 'layout',
  components: {
    elFrame,
    SimpleKeyboard,
    screenfull
  },
  data() {
    return {
      input: '',
      isShow: false,
      lockShow: false,
      currentInputDom: '',
      keyboard: keyboard,
      // 倒计时相关
      countdown: 30, // 倒计时秒数
      countdownTimer: null, // 倒计时定时器
      isCountdownActive: false, // 倒计时是否激活
      autoLockTimeout: 30, // 自动锁屏超时时间（秒），从后台配置读取
      lastActivityTime: Date.now(), // 最后活动时间
      activityCheckTimer: null, // 活动检查定时器
      countdownResetTime: null, // 记录倒计时重置的时间
      storageCheckTimer: null, // localStorage检查定时器
      // HMI通知相关
      isNotificationEnabled: false, // HMI通知是否启用
      notificationTimer: null, // 弹窗数据查询定时器
      notificationInterval: 3000, // 查询间隔（毫秒），默认3秒
      lastCheckedMessageId: '', // 最后检查的消息ID
      unreadMessageCount: 0, // 未读消息数量
      greenbg: greenbg,
      greenbgShow: true,
      userForm: {
        newPw: '',
        user: '',
        isCover: true
        // isLock:this.$store.state.user.isLock,
      },
      loading: false,
      logo: '',
      dialogVisible: false,
      height: document.documentElement.clientHeight,
      hmiMenuData: [],
      editableTabsValue1: '0',
      editableTabs1: [],
      marginLeft: 0,
      marginLeft1: 0,
      stationData: [],
      currentStation: {
        prod_line_id: '',
        prod_line_code: '',
        prod_line_des: '',
        station_id: '',
        station_code: '',
        station_des: '',
        cell_id: '0'
      },
      onlineTime: 0,
      timer: '',
      elFrameFlag: true
    }
  },
  computed: {
    ...mapGetters(['user'])
  },
  watch: {},
  async mounted() {
    this.timer = setInterval(this.getOnlineTime, 1000)
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight
    }
    window.addEventListener('message', this.handleMessage)
    setTimeout(() => {
      // console.log(document.querySelectorAll('.itemBtns'))
      document.querySelectorAll('.itemBtns') && document.querySelectorAll('.itemBtns')[0] && document.querySelectorAll('.itemBtns')[0].click && document.querySelectorAll('.itemBtns')[0].click()
    }, 1000)
    
    // 获取系统配置
    const isLockScreenEnabled = await this.getAutoLockConfig()
    await this.getHmiNotificationConfig()
    
    // 只有在启用锁屏功能时才启动相关监听和倒计时
    if (isLockScreenEnabled) {
      // 直接监听document上的所有用户活动事件
      this.startGlobalActivityListener()
      
      // 页面加载后启动倒计时
      this.startCountdown()
    } else {
      console.log('锁屏功能已禁用，不启动相关监听和倒计时')
    }
    
    // 根据通知配置启动相关功能
     if (this.isNotificationEnabled) {
       console.log('HMI通知功能已启用')
       // 启动定时查询后台弹窗数据
       this.startNotificationPolling()
     } else {
       console.log('HMI通知功能已禁用')
     }
  },
  beforeUpdate() {
    this.$nextTick(() => {
      this.findInput()
    })
  },
  created: function() {
    this.getLogo()
    this.handleRefresh('127.0.0.1', '8090')
    this.getOnlineTime()
  },
  beforeDestroy() {
    clearInterval(this.timer)
    // 清理倒计时定时器
    this.stopCountdown()
    // 移除document事件监听
    this.stopGlobalActivityListener()
    // 停止通知轮询
    this.stopNotificationPolling()
    // 移除消息监听器
    window.removeEventListener('message', this.handleMessage)
  },
  methods: {
    userMsg() {
      return Cookies.get('userMsg')
    },
    handleMessage(event) {
      if (event.data === 'whiteLoginWx') {
        this.$router.push('/whiteLoginWx')
      }
    },
    getOnlineTime() {
      if (Cookies.get('OnlineTime') !== undefined) {
        var dateBegin = new Date(Cookies.get('OnlineTime'))
        var dateEnd = new Date()
        var dateDiff = dateEnd.getTime() - dateBegin.getTime() // 时间差的毫秒数
        this.onlineTime = Math.floor(dateDiff / (60 * 1000)) // 计算相差分钟数
      }
    },
    getLogo() {
      // 格式化查询条件
      const query = {
        user_name: Cookies.get('userName')
      }
      sel(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.logo = 'data:image/png;base64,' + defaultQuery.data[0].logo
            }
          }
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: this.$t('lang_pack.vie.queryException')
          })
        })
    },
    openStationChoose() {
      this.dialogVisible = true
    },
    handleStationChoose(item, item1) {
      this.editableTabs1 = []
      this.currentStation = {
        prod_line_id: item.prod_line_id,
        prod_line_code: item.prod_line_code,
        prod_line_des: item.prod_line_des,
        station_id: item1.station_id,
        station_code: item1.station_code,
        station_des: item1.station_des,
        cell_id: item1.cell_id
      }
      this.dialogVisible = false
    },
    handleRefresh(line_code, station_code) {
      this.editableTabs1 = []
      this.hmiMenuData = []
      const query = {
        user_name: Cookies.get('userName'),
        userID: Cookies.get('userId')
      }
      selHmiInfo(query)
        .then(res => {
          this.stationData = res.station
          if (res.station.length > 0) {
            this.currentStation = {
              prod_line_id: this.stationData[0].prod_line_id,
              prod_line_code: this.stationData[0].prod_line_code,
              prod_line_des: this.stationData[0].prod_line_des,
              station_id: this.stationData[0].station_list[0].station_id,
              station_code: this.stationData[0].station_list[0].station_code,
              station_des: this.stationData[0].station_list[0].station_des,
              cell_id: this.stationData[0].station_list[0].cell_id
            }
          }
          const menuData = res.menu

          // this.hmiMenuData = defaultQuery.data;
          var dd = []
          for (let i = 0; i < menuData.length; i++) {
            const element = menuData[i]
            const id = element.menu_item_id
            const menu_des = element.menu_item_des
            const path = element.function_path
            const hmi_menu_ico = element.hmi_menu_ico

            var aa = {}
            aa.id = '1-' + id
            aa.menu_des = menu_des
            aa.button_type = 'primary'
            aa.current_menu_des = ''
            aa.menu_icon = hmi_menu_ico === '' ? 'el-icon-s-platform' : hmi_menu_ico
            aa.path = path // 'http://' + server_host + ':' + cell_port + path // http://localhost:8013/hmi
            dd.push(aa)
          }
          this.hmiMenuData = dd
        })
        .catch(() => {
          this.$message({
            type: 'error',
            message: this.$t('lang_pack.vie.queryException')
          })
        })
    },
    handlesSelect(item, index, parent) {
      let obj = {}
      const TabName = 'Tab_' + item.id
      obj = this.editableTabs1.find(item => {
        return item.name === TabName
      })
      const currentMenu1 = this.editableTabs1.filter(item => item.button_type === 'warning')
      if (currentMenu1.length > 0) {
        currentMenu1[0].button_type = 'primary'
      }

      if (typeof obj !== 'undefined') {
        obj.button_type = 'warning'
        this.editableTabsValue1 = TabName
      } else {
        this.editableTabs1.push({
          title: item.menu_des,
          name: TabName,
          path: item.path,
          icon: item.menu_icon,
          button_type: 'warning'
        })
        this.editableTabsValue1 = TabName
      }
      const currentMenu = this.hmiMenuData.filter(item => item.button_type === 'warning')
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
        currentMenu[0].current_menu_des = ''
      }
      parent.current_menu_des = item.menu_des
      parent.button_type = 'warning'
      this.$emit('showCellNavbar', item.menu_des)
      this.greenbgShow = false
    },
    handleRemove(tabName) {
      for (var i = 0; i < this.editableTabs1.length; i++) {
        if (this.editableTabs1[i].name === tabName) {
          this.editableTabs1.splice(i, 1)
        }
      }
    },
    handlesOpenedMenu(item, index) {
      const currentMenu = this.editableTabs1.filter(item => item.button_type === 'warning')
      if (currentMenu.length > 0) {
        currentMenu[0].button_type = 'primary'
      }
      item.button_type = 'warning'
      this.editableTabsValue1 = item.name
      this.$emit('showCellNavbar', item.title)
    },
    open() {
      this.$confirm(this.$t('lang_pack.header.exitSystem'), this.$t('lang_pack.Prompt'), {
        confirmButtonText: this.$t('lang_pack.vie.determine'),
        cancelButtonText: this.$t('lang_pack.commonPage.cancel'),
        type: 'warning'
      }).then(() => {
        this.$router.push('/whiteLoginWx')
      })
    },
    // 锁屏：
    lockScreen() {
      this.lockShow = true
    },
    showKeyboard() {
      this.isShow = true
    },
    onChange(input) {
      const that = this
      that.input = input
      that.currentInputDom.value = input + ''
      that.currentInputDom.dispatchEvent(new Event('input'))
    },

    onKeyPress(button) {
      const that = this
      if (button === '{enter}') {
      // 如果按确认键的相应操作
        that.isShow = false
      }
      if (button === '{bksp}') {
      // console.log(1)
      // 删除键的相应操作
      }
      if (button === '{bksp}') {
      // console.log(1)
      // 删除键的相应操作
      }
    },
    // 给页面中input框添加点击事件
    findInput() {
      const that = this
      const list = document.getElementsByTagName('input')
      for (let i = 0; i < list.length; i++) {
        const inputDom = list[i]
        if (inputDom.type === 'text' || inputDom.type === 'password') {
        // console.log(inputDom.readOnly, i)
          if (!inputDom.readOnly) {
            inputDom.addEventListener('click', function(event) {
              that.input = event.target.value // 获取到输入框的值
              that.currentInputDom = event.target
            })
          // console.log(inputDom.type, i);
          }
        }
      }
    },
    unLock() {
      const oldAuct = sessionStorage.getItem('lockPassword')
      sessionStorage.setItem('newlockPassword', md5(this.userForm.newPw))
      console.log(oldAuct, sessionStorage.getItem('newlockPassword'), '999990')
      if (this.userForm.newPw === '' || this.userForm.newPw === undefined) {
        this.$notify.error({
          title: this.$t('lang_pack.header.error'),
          message: this.$t('lang_pack.header.passwordUnlock'),
          duration: 1500
        })
        return
      } else if (md5(this.userForm.newPw) !== oldAuct) {
        this.userForm.newPw = ''
        this.$notify.error({
          title: this.$t('lang_pack.header.error'),
          message: this.$t('lang_pack.header.passwordError'),
          duration: 1500
        })
        return
      } else {
        setTimeout(() => {
          this.$notify.success({
            title: this.$t('lang_pack.header.unlockingSuccessful'),
            duration: 1500
          })
          this.lockShow = false
          this.isShow = false
          this.userForm.newPw = ''
          
          // 解锁后重新启动倒计时
          this.startCountdown()
        }, 500)
      }
    },
    
    // 获取自动锁屏配置
    async getAutoLockConfig() {
      try {
        // 导入系统参数查询方法
        const { sel: selSysParameter } = await import('@/api/core/system/sysParameter')
        
        const queryParameter = {
          userName: Cookies.get('userName'),
          parameter_code: 'AUTO_LOCK_TIMEOUT',
          enable_flag: 'Y'
        }
        
        const response = await selSysParameter(queryParameter)
        
        if (response.code === 0 && response.data && response.data.length > 0) {
          const timeoutMinutes = parseInt(response.data[0].parameter_val)
          if (timeoutMinutes && timeoutMinutes > 0) {
            this.autoLockTimeout = timeoutMinutes * 60 // 转换为秒
            console.log(`自动锁屏配置已加载: ${timeoutMinutes}分钟 (${this.autoLockTimeout}秒)`)
            return true // 返回true表示启用锁屏功能
          } else {
            console.log('自动锁屏配置为0，不启用锁屏功能')
            this.autoLockTimeout = 0
            return false // 返回false表示不启用锁屏功能
          }
        } else {
          console.log('未找到自动锁屏配置，不启用锁屏功能')
          this.autoLockTimeout = 0
          return false // 返回false表示不启用锁屏功能
        }
      } catch (error) {
        console.error('获取自动锁屏配置失败，不启用锁屏功能:', error)
        this.autoLockTimeout = 0
        return false // 返回false表示不启用锁屏功能
      }
    },
    
    // 获取HMI通知配置
    async getHmiNotificationConfig() {
      try {
        // 导入系统参数查询方法
        const { sel: selSysParameter } = await import('@/api/core/system/sysParameter')
        
        const queryParameter = {
          userName: Cookies.get('userName'),
          parameter_code: 'HMI_NOTIFICATION_ENABLED',
          enable_flag: 'Y'
        }
        
        const response = await selSysParameter(queryParameter)
        
        if (response.code === 0 && response.data && response.data.length > 0) {
          const notificationEnabled = response.data[0].parameter_val
          if (notificationEnabled === '1' || notificationEnabled.toLowerCase() === 'true' || notificationEnabled.toLowerCase() === 'y') {
            this.isNotificationEnabled = true
            console.log('HMI通知配置已加载: 启用')
          } else {
            this.isNotificationEnabled = false
            console.log('HMI通知配置已加载: 禁用')
          }
        } else {
          console.log('未找到HMI通知配置，默认禁用通知功能')
          this.isNotificationEnabled = false
        }
      } catch (error) {
        console.error('获取HMI通知配置失败，默认禁用通知功能:', error)
        this.isNotificationEnabled = false
      }
    },
     
     // 启动通知轮询
     startNotificationPolling() {
       if (this.notificationTimer) {
         clearInterval(this.notificationTimer)
       }
       
       // 立即执行一次查询
       this.queryNotificationData()
       
       // 设置定时器
       this.notificationTimer = setInterval(() => {
         this.queryNotificationData()
       }, this.notificationInterval)
       
       console.log(`HMI通知轮询已启动，查询间隔: ${this.notificationInterval / 1000}秒`)
     },
     
     // 停止通知轮询
     stopNotificationPolling() {
       if (this.notificationTimer) {
         clearInterval(this.notificationTimer)
         this.notificationTimer = null
         console.log('HMI通知轮询已停止')
       }
     },
     
     // 查询通知数据（检查新消息）
     async queryNotificationData() {
       try {
         // 检查通知功能是否启用
         if (!this.isNotificationEnabled) return
         
         // 确保有选中的站点才进行检查
         if (!this.currentStation.station_id) return
         
         const query = {
           user_name: Cookies.get('userName'),
           station_id: this.currentStation.station_id.toString()
         }
         
         console.log('正在查询后台弹窗数据...', new Date().toLocaleTimeString(), query)
         
         // 调用消息查询API
         selCurrentMessage(query).then(res => {
           console.log('检查新消息返回:', res)
           if (res.code === 0 && res.data && res.data.length > 0) {
             // 过滤未读消息（finish_flag 为 'N' 的消息）
             const unreadMessages = res.data.filter(msg => msg.finish_flag === 'N')
             
             if (unreadMessages.length > 0) {
               const newMessage = unreadMessages[0] // 取第一条未读消息
               
               // 使用后端返回的 hmi_show_id 作为唯一标识
               const messageId = newMessage.hmi_show_id
               
               if (!messageId) {
                 console.warn('消息缺少 hmi_show_id，跳过处理:', newMessage)
                 return
               }
               
               // 检查是否是新消息（基于唯一ID判断）
               if (messageId !== this.lastCheckedMessageId) {
                 this.lastCheckedMessageId = messageId
                 this.handleNotificationData(newMessage)
                 this.unreadMessageCount++
                 console.log('显示新消息通知:', newMessage, 'hmi_show_id:', messageId)
               } else {
                 console.log('消息已经通知过，跳过:', messageId)
               }
             } else {
               console.log('没有未读消息')
             }
           } else {
             console.log('没有新消息')
           }
         }).catch((error) => {
           console.error('查询弹窗数据失败:', error)
         })
         
       } catch (error) {
         console.error('查询弹窗数据失败:', error)
       }
     },
     
     // 处理通知数据
     handleNotificationData(message) {
       // 检查通知功能是否启用
       if (!this.isNotificationEnabled) {
         console.log('通知功能已被配置为关闭状态，跳过显示通知')
         return
       }
       
       console.log('收到弹窗数据:', message)
       
       // 显示弹窗通知
        const messageContent = message.cim_msg || message.message_content || message.message || '您有新的消息'
        const messageTime = message.item_date || ''
        const fullMessage = messageTime ? `${messageContent}<br><br>   ${messageTime}` : messageContent
        
        this.$notify({
          title: message.message_title || '系统通知',
          message: fullMessage,
          dangerouslyUseHTMLString: true, // 允许使用HTML格式
          type: this.getMessageType(message.popup_type || message.message_type),
          duration: 0, // 不自动关闭，需要用户手动确认
          showClose: true,
          onClick: () => {
            this.handleMessageConfirmed(message)
          }
        })
     },
     
     // 获取消息类型
     getMessageType(messageType) {
       switch (messageType) {
         case 'error':
         case 'ERROR':
           return 'error'
         case 'warning':
         case 'WARNING':
           return 'warning'
         case 'success':
         case 'SUCCESS':
           return 'success'
         default:
           return 'info'
       }
     },
     
     // 处理消息确认
     handleMessageConfirmed(message) {
       console.log('消息已确认:', message)
       
       // 标记消息为已读
       this.markMessageAsRead(message)
       
       // 减少未读消息计数
       if (this.unreadMessageCount > 0) {
         this.unreadMessageCount--
       }
       
       // 显示确认提示
       this.$message.success('消息已确认')
     },
     
     // 标记消息为已读
     async markMessageAsRead(message) {
       try {
         const updateData = {
           user_name: Cookies.get('userName'),
           hmi_show_id: message.hmi_show_id || message.id,
           finish_flag: 'Y'
         }
         
         console.log('标记消息为已读:', updateData)
         
         // 调用后端API更新消息状态
         const { updateMessageStatus } = await import('@/api/eap/eapStationHmiShow')
         const res = await updateMessageStatus(updateData)
         
         if (res.code === 0) {
           console.log('消息状态更新成功')
         } else {
           console.error('消息状态更新失败:', res.msg)
         }
       } catch (error) {
         console.error('标记消息为已读失败:', error)
       }
     },
     
     // 启动倒计时
    startCountdown() {
      // 检查是否启用锁屏功能
      if (this.autoLockTimeout <= 0) {
        console.log('锁屏功能未启用，不启动倒计时')
        return
      }
      
      if (this.isCountdownActive) {
        return // 如果倒计时已经激活，不重复启动
      }
      
      this.isCountdownActive = true
      this.countdown = this.autoLockTimeout
      this.countdownResetTime = Date.now()
      
      // 使用localStorage存储倒计时状态，供跨页面使用
      localStorage.setItem('lockscreen_countdown_active', 'true')
      localStorage.setItem('lockscreen_timeout', this.autoLockTimeout.toString())
      localStorage.setItem('lockscreen_reset_time', this.countdownResetTime.toString())
      
      console.log(`开始${this.autoLockTimeout}秒倒计时，倒计时结束后将自动锁屏（使用localStorage跨页面同步）`)
      
      // 启动活动检查定时器
      this.startActivityCheck()
      // 启动localStorage监听
      this.startLocalStorageListener()
    },
    
    // 启动全局活动监听
    startGlobalActivityListener() {
      const events = [
        'mousedown', 'mousemove', 'mouseup',
        'keydown', 'keyup', 'keypress',
        'scroll', 'wheel',
        'touchstart', 'touchmove', 'touchend',
        'click', 'dblclick',
        'focus', 'blur',
        'input', 'change'
      ]
      
      events.forEach(event => {
        document.addEventListener(event, this.handleUserActivity, {
          passive: true,
          capture: true
        })
      })
      
      console.log('直接监听document事件，覆盖所有页面活动')
    },
    
    // 停止全局活动监听
    stopGlobalActivityListener() {
      const events = [
        'mousedown', 'mousemove', 'mouseup',
        'keydown', 'keyup', 'keypress',
        'scroll', 'wheel',
        'touchstart', 'touchmove', 'touchend',
        'click', 'dblclick',
        'focus', 'blur',
        'input', 'change'
      ]
      
      events.forEach(event => {
        document.removeEventListener(event, this.handleUserActivity, true)
      })
    },
    
    // 处理用户活动
    handleUserActivity(event) {
      // 只要检测到用户活动且倒计时激活，就重置倒计时
      if (this.isCountdownActive) {
        this.countdown = this.autoLockTimeout
        this.countdownResetTime = Date.now()
        
        // 更新localStorage，供其他页面使用
        localStorage.setItem('lockscreen_reset_time', this.countdownResetTime.toString())
        
        // 开发环境下输出详细信息
        if (process.env.NODE_ENV === 'development') {
          console.log('检测到用户活动，倒计时已重置（localStorage已更新）:', {
            resetTo: this.autoLockTimeout + 's',
            eventType: event.type,
            target: event.target ? event.target.tagName : 'unknown'
          })
        }
      }
    },
    
    // 启动localStorage监听
    startLocalStorageListener() {
      // 监听localStorage变化
      window.addEventListener('storage', this.handleStorageChange)
      
      // 定期检查localStorage中的重置时间
      this.storageCheckTimer = setInterval(() => {
        this.checkLocalStorageReset()
      }, 100) // 每100ms检查一次
    },
    
    // 停止localStorage监听
    stopLocalStorageListener() {
      window.removeEventListener('storage', this.handleStorageChange)
      if (this.storageCheckTimer) {
        clearInterval(this.storageCheckTimer)
        this.storageCheckTimer = null
      }
    },
    
    // 处理localStorage变化
    handleStorageChange(event) {
      if (event.key === 'lockscreen_reset_time' && event.newValue) {
        this.handleExternalReset(parseInt(event.newValue))
      }
    },
    
    // 检查localStorage中的重置时间
    checkLocalStorageReset() {
      const storedResetTime = parseInt(localStorage.getItem('lockscreen_reset_time') || '0')
      if (storedResetTime > this.countdownResetTime) {
        this.handleExternalReset(storedResetTime)
      }
    },
    
    // 处理外部重置
    handleExternalReset(resetTime) {
      if (this.isCountdownActive) {
        this.countdown = this.autoLockTimeout
        this.countdownResetTime = resetTime
        
        console.log('检测到外部页面活动，倒计时已重置:', {
          resetTo: this.autoLockTimeout + 's',
          source: 'localStorage'
        })
      }
    },
    
    // 启动用户活动监听（已废弃，改为使用全局监听）
     startActivityListener() {
       const events = [
         'mousedown', 'mousemove', 'mouseup',
         'keydown', 'keyup', 'keypress',
         'scroll', 'wheel',
         'touchstart', 'touchmove', 'touchend',
         'click', 'dblclick',
         'focus', 'blur',
         'input', 'change',
         'resize', 'orientationchange',  // 窗口变化
         'contextmenu', 'selectstart',   // 右键菜单、选择文本
         'dragstart', 'dragend',         // 拖拽操作
         'pointerdown', 'pointermove', 'pointerup'  // 指针事件（兼容触摸和鼠标）
       ]
       
       // 在document上添加事件监听器，覆盖整个网页页面
       events.forEach(event => {
         document.addEventListener(event, this.resetActivityTimer, {
           passive: true,
           capture: true
         })
       })
       
       console.log('全局用户活动监听已启动 - 覆盖整个网页页面（包括header和所有下方内容）')
       console.log('监听事件类型:', events.join(', '))
     },
     
     // 停止用户活动监听
     stopActivityListener() {
       const events = [
         'mousedown', 'mousemove', 'mouseup',
         'keydown', 'keyup', 'keypress',
         'scroll', 'wheel',
         'touchstart', 'touchmove', 'touchend',
         'click', 'dblclick',
         'focus', 'blur',
         'input', 'change',
         'resize', 'orientationchange',  // 窗口变化
         'contextmenu', 'selectstart',   // 右键菜单、选择文本
         'dragstart', 'dragend',         // 拖拽操作
         'pointerdown', 'pointermove', 'pointerup'  // 指针事件（兼容触摸和鼠标）
       ]
       
       // 从document移除事件监听器
       events.forEach(event => {
         document.removeEventListener(event, this.resetActivityTimer, true)
       })
       
       console.log('全局用户活动监听已停止 - 已移除整个网页页面的所有监听器')
     },
     
     // 重置活动计时器
     resetActivityTimer(event) {
       this.lastActivityTime = Date.now()
       
       // 详细记录用户活动信息（可选，用于调试）
       if (event && event.type) {
         const target = event.target
         const tagName = target ? target.tagName : 'unknown'
         const className = target ? target.className : ''
         const eventInfo = {
           type: event.type,
           target: tagName,
           class: className,
           timestamp: new Date().toLocaleTimeString()
         }
         
         // 只在开发环境或需要调试时输出详细信息
         if (process.env.NODE_ENV === 'development') {
           console.log('用户活动检测:', eventInfo)
         }
       }
     },
     
     // 启动活动检查
     startActivityCheck() {
       if (this.activityCheckTimer) {
         clearInterval(this.activityCheckTimer)
       }
       
       this.activityCheckTimer = setInterval(() => {
         this.checkUserActivity()
       }, 1000) // 每秒检查一次
     },
     
     // 检查用户活动
      checkUserActivity() {
        if (!this.isCountdownActive) return
        
        const now = Date.now()
        
        // 如果有倒计时重置时间，使用它来计算剩余时间
        if (this.countdownResetTime) {
          const elapsedSinceReset = Math.floor((now - this.countdownResetTime) / 1000)
          this.countdown = Math.max(0, this.autoLockTimeout - elapsedSinceReset)
        } else {
          // 否则使用全局状态中的活动时间
          const globalLastActivityTime = this.$store.state.app.lastActivityTime
          const inactiveTime = Math.floor((now - globalLastActivityTime) / 1000)
          this.countdown = Math.max(0, this.autoLockTimeout - inactiveTime)
        }
        
        if (this.countdown <= 0) {
          this.onCountdownEnd()
        }
      },
     
     // 停止倒计时
     stopCountdown() {
       // 清理倒计时定时器
       if (this.countdownTimer) {
         clearInterval(this.countdownTimer)
         this.countdownTimer = null
       }
       
       // 清理活动检查定时器
       if (this.activityCheckTimer) {
         clearInterval(this.activityCheckTimer)
         this.activityCheckTimer = null
       }
       
       // 清理localStorage监听
       this.stopLocalStorageListener()
       
       // 清理localStorage状态
       localStorage.removeItem('lockscreen_countdown_active')
       localStorage.removeItem('lockscreen_timeout')
       localStorage.removeItem('lockscreen_reset_time')
       
       this.isCountdownActive = false
       this.countdown = this.autoLockTimeout
       this.countdownResetTime = null
       console.log('倒计时已停止，localStorage已清理')
     },
    
    // 倒计时结束处理
    onCountdownEnd() {
      console.log('倒计时结束，自动触发锁屏')
      this.stopCountdown()
      this.lockScreen() // 自动触发锁屏
    }
    
    // logout() {
    //   this.$store.dispatch('LogOut').then(() => {
    //     this.$router.push('/whitelLogin')
    //     location.reload()
    //   })
    // }
  }
}
</script>
<style lang="less" scoped>
.el-header {
  background-color: #79a0f1;
  z-index: 2;
  // height: 70px !important;
}
.el-footer {
  background-color: #79a0f1;
  color: #333;
  line-height: 60px;
  padding: 0px;
  overflow: hidden;
  z-index: 2;
}
.el-main {
  background-color: #ffffff;
  color: #333;
  padding: 0px;
}
// 细化滚动条
.el-main::-webkit-scrollbar {
  width: 0px;
  height: 4px;
  background-color: #ebeef5;
  cursor: pointer !important;
}
.el-main::-webkit-scrollbar-thumb {
  box-shadow: inset 0 0 6px rgba(255, 255, 255, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  background-color: #f6f9ff;
}
.el-main::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
  background: rgba(255, 255, 255, 1);
}
body > .el-container {
  margin-bottom: 40px;
}
.hmi-logo {
  width: auto;
  height: 40px;
}
.right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #ffffff;
      vertical-align: text-bottom;
      margin-left: 10px;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }
    .headerFirst{
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 50px;
      .headerLogo{
        display: flex;
        align-items: center;
        .headerStaion{
          span{
          color: #ffffff;
          font-size: 12px;
          padding: 8px 10px;
          border-radius: 25px;
          background: rgba(0,0,0,.1);
          font-weight: normal;
          white-space: nowrap;
          cursor: pointer;
            .svg-icon{
              margin-right: 5px;
            }
          }
          .spanFisrt{
            margin-right: 6px;
            margin-left: 15px;
          }
        }
      }
    }
    .headerSecond{
      color: #ffffff;
      display: flex;
    align-items: center;
      span{
        margin-right: 10px;
        font-size: 12px;
        font-weight: normal;
        white-space: nowrap;
        .svg-icon{
          margin-right: 5px;
        }
      }
      .loginout{
        cursor: pointer;
        margin-right: 0 !important;
        font-size: 14px;
        i{
          font-weight: 700;
        }
      }
    }
    .headerTwo{
      display: flex;
      margin-top: 6px;
    }
    .el-icon-lock{
      font-size: 14px;
    }
    .el-icon-time{
      margin-right: 5px;
    }
    ::v-deep .right-menu-item.hover-effect{
      font-size: 14px;
    margin-left: 0;
    padding-right: 0;
    }
    .zhezhao{
      position: fixed;
      width: 100%;
      height: 100%;
      left: 0;
      right: 0;
      bottom: 0;
      top: 0;
      background-color: rgba(0, 0, 0, 0.3);
      z-index: 9;

    }
    .userInfo{
        position: fixed;
    bottom: 15%;
    right: 10%;
    border-radius: 10px;
    background: #ffffff;
    padding: 30px 25px;
    width: 380px;
    z-index: 13;
    .title {
  margin: 0 auto 40px auto;
  text-align: center;
  color: #18245e;
  font-size: 24px;
}
  }
  .wrapbtn{
    margin-top: 20px;
  }
  .keyboard-mask{
    position: fixed;
    bottom: 15%;
    left: 5%;
    z-index: 999;
}
::v-deep .hg-theme-default .hg-button.hg-standardBtn {
    width: 24px !important;
    // height: 51px;
}
.jianpan img{
      width: 35px;
    margin-top: 10px;
    margin-left: 10px;
    cursor: pointer;
}
.greenStyle{
  background-repeat: no-repeat;
  background-size: cover;
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: 0;
  top: 0;
  bottom: 0;
  right: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}
.drop {
    background: rgba(255,255,255,0.1);
    border-radius: 10px;
    border: 1px solid rgba(255,255,255,0.2);
    position: absolute;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    display: block;
    margin: 0 auto;
    h1 {
      text-align: center;
    font-family: Arial;
    color: #FFF;
    font-size: 90px;
    padding: 20px 30px;
    text-transform: uppercase;
}
.drop1 {
    width: 47%;
    height: 150px;
    top: 56px;
    left: 0;
    right: 0;
    z-index: 2;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}
.drop2 {
    width: 40px;
    height: 40px;
    top: -60px;
    left: -80%;
    right: 0;
    z-index: 4;
}
.drop3 {
    width: 60px;
    height: 60px;
    bottom: -80px;
    right: 30px;
    z-index: 3;
}
.drop4 {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    top: -55px;
    right: 20px;
}
}
.arrowStyle{
  color: #ffffff;
  transform: rotate(90deg);
}
</style>
<style lang="less">
#hmiTabs .el-tabs__header {
  margin: 0 0 0px;
  display: none;
}
</style>

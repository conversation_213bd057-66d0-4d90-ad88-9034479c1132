<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.planCount') }}
        </template>
        <el-input
          ref="webWipPlanCount"
          v-model="webWipPlanCount"
          :readonly="true"
          clearable
          size="mini"
        />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.okCount') }}
        </template>
        <el-input
          ref="webWipOKCount"
          v-model="webWipOKCount"
          :readonly="true"
          clearable
          size="mini"
        />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.shortCount') }}
        </template>
        <el-input
          ref="webWipShortCount"
          v-model="webWipShortCount"
          clearable
          size="mini"
          @keydown.native="handleKeydown"
        />
      </el-descriptions-item>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          {{ $t('lang_pack.dialogMain.noReadCount') }}
        </template>
        <el-input
          ref="webWipNoReadCount"
          v-model="webWipNoReadCount"
          clearable
          size="mini"
          @keydown.native="handleKeydown"
        />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top: 10px; text-align: right">
      <el-button type="primary" @click="handleSendInfo">{{ $t('lang_pack.dialogMain.confirm') }}</el-button>
    </div>
  </div>
</template>

<script>
import Cookies from 'js-cookie'
export default {
  components: {},
  props: {
    tag_key_list: {
      type: Object,
      default: null
    }
  },
  // 数据模型
  data() {
    return {
      webWipPlanCount: '',
      webWipOKCount: '',
      webWipShortCount: '',
      webWipNoReadCount: ''
    }
  },
  mounted: function() {
  },
  created: function() {
    this.webWipPlanCount = this.tag_key_list.WebWipPlanCount
    this.webWipOKCount = this.tag_key_list.WebWipOKCount
    this.$nextTick((x) => {
      // 正确写法
      this.$refs.webWipShortCount.focus()
    })
  },
  methods: {
    handleKeydown(e) {
      // 允许输入数字、Backspace、Delete、左箭头、右箭头
      if (
        !(
          (e.keyCode >= 48 && e.keyCode <= 57) || // 数字
          e.keyCode === 8 || // Backspace
          e.keyCode === 46 || // Delete
          (e.keyCode >= 37 && e.keyCode <= 40)
        ) // 方向键
      ) {
        e.preventDefault()
      }
    },
    handleSendInfo() {
      console.log(this.tag_key_list)
      if (this.webWipShortCount === '') {
        this.$message({ message: 'Please enter the quantity of missing boards', type: 'info' })
        return
      }
      if (this.webWipNoReadCount === '') {
        this.$message({ message: 'Please enter the number of NoReads', type: 'info' })
        return
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebWipShortCount,
        TagValue: this.webWipShortCount
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebWipNoReadCount,
        TagValue: this.webWipNoReadCount
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebWipManual,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic =
        'SCADA_WRITE/' + this.tag_key_list.WebWipShortCount.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 150px;
}
</style>

<template>
  <div class="app-container">
    <el-card v-if="crud.props.searchToggle" ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务号：">
                <!-- 任务号： -->
                <el-input v-model="query.task_num" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="物料编码:">
                <!-- 物料编码 -->
                <el-input v-model="query.material_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务类型:">
                <!-- 任务类型 -->
                <el-select v-model="query.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.APS_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item label="任务状态:">
                <!-- 任务状态 -->
                <el-select v-model="query.task_status" clearable filterable>
                  <el-option
                    v-for="item in dict.PROD_TASK_STATUS"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <rrOperation />
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="always" style="margin-top: 10px">
      <!--工具栏-->
      <crudOperation show="" :permission="permission" />

      <el-row :gutter="20">
        <el-col :span="24">
          <!--表单渲染-->
          <el-drawer
            append-to-body
            :wrapper-closable="false"
            :title="crud.status.title"
            :visible="crud.status.cu > 0"
            :before-close="crud.cancelCU"
            size="750px"
          >
          <div class="drawer-content" style="height: calc(100vh - 60px); overflow-y: auto;">
            <el-form
              ref="form"
              class="el-form-wrap"
              :model="form"
              :rules="rules"
              size="small"
              label-width="145px"
              :inline="true"
            >
              <el-form-item :label="$t('lang_pack.fjrm.taskFrom')" prop="task_from">
                <!-- 任务来源 -->
                <el-select v-model="form.task_from" clearable filterable>
                  <el-option
                    v-for="item in dict.DATA_SOURCES"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.taskWay')" prop="task_way">
                <!-- 任务方式 -->
                <el-select v-model="form.task_way" clearable filterable>
                  <el-option
                    v-for="item in dict.TASK_WAY"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.taskType')" prop="task_type">
                <!-- 任务类型 -->
                <el-select v-model="form.task_type" clearable filterable>
                  <el-option
                    v-for="item in dict.APS_TASK_TYPE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.wareHouse')" prop="ware_house">
                <!-- 库区域 -->
                <el-select v-model="form.ware_house" clearable filterable>
                  <el-option
                    v-for="item in dict.WARE_HOUSE"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.fromStockCode')" prop="from_stock_code">
                <!-- 起始库位 -->
                <el-select v-model="form.from_stock_code" clearable filterable>
                  <el-option
                    v-for="item in stockDataTable"
                    :key="item.stock_code"
                    :label="item.stock_des"
                    :value="item.stock_code"
                  >
                    <span style="float: left">{{ item.stock_des }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stock_code
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.toStockCode')" prop="to_stock_code">
                <!-- 目标库位 -->
                <el-select v-model="form.to_stock_code" clearable filterable>
                  <el-option
                    v-for="item in stockDataTable"
                    :key="item.stock_code"
                    :label="item.stock_des"
                    :value="item.stock_code"
                  >
                    <span style="float: left">{{ item.stock_des }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.stock_code
                    }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.lotNum')" prop="lot_num">
                <!-- 批次号 -->
                <el-input v-model="form.lot_num" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.materialCode')" prop="material_code">
                <!-- 物料编码 -->
                <el-input v-model="form.material_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.alloyGrade')" prop="grade">
                <!-- 合金牌号 -->
                <el-input v-model="form.grade" />
              </el-form-item>
              <!-- <el-form-item :label="$t('lang_pack.fjrm.width')" prop="width">
                 总重量(KG) 
                <el-input v-model.number="form.width"  />
              </el-form-item> -->
              <el-form-item :label="$t('lang_pack.fjrm.composition_error_code')" prop="composition_error_code">
                <!-- 成分异常代码 -->
                <el-input v-model="form.composition_error_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.errorMin')" prop="error_min">
                <!-- 化学成分异常最小值 -->
                <el-input v-model="form.error_min"  />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.errorMax')" prop="error_max">
                <!-- 化学成分异常最大值 -->
                <el-input v-model="form.error_max"  />
              </el-form-item>

              <!--任务排序
              <el-form-item :label="$t('lang_pack.fjrm.taskOrder')" prop="task_order">
                <el-input v-model.number="form.task_order" type="number" />
              </el-form-item>-->
              <el-form-item :label="$t('lang_pack.fjrm.wharfCode')" prop="wharf_code">
                <!-- 码头编码 -->
                <el-input v-model="form.wharf_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.wasteBoxCode')" prop="waste_box_code">
                <!-- 废料框编码 -->
                <el-input v-model="form.waste_box_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.material_shape_code')" prop="material_shape_code">
                <!-- 废料形态代码 -->
                <el-input v-model="form.material_shape_code" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.si')" prop="si_a">
                <!-- 硅含量 -->
                <el-input v-model="form.si_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.si_b')" prop="si_b">
                <!-- 硅是否超标 -->
                <el-select v-model="form.si_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.fe')" prop="fe_a">
                <!-- 铁含量 -->
                <el-input v-model="form.fe_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.fe_b')" prop="fe_b">
                <!-- 铁是否超标 -->
                <el-select v-model="form.fe_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.cu')" prop="cu_a">
                <!-- 铜含量 -->
                <el-input v-model="form.cu_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.cu_b')" prop="cu_b">
                <!-- 铜是否超标 -->
                <el-select v-model="form.cu_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.mn')" prop="mn_a">
                <!-- 锰含量 -->
                <el-input v-model="form.mn_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.mn_b')" prop="mn_b">
                <!-- 锰是否超标 -->
                <el-select v-model="form.mn_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.mg')" prop="mg_a">
                <!-- 镁含量 -->
                <el-input v-model="form.mg_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.mg_b')" prop="mg_b">
                <!-- 镁是否超标 -->
                <el-select v-model="form.mg_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.ni')" prop="ni_a">
                <!-- 镍含量 -->
                <el-input v-model="form.ni_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.ni_b')" prop="ni_b">
                <!-- 镍是否超标 -->
                <el-select v-model="form.ni_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.zn')" prop="zn_a">
                <!-- 锌含量 -->
                <el-input v-model="form.zn_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.zn_b')" prop="zn_b">
                <!-- 锌是否超标 -->
                <el-select v-model="form.zn_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.ti')" prop="ti_a">
                <!-- 钛含量 -->
                <el-input v-model="form.ti_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.ti_b')" prop="ti_b">
                <!-- 钛是否超标 -->
                <el-select v-model="form.ti_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.cr')" prop="cr_a">
                <!-- 铬含量 -->
                <el-input v-model="form.cr_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.cr_b')" prop="cr_b">
                <!-- 铬是否超标 -->
                <el-select v-model="form.cr_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.na')" prop="na_a">
                <!-- 钠含量 -->
                <el-input v-model="form.na_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.na_b')" prop="na_b">
                <!-- 钠是否超标 -->
                <el-select v-model="form.na_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item :label="$t('lang_pack.fjrm.ca')" prop="ca_a">
                <!-- 钙含量 -->
                <el-input v-model="form.ca_a" />
              </el-form-item>
              <el-form-item :label="$t('lang_pack.fjrm.ca_b')" prop="ca_b">
                <!-- 钙是否超标 -->
                <el-select v-model="form.ca_b" clearable filterable>
                  <el-option
                    v-for="item in dict.LOCK_FLAG"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  >
                    <span style="float: left">{{ item.label }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ item.value }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

            </el-form>
            <el-divider />
            <div style="text-align: center">
              <el-button size="small" icon="el-icon-close" plain @click="crud.cancelCU">{{
                $t('lang_pack.commonPage.cancel') }}</el-button>
              <!-- 取消 -->
              <el-button
                type="primary"
                size="small"
                icon="el-icon-check"
                :loading="crud.status.cu === 2"
                @click="crud.submitCU"
              >{{ $t('lang_pack.commonPage.confirm') }}</el-button>
              <!-- 确认 -->
            </div>
            </div>
          </el-drawer>

          <!--表格渲染-->
          <el-table
            ref="table"
            v-loading="crud.loading"
            border
            size="small"
            :data="crud.data"
            style="width: 100%"
            :cell-style="crud.cellStyle"
            :height="height"
            :highlight-current-row="true"
            @header-dragend="crud.tableHeaderDragend()"
            @selection-change="crud.selectionChangeHandler"
          >
            <el-table-column type="selection" width="55" fixed />

            <!-- 任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_num"
              :label="$t('lang_pack.fjrm.taskNum')"
              width="180"
              align="center"
            />
            <!-- 任务状态 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_status"
              :label="$t('lang_pack.fjrm.taskStatus')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.PROD_TASK_STATUS[scope.row.task_status] }}
              </template>
            </el-table-column>
            <!-- 排序 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_order"
              :label="$t('lang_pack.fjrm.taskOrder')"
              width="80"
              align="center"
            />

            <!-- 任务来源 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_from"
              :label="$t('lang_pack.fjrm.taskFrom')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.DATA_SOURCES[scope.row.task_from] }}
              </template>
            </el-table-column>
            <!-- 任务方式 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_way"
              :label="$t('lang_pack.fjrm.taskWay')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.TASK_WAY[scope.row.task_way] }}
              </template>
            </el-table-column>
            <!-- 任务类型 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="task_type"
              :label="$t('lang_pack.fjrm.taskType')"
              width="120"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.APS_TASK_TYPE[scope.row.task_type] }}
              </template>
            </el-table-column>
            <!-- 库区域 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ware_house"
              :label="$t('lang_pack.fjrm.wareHouse')"
              width="80"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ dict.label.WARE_HOUSE[scope.row.ware_house] }}
              </template>
            </el-table-column>
            <!-- 起始库位描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="from_stock_code"
              :label="$t('lang_pack.fjrm.fromStockCode')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStockDes(scope.row.from_stock_code) }}
              </template>
            </el-table-column>
            <!-- 目标库位描述 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="to_stock_code"
              :label="$t('lang_pack.fjrm.toStockCode')"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                <!--取到当前单元格-->
                {{ getStockDes(scope.row.to_stock_code) }}
              </template>
            </el-table-column>
            <!-- 批次号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="lot_num"
              :label="$t('lang_pack.fjrm.lotNum')"
              width="80"
              align="center"
            />
            <!-- 物料编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_code"
              :label="$t('lang_pack.fjrm.materialCode')"
              width="80"
              align="center"
            />
            <!-- 总重量(KG) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="width"
              :label="$t('lang_pack.fjrm.width')"
              width="80"
              align="center"
            />
            <!-- 化学成分异常最小值 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="error_min"
              :label="$t('lang_pack.fjrm.errorMin')"
              width="80"
              align="center"
            />
            <!-- 化学成分异常最大值 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="error_max"
              :label="$t('lang_pack.fjrm.errorMax')"
              width="80"
              align="center"
            />
            <!-- 码头编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="wharf_code"
              :label="$t('lang_pack.fjrm.wharfCode')"
              width="80"
              align="center"
            />
            <!-- 废料框编码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="waste_box_code"
              :label="$t('lang_pack.fjrm.wasteBoxCode')"
              width="150"
              align="center"
            />

            <!-- 拆分源任务号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_task_num"
              label="拆分源任务号"
              width="150"
              align="center"
            />
            <!-- 拆分总重量(KG) -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="split_width"
              label="拆分总重量"
              width="150"
              align="center"
            />

            <el-table-column
              :label="$t('lang_pack.fjrm.lockFlag')"
              align="center"
              width="120"
              prop="lock_flag"
            >
              <!-- 是否锁定 -->
              <template slot-scope="scope">
                <!--取到当前单元格-->
                <el-switch v-model="scope.row.lock_flag" active-color="#13ce66" inactive-color="#ff4949" active-value="Y" inactive-value="N" />
              </template>
            </el-table-column>
            
            <!-- 合金牌号 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="grade"
              label="合金牌号"
              width="80"
              align="center"
            />

            <!-- 成分异常代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="composition_error_code"
              label="成分异常代码"
              width="100"
              align="center"
            />

            <!-- 废料形态代码 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="material_shape_code"
              label="废料形态代码"
              width="100"
              align="center"
            />

            <!-- 硅含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="si_a"
              label="硅含量"
              width="80"
              align="center"
            />

            <!-- 硅是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="si_b"
              label="硅是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.si_b] }}
              </template>
            </el-table-column>

            <!-- 铁含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="fe_a"
              label="铁含量"
              width="80"
              align="center"
            />

            <!-- 铁是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="fe_b"
              label="铁是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.fe_b] }}
              </template>
            </el-table-column>

            <!-- 铜含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cu_a"
              label="铜含量"
              width="80"
              align="center"
            />

            <!-- 铜是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cu_b"
              label="铜是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.cu_b] }}
              </template>
            </el-table-column>

            <!-- 锰含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="mn_a"
              label="锰含量"
              width="80"
              align="center"
            />

            <!-- 锰是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="mn_b"
              label="锰是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.mn_b] }}
              </template>
            </el-table-column>

            <!-- 镁含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="mg_a"
              label="镁含量"
              width="80"
              align="center"
            />

            <!-- 镁是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="mg_b"
              label="镁是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.mg_b] }}
              </template>
            </el-table-column>

            <!-- 镍含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ni_a"
              label="镍含量"
              width="80"
              align="center"
            />

            <!-- 镍是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ni_b"
              label="镍是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.ni_b] }}
              </template>
            </el-table-column>

            <!-- 锌含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="zn_a"
              label="锌含量"
              width="80"
              align="center"
            />

            <!-- 锌是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="zn_b"
              label="锌是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.zn_b] }}
              </template>
            </el-table-column>

            <!-- 钛含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ti_a"
              label="钛含量"
              width="80"
              align="center"
            />

            <!-- 钛是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ti_b"
              label="钛是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.ti_b] }}
              </template>
            </el-table-column>

            <!-- 铬含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cr_a"
              label="铬含量"
              width="80"
              align="center"
            />

            <!-- 铬是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="cr_b"
              label="铬是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.cr_b] }}
              </template>
            </el-table-column>

            <!-- 钠含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="na_a"
              label="钠含量"
              width="80"
              align="center"
            />

            <!-- 钠是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="na_b"
              label="钠是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.na_b] }}
              </template>
            </el-table-column>

            <!-- 钙含量 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ca_a"
              label="钙含量"
              width="80"
              align="center"
            />

            <!-- 钙是否超标 -->
            <el-table-column
              :show-overflow-tooltip="true"
              prop="ca_b"
              label="钙是否超标"
              width="100"
              align="center"
            >
              <template slot-scope="scope">
                {{ dict.label.LOCK_FLAG[scope.row.ca_b] }}
              </template>
            </el-table-column>

            <!-- Table单条操作-->
            <el-table-column
              :label="$t('lang_pack.commonPage.operate')"
              align="center"
              width="180"
              fixed="right"
            >
              <!-- 操作 -->
              <template slot-scope="scope">
                <udOperation :data="scope.row" :permission="permission" :disabled-dle="false" :delete-del="false" :delete-edit="false">
                  <template slot="right">
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                      :disabled="scope.row.task_status != 'PLAN'"
                      @click="taskStatusExecute(scope.row.task_id,'WORK')"
                    >执行</el-button>
                    <el-button
                      slot="reference"
                      type="text"
                      size="small"
                      @click="taskStatusExecute(scope.row.task_id,'CANCEL')"
                    >取消</el-button>
                  </template>
                </udOperation>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <pagination />
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script>
import crudTask from '@/api/dcs/project/fjrm/intefTask/intefTask'
import Cookies from 'js-cookie'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
const defaultForm = {
  task_id: '',
  task_num: '',
  task_from: '',
  task_way: '',
  task_type: '',
  ware_house: '',
  from_stock_code: '',
  to_stock_code: '',
  lot_num: '',
  material_code: '',
  width: '',
  error_min: '',
  error_max: '',
  task_order: '',
  task_status: '',
  wharf_code: '',
  waste_box_code: '',
  // grade:'',
  split_task_num: '',
  split_width: '',
  lock_flag: 'N'
}
export default {
  name: 'INTEF_TASK',
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: '任务',
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'task_id',
      // 排序
      sort: ['task_id desc'],
      // CRUD Method
      crudMethod: { ...crudTask },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: false,
        reset: true
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 300,
      permission: {
        add: ['admin', 'dcs_wms_intef_task :add']
      },
      rules: {
        task_from: [{ required: true, message: '请选择任务来源', trigger: 'blur' }],
        task_way: [{ required: true, message: '请选择任务方式', trigger: 'blur' }],
        task_type: [{ required: true, message: '请选择任务类型', trigger: 'blur' }]
      },

      // 库位
      stockDataTable: []
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG', 'APS_TASK_TYPE', 'DATA_SOURCES', 'TASK_WAY', 'WARE_HOUSE', 'PROD_TASK_STATUS','LOCK_FLAG'],
  mounted: function() {
    const that = this
    that.crud.props.searchToggle = false // 默认先隐藏
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 300
    }
  },
  created: function() {
    this.getSelStock()
  },
  methods: {

    // 库位
    getSelStock() {
      const query = {
        user_name: Cookies.get('userName'),
        ware_house: '',
        exclude_stock_code: '',
        enable_flag: 'Y'
      }
      crudTask.stockSel(query).then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.count === 0) {
            this.stockDataTable = []
          } else {
            this.stockDataTable = defaultQuery.data
          }
        } else {
          this.stockDataTable = []
          this.$message({
            message: defaultQuery.msg,
            type: 'error'
          })
        }
      }).catch(() => {
        this.$message({
          message: '查询异常',
          type: 'error'
        })
      })
    },
    getStockDes(stock_code) {
      var item = this.stockDataTable.find(item => item.stock_code === stock_code)
      if (item !== undefined) {
        return item.stock_des
      }
      return stock_code
    },

    // 执行
    taskStatusExecute(taskId, taskStatus) {
      this.$confirm(`确定要执行当前任务吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          const query = {
            task_id: taskId,
            task_status: taskStatus,
            user_name: Cookies.get('userName')
          }
          crudTask.taskStatusUpd(query).then(res => {
            const defaultQuery = JSON.parse(JSON.stringify(res))
            if (defaultQuery.code === 0) {
              this.$message.success('执行成功')
              this.crud.toQuery()
            } else {
              this.$message({
                message: defaultQuery.msg || '执行失败',
                type: 'error'
              })
            }
          }).catch(() => {
            this.$message({
              message: '执行失败',
              type: 'error'
            })
          })
        })
        .catch(() => {})
    }

  }
}
</script>
<style scoped lang="less">
.app-container {
    .inbOutNum {
        margin: 0 5px;
        color: #409eff;
        cursor: pointer;
    }

    .hover-row {
        td {
            .el-dropdown {
                .el-button--text {
                    color: #fff;
                }
            }

            .inbOutNum {
                color: #fff;
            }
        }
    }

    .current-row {
        td {
            .el-dropdown {
                .el-button--text {
                    color: #fff;
                }
            }

            .inbOutNum {
                color: #fff;
            }
        }
    }

    ::v-deep .el-dialog {
        margin-top: 10vh !important;
    }
}
</style>

import request from '@/utils/request'

// 上机接口 - 开始生产
export function startProduction(data) {
  return request({
    url: 'aisEsbApi/eap/project/hsql/index/StartProduction',
    method: 'post',
    data
  })
}

/**
 * 手动放行 - NG原因保存
 * 根据工号、当前作业工单批号、读码信息、读码面次、维护NG原因、机台编号进行手动放行
 * @param {Object} data 请求参数
 * @param {string} data.EmpID 工号
 * @param {string} data.LotNum 工单批号
 * @param {string} data.ReadInfo 读码信息
 * @param {string} data.ReadMC 读码面次
 * @param {string} data.NGReason 维护NG原因
 * @param {string} data.StationCode 机台编号
 * @returns {Promise} 返回处理结果
 */
export function ngReasonSaved(data) {
  return request({
    url: '/aisEsbApi/eap/project/hsql/index/NGReasonSaved',
    method: 'post',
    data
  })
}

/**
 * 获取物料信息
 * 根据物料编码获取物料的厚度、孔径、孔数信息
 * @param {Object} data 请求参数
 * @param {string} data.PartNo 物料编码
 * @returns {Promise} 返回物料信息
 */
export function getMaterialInfo(data) {
  return request({
    url: '/aisEsbApi/eap/project/hsql/index/ReturnOPInformation',
    method: 'post',
    data
  })
}

// 导出配方维护信息
export function EapRecipeExport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/hsql/index/EapRecipeExport',
    method: 'post',
    responseType: 'blob',
    data
  })
}

// 导入配方维护信息
export function EapRecipeImport(data) {
  return request({
    url: 'aisEsbWeb/eap/project/hsql/index/EapRecipeImport',
    method: 'post',
    responseType: 'blob',
    data
  })
}

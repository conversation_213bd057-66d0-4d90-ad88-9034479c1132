<template>
  <div class="app-container">
    <el-row :gutter="20" style="margin-right: 0px; padding: 0px">
      <el-col :span="24">
        <el-card shadow="never" class="wrapCard">
          <el-form ref="query" :inline="true" size="small" label-width="100px">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-12 col-12">
                <div class="formChild col-md-12 col-12 barcode">
                  <el-form-item label="网框码：">
                    <el-input ref="wkBarCode" v-model="wkBarCode" clearable size="small" @keyup.enter.native="scanWkBarcode" />
                  </el-form-item>
                  <el-button type="primary" @click="scanWkBarcode">确认</el-button>
                </div>
                <div class="formChild col-md-12 col-12 barcode">
                  <el-form-item label="料框码：">
                    <el-input ref="lkBarCode" v-model="lkBarCode" clearable size="small" @keyup.enter.native="scanLkBarcode" />
                  </el-form-item>
                  <el-button type="primary" @click="scanLkBarcode">确认</el-button>
                </div>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card shadow="never" class="wrapCard" style="margin-top: 10px;">
          <el-form ref="query" :inline="true" size="small" label-width="100px">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-10 col-12">
                <div class="formChild col-md-3 col-12">
                  <el-form-item label="当前工单：">
                    <el-input v-model="monitorData.LotID.value " disabled clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item label="当前料号：">
                    <el-input v-model="monitorData.PartNo.value" disabled clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-6 col-12 ">
                  <el-form-item label="当前网框码：">
                    <el-input v-model="monitorData.WkBarCode.value" disabled clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-6 col-12 ">
                  <el-form-item label="当前板件:">
                    <el-input v-model="monitorData.PanelID.value" disabled clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-6 col-12 ">
                  <el-form-item label="当前料框码：">
                    <el-input v-model="monitorData.LkBarCode.value" disabled clearable size="small" />
                  </el-form-item>
                </div>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card shadow="never" class="wrapCard" style="margin-top: 10px;">
          <el-form ref="query" :inline="true" size="small" label-width="130px">
            <div class="wrapElForm">
              <div class="wrapElFormFirst col-md-10 col-12">

                <div class="formChild col-md-3 col-12">
                  <el-form-item label="工单:">
                    <!-- 工单 -->
                    <el-input v-model="query.lot_num" clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item :label="$t('lang_pack.vie.partNum') + ':'">
                    <!-- 料号 -->
                    <el-input v-model="query.material_code" clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item label="板件:">
                    <!-- 板件 -->
                    <el-input v-model="query.panel_barcode" clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item label="网框码:">
                    <!-- 网框码 -->
                    <el-input v-model="query.wk_barcode" clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-3 col-12">
                  <el-form-item label="料框码:">
                    <!-- 料框码 -->
                    <el-input v-model="query.lk_barcode" clearable size="small" />
                  </el-form-item>
                </div>
                <div class="formChild col-md-4 col-12">
                  <el-form-item :label="$t('view.form.timePeriod') + ': '" label-width="120px">
                    <div class="block">
                      <el-date-picker
                        v-model="query.item_date"
                        type="datetimerange"
                        size="small"
                        align="right"
                        unlink-panels
                        range-separator="~"
                        :start-placeholder="$t('view.form.timePeriodStart')"
                        :end-placeholder="$t('view.form.timePeriodEnd')"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        style="width:100%"
                      />
                    </div>
                  </el-form-item>
                </div>
              </div>
              <div class="wrapElFormSecond formChild col-md-2 col-12">
                <el-form-item>
                  <rrOperation />
                </el-form-item>
              </div>
            </div>
          </el-form>
        </el-card>
        <el-card shadow="never" style="margin-top: 10px">
          <el-row :gutter="20">
            <el-col :span="24">
              <el-table ref="table" v-loading="crud.loading" border size="small" :data="crud.data" style="width: 100%" :cell-style="crud.cellStyle" :height="height" highlight-current-row @header-dragend="crud.tableHeaderDragend()">
                <el-table-column :show-overflow-tooltip="true" prop="item_date" label="时间" />
                <el-table-column :show-overflow-tooltip="true" prop="user_name" label="作业者" />
                <el-table-column :show-overflow-tooltip="true" prop="lot_num" label="工单" />
                <el-table-column :show-overflow-tooltip="true" prop="material_code" label="料号" />
                <el-table-column :show-overflow-tooltip="true" prop="plan_count" label="计划数量" />
                <el-table-column :show-overflow-tooltip="true" prop="wk_barcode" label="网框码" />
                <el-table-column :show-overflow-tooltip="true" prop="lk_barcode" label="料框码" />
                <el-table-column :show-overflow-tooltip="true" prop="panel_barcode" label="板件码" />
              </el-table>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <div style="margin-top: 5px;text-align:right;">
                <el-button-group>
                  <el-button
                    type="primary"
                  >
                    {{ $t('view.pagination.total') }}: {{ page.total }}</el-button>
                  <el-button
                    type="primary"
                  >
                    {{ $t('view.pagination.current') }}{{ nowPageIndex }}{{ $t('view.pagination.unit') }}</el-button>
                  <el-button
                    type="primary"
                    @click="pageQuery('pre')"
                  >
                    &lt;&nbsp;{{ $t('view.pagination.previous') }}</el-button>
                  <el-button
                    type="primary"
                    @click="pageQuery('next')"
                  >
                    {{ $t('view.pagination.next') }}&nbsp;&gt;</el-button>
                </el-button-group>
              </div>
            </el-col>
          </el-row>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script>
import Cookies from 'js-cookie'
import crudWorkOrder from '@/api/eap/project/tlps/index'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import axios from 'axios'
import { selCellIP } from '@/api/core/center/cell'
import { sel as selStation } from '@/api/core/factory/sysStation'
import mqtt from 'mqtt'
import { MQTT_USERNAME, MQTT_PASSWORD } from '@/utils/emq.js'
const defaultForm = {

}
export default {
  components: { crudOperation, rrOperation, udOperation, pagination },
  cruds() {
    return CRUD({
      title: this.parent.$t('lang_pack.vie.formulaMainten'),
      // 登录用户
      userName: Cookies.get('userName'),
      // 唯一字段
      idField: 'master_flow_id',
      // 排序
      sort: ['item_date desc'],
      // CRUD Method
      crudMethod: { ...crudWorkOrder },
      // 按钮显示
      optShow: {
        add: true,
        edit: true,
        del: true,
        reset: true
      },
      query: {
        tableSize: 20
      }
    })
  },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  data() {
    return {
      nowPageIndex: 1, // 当前页数
      pageList: [],
      wkBarCode:'',
      lkBarCode:'',
      barcode:'',
      height: document.documentElement.clientHeight - 460,
      formData: {
        tag_value: ''
      },
      page: {
        total: 0
      },
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      // MQTT配置全局变量
      clientMqtt: null,
      clientChangeMqtt: null,
      mqttConnStatus: false, // MQTT连接状态(true、false)
      mqttChangeStatus: false, // 接收收扳机的ip
      optionsMqtt: {
        // host: 'broker.emqx.io',
        // port: 8083,
        endpoint: '/mqtt',
        clean: true, // 保留会话
        connectTimeout: 5000, // 超时时间
        // reconnectPeriod: 4000,  // 重连时间间隔
        // keepalive: 60, //心跳
        // 认证信息
        clientId:
          'ScadaEsb_' +
          Cookies.get('userName') +
          '_' +
          Math.random().toString(16).slice(2, 10), // 创建唯一UID
        username: MQTT_USERNAME,
        password: MQTT_PASSWORD,
        // protocolId: 'MQTT',
        // protocolVersion: 4,
        cleansession: false
      },
      stationAttr: '',
      client_id: '',
      station_code: '',
      monitorData: {
        LotID: {
          client_code: `Plc`,
          group_code: 'PlcCraft',
          tag_code: 'LotID',
          tag_des: '批号',
          value: ''
        },
        PartNo: {
          client_code: `Plc`,
          group_code: 'PlcCraft',
          tag_code: 'PartNo',
          tag_des: '料号',
          value: ''
        },
        PanelID: {
          client_code: `Plc`,
          group_code: 'PlcCraft',
          tag_code: 'PanelID2',
          tag_des: '追溯码(新增)',
          value: ''
        },
        WkBarCode: {
          client_code: `Ais`,
          group_code: 'AisStatus',
          tag_code: 'WkBarCode',
          tag_des: '网框码',
          value: ''
        },
        LkBarCode: {
          client_code: `Ais`,
          group_code: 'AisStatus',
          tag_code: 'LkBarCode',
          tag_des: '料框码',
          value: ''
        },
      },
    }
  },
  mounted: function() {
    this.getStationAttr()
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 460
    }
  },
  created() {
    this.getCellIp()
  },
  methods: {
    //============================================标准功能=============================
    //获取工位信息
    getStationAttr() {
      const query = {
        stationCodeDes: this.$route.query.station_code,
        user_name: Cookies.get('userName')
      }
      selStation(query).then(res => {
        if (res.code === 0) {
          if (res.data.length > 0) {
            this.stationAttr = res.data[0].station_attr
            this.station_code = res.data[0].station_code
            this.client_id = res.data[0].station_short_code
            Object.keys(this.monitorData).forEach((key) => {
              this.monitorData[key].client_code = `${this.stationAttr}` + this.monitorData[key].client_code
            })
            return
          }
          this.stationAttr = ''
        }
      })
    },
    //获取CELL_IP
    getCellIp() {
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: 1,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
            setTimeout(() => {
              this.toStartWatch()
              this.getTagValue()
            }, 3000)
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('tlps.index.queryException'), type: 'error' })
        })
    },
    getTagValue() {
      // 检查MQTT连接状态
      if (!this.mqttConnStatus) {
        console.warn('MQTT未连接，无法获取标签值')
        return
      }
      var readTagArray = []
      Object.keys(this.monitorData).forEach((key) => {
        var client_code = this.monitorData[key].client_code
        var group_code = this.monitorData[key].group_code
        var tag_code = this.monitorData[key].tag_code
        var readTag = {}
        readTag.tag_key = client_code + '/' + group_code + '/' + tag_code
        readTagArray.push(readTag)
      })
      var method = '/cell/core/scada/CoreScadaReadTag'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      axios
        .post(path, readTagArray, {
          headers: {
            'Content-Type': 'application/json'
          }
        })
        .then((res) => {
          const defaultQuery = JSON.parse(JSON.stringify(res.data))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data && defaultQuery.data.length > 0) {
              var result = defaultQuery.data
              // 更新监控数据
              Object.keys(this.monitorData).forEach((key) => {
                var client_code = this.monitorData[key].client_code
                var group_code = this.monitorData[key].group_code
                var tag_code = this.monitorData[key].tag_code
                var tag_key = client_code + '/' + group_code + '/' + tag_code
                const item = result.filter((item) => item.tag_key === tag_key)
                if (item.length > 0) {
                  this.monitorData[key].value =
                    item[0].tag_value === undefined ? '' : item[0].tag_value
                }
              })
            } else {
              console.log('未获取到标签值数据')
            }
          } else {
            console.error('获取标签值失败:', defaultQuery.msg)
          }
        })
        .catch((ex) => {
          this.$message({ message: this.$t('tlps.index.queryExceptionWithMsg', { msg: ex }), type: 'error' })
        })
    },
    toStartWatch() {
      // 如果已启动则先结束之前的连接
      if (this.mqttConnStatus) {
        this.clientMqtt.end()
        this.mqttConnStatus = false
      }
      var connectUrl = 'ws://' + this.cellIp + ':' + this.mqttPort + '/mqtt'
      // mqtt连接
      this.clientMqtt = mqtt.connect(connectUrl, this.optionsMqtt) // 开启连接
      this.clientMqtt.on('connect', () => {
        console.log('MQTT连接成功，开始初始化数据...')
        this.mqttConnStatus = true
        Object.keys(this.monitorData).forEach((key) => {
          var client_code = this.monitorData[key].client_code
          var group_code = this.monitorData[key].group_code
          var tag_code = this.monitorData[key].tag_code
          this.topicSubscribe(
            'SCADA_CHANGE/' + client_code + '/' + group_code + '/' + tag_code
          )
        })
        this.$message({
          message: this.$t('tlps.index.connectionSuccess'),
          type: 'success'
        })
        this.getTagValue()
      })

      // MQTT连接失败
      this.clientMqtt.on('error', () => {
        this.$message({
          message: this.$t('tlps.index.connectionFailed'),
          type: 'error'
        })
        this.clientMqtt.end()
      })
      // 断开发起重连(异常)
      this.clientMqtt.on('reconnect', () => {
        this.$message({
          message: this.$t('tlps.index.connectionDisconnected'),
          type: 'error'
        })
      })
      this.clientMqtt.on('disconnect', () => {})
      this.clientMqtt.on('close', () => {})
      // 接收消息处理
      this.clientMqtt.on('message', (topic, message) => {
        try {
          // 解析传过来的数据
          var jsonData = JSON.parse(message)
          if (jsonData == null) return
          if (topic.indexOf('SCADA_CHANGE/') >= 0) {
            Object.keys(this.monitorData).forEach((key) => {
              var client_code = this.monitorData[key].client_code
              var group_code = this.monitorData[key].group_code
              var tag_code = this.monitorData[key].tag_code
              var tag_key = client_code + '/' + group_code + '/' + tag_code
              if (tag_key === jsonData.TagKey) {
                this.monitorData[key].value = jsonData.TagNewValue
              }
            })
          }
        } catch (e) {
          console.log(e)
        }
      })
    },
    // 发送消息函数
    sendMessage(topic, msg) {
      if (!this.mqttConnStatus) {
        console.error('MQTT未连接')
        this.$message({
          message: this.$t('tlps.index.pleaseStartMonitoring'),
          type: 'error'
        })
        return
      }

      // qos消息发布服务质量
      this.clientMqtt.publish(topic, msg, { qos: 0 }, (error) => {
        if (!error) {
          console.log('MQTT消息已发送')
          console.warn('MQTT发送成功：' + topic)
          // this.$message({ message: '写入成功', type: 'success' })
        } else {
          console.error('MQTT发送错误:', error)
          this.$message({ message: this.$t('tlps.index.operationFailed'), type: 'error' })
        }
        console.log('HAQD下发配方 - 结束 ============================')
      })
    },
    // 订阅主题函数
    topicSubscribe(topic) {
      if (!this.mqttConnStatus) {
        this.$message({
          message: this.$t('tlps.index.pleaseStartMonitoring'),
          type: 'error'
        })
        return
      }
      // 订阅主题
      // 等级与作用
      // level 0：最多一次的传输
      // level 1：至少一次的传输(鸡肋)
      // level 2： 只有一次的传输
      this.clientMqtt.subscribe(topic, { qos: 0 }, (error) => {
        if (!this.mqttConnStatus) {
          this.$message({
            message: this.$t('tlps.index.pleaseStartMonitoring'),
            type: 'error'
          })
          return
        }
        if (!error) {
          // console.log('MQTT订阅成功:' + topic)
        } else {
          console.log('MQTT订阅失败:' + topic)
        }
      })
    },


    //============================================定制化逻辑功能=============================
    //扫描网框条码
    scanWkBarcode(){
      if(this.wkBarCode===''){
          this.$message({ message: 'please Scan WkBarCode', type: 'warning' })
          return
      }
      var client_code = this.monitorData.WkBarCode.client_code
      var group_code = this.monitorData.WkBarCode.group_code
      var tag_code = this.monitorData.WkBarCode.tag_code
      var tag_key = client_code + '/' + group_code + '/' + tag_code
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: this.wkBarCode
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + client_code
      this.sendMessage(topic, sendStr)
      this.wkBarCode=''
    },
    //扫描料框条码
    scanLkBarcode(){
      if(this.lkBarCode===''){
          this.$message({ message: 'please Scan lKBarCode', type: 'warning' })
          return
      }
      var client_code = this.monitorData.LkBarCode.client_code
      var group_code = this.monitorData.LkBarCode.group_code
      var tag_code = this.monitorData.LkBarCode.tag_code
      var tag_key = client_code + '/' + group_code + '/' + tag_code
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: tag_key,
        TagValue: this.lkBarCode
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + client_code
      this.sendMessage(topic, sendStr)
      this.lkBarCode=''
    },
    toQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.toQuery()
    },
    resetQuery() {
      this.nowPageIndex = 1
      this.pageList = []
      this.query.page_dirct = ''
      this.query.page_id = ''
      this.crud.resetQuery()
    },
    pageQuery(pageDirct) {
      if (pageDirct === 'pre') {
        if (this.nowPageIndex <= 1) {
          this.nowPageIndex = 1
          this.$message({
            message: this.$t('view.dialog.top'),
            type: 'info'
          })
          return
        }
        this.nowPageIndex = this.nowPageIndex - 1
        const preId = this.pageList[this.nowPageIndex]
        this.query.page_dirct = pageDirct
        this.query.page_id = preId
        this.crud.toQuery()
      } else {
        const total_page = this.page.total === 0 ? 1 : Math.ceil(this.page.total / this.query.tableSize)
        if (total_page === 1 || this.nowPageIndex >= total_page) {
          this.$message({
            message: this.$t('view.dialog.bottom'),
            type: 'info'
          })
          return
        }
        const preId = this.crud.data[0].id
        this.pageList[this.nowPageIndex] = preId
        this.nowPageIndex = this.nowPageIndex + 1
        this.query.page_dirct = pageDirct
        this.query.page_id = this.crud.data[this.crud.data.length - 1].id
        this.crud.toQuery()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.app-container {
    .wrapTextSelect {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .wrapElForm{
    ::v-deep .barcode{
            display: flex;
            .el-form-item--small{
                width: 90%;
                margin-right: 10px;
                .el-form-item__content{
                    .el-input__inner{
                        background: yellow;
                    }
                }
            }
        }
    }
}
</style>

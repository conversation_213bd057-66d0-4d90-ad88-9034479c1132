<template>
  <div class="app-container">
    <el-card ref="queryCard" shadow="never" class="wrapCard">
      <el-form ref="query" :inline="true" size="small" label-width="100px">
        <div class="wrapElForm">
          <div class="wrapElFormFirst col-md-10 col-12">
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceRole.productionLine') + '：'">
                <!-- 产线： -->
                <el-select v-model="query.prod_line_id" filterable clearable size="small">
                  <el-option v-for="item in prodLineData" :key="item.prod_line_id" :label="item.prod_line_code + ' ' + item.prod_line_des" :value="item.prod_line_id" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.maintenanceRole.station') + '：'">
                <!-- 工位： -->
                <el-select v-model="query.station_id" filterable clearable>
                  <el-option v-for="item in stationData" :key="item.station_id" :label="item.station_code + ' ' + item.station_des" :value="item.station_id+''" />
                </el-select>
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.mainmain.processCodet') + '：'">
                <!-- 流程编码： -->
                <el-input v-model="query.flow_main_code" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.mainmain.processDescriptiont') + ''">
                <!-- 流程描述： -->
                <el-input v-model="query.flow_main_des" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.mainmain.taskInfo') + '：'">
                <!-- 任务信息： -->
                <el-input v-model="query.task_info" clearable size="small" />
              </el-form-item>
            </div>
            <div class="formChild col-md-4 col-12">
              <el-form-item :label="$t('lang_pack.monitor.time') + '：'">
                <div class="block">
                  <el-date-picker
                    v-model="query.item_date"
                    type="datetimerange"
                    size="small"
                    align="right"
                    unlink-panels
                    range-separator="~"
                    :start-placeholder="$t('lang_pack.cuttingZone.startTime')"
                    :end-placeholder="$t('lang_pack.cuttingZone.endTime')"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :picker-options="pickerOptions"
                  />
                </div>
              </el-form-item>
            </div>
          </div>
          <div class="wrapElFormSecond formChild col-md-2 col-12">
            <el-form-item>
              <span class="wrapRRItem">
                <el-button class="filter-item" size="small" type="primary" icon="el-icon-search" @click="toQuery">{{ $t('lang_pack.commonPage.search') }}</el-button>  <!-- 搜索 -->
                <el-button class="filter-item" size="small" icon="el-icon-refresh-left" @click="resetQuery()">{{ $t('lang_pack.commonPage.reset') }}</el-button>  <!-- 重置 -->

              </span>
            </el-form-item>
          </div>
        </div>
      </el-form>
    </el-card>

    <el-card shadow="never" style="margin-top: 10px">
      <!--工具栏-->

      <el-drawer id="flow-chart-drawer" :with-header="false" :visible.sync="editFlowChart" direction="rtl" size="100%">
        <flowChart
          v-if="mainchartShow"
          ref="flowChart"
          :flow_mod_main_id="currentRow.flow_mod_main_id"
          :flow_mod_main_des="currentRow.flow_mod_main_des"
          @closeDrawer="
            editFlowChart = false
            mainchartShow = false
          "
        />
      </el-drawer>
      <el-row :gutter="20">
        <el-col :span="24">
          <!--表格渲染-->
          <el-table ref="table" v-loading="tableLoading" border size="small" :data="tableData" style="width: 100%" :cell-style="cellStyle" highlight-current-row :height="height" @header-dragend="crud.tableHeaderDragend()">
            <el-table-column :show-overflow-tooltip="true" prop="station_des" :label="$t('lang_pack.maintenanceRole.station')" />
            <el-table-column :show-overflow-tooltip="true" prop="flow_main_code" :label="$t('lang_pack.mainmain.processCodet')" />
            <el-table-column :show-overflow-tooltip="true" prop="flow_main_des" :label="$t('lang_pack.mainmain.processDescriptiont')" />
            <el-table-column :show-overflow-tooltip="true" prop="flow_task_status" :label="$t('lang_pack.monitor.status')" />
            <el-table-column :show-overflow-tooltip="true" prop="start_date" :label="$t('lang_pack.hmiMain.startTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="end_date" :label="$t('lang_pack.hmiMain.endTime')" />
            <el-table-column :show-overflow-tooltip="true" prop="task_info" :label="$t('lang_pack.mainmain.taskInfo')" />
            <el-table-column :label="$t('lang_pack.commonPage.operate')" width="70" align="center" fixed="right">
              <!-- 操作 -->
              <template slot-scope="scope">
                <el-button size="small" type="text" @click="openFlowChart(scope.row)">{{ $t('lang_pack.mainmain.view') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页组件-->
          <el-pagination
            :page-size.sync="pageTable.size"
            :total="pageTable.total"
            :current-page.sync="pageTable.page"
            style="margin-top: 8px;float:right;"
            layout="total, prev, pager, next, sizes"
            @size-change="sizeChangeHandler($event)"
            @current-change="pageChangeHandler"
          />
        </el-col>
      </el-row>
    </el-card>
    <el-drawer id="flow-chart-drawer" :with-header="false" :visible.sync="flowTaskDialogVisible" direction="rtl" size="100%">
      <flowTask v-if="flowTaskDialogVisible" ref="flowTask" :flow_task="currentRow" :cel_api="cellIp + ':' + webapiPort" :mqtt_url="cellIp + ':' + mqttPort" @closeDrawer="flowTaskDialogVisible = false" />
    </el-drawer>
  </div>
</template>

<script>
import crudProdLine from '@/api/core/factory/sysProdLine'
import { sel as selStation } from '@/api/core/factory/sysStation'
import { selCellIP } from '@/api/core/center/cell'
import Cookies from 'js-cookie'
import flowTask from '@/views/core/flow/task/task'
import { createDatePickerShortcuts } from '@/utils/datePickerShortcuts'

import axios from 'axios'
const queryDefault = {
  prod_line_id: '',
  station_id: '',
  item_date: null,
  flow_task_status: '',
  task_info: '',
  flow_main_code: '',
  flow_main_des: '',
  sort: '',
  page: 0,
  size: 10
}
const pageTableDefault = {
  // 页码
  page: 1,
  // 每页数据条数
  size: 10,
  // 总数据条数
  total: 0
}
export default {
  name: 'HMI_FLOW_RECORD',
  components: { flowTask },
  // 数据模型
  data() {
    return {
      height: document.documentElement.clientHeight - 270,
      query: { ...queryDefault },
      pageTable: { ...pageTableDefault },
      tableLoading: false,
      tableData: [],
      prodLineData: [],
      stationData: [],
      currentRow: {},
      mainchartShow: false,
      editFlowChart: false,
      cellIp: '', // 单元IP
      webapiPort: '', // 单元API端口号
      mqttPort: '', // MQTT端口号
      flowTaskDialogVisible: false,
      pickerOptions: {
        shortcuts: []
      }
    }
  },
  // 数据字典
  dicts: ['ENABLE_FLAG'],
  watch: {
    'query.prod_line_id': {
      handler() {
        this.getStationData()
      }
    },
    'query.station_id': {
      handler() {
        this.getCellIp()
      }
    }
  },

  mounted() {
    const that = this
    window.onresize = function temp() {
      that.height = document.documentElement.clientHeight - 270
    }
  },
  created() {
    // Initialize date picker shortcuts
    this.initPickerOptions()

    crudProdLine
      .sel({
        user_name: Cookies.get('userName'),
        enable_flag: 'Y'
      })
      .then(res => {
        const defaultQuery = JSON.parse(JSON.stringify(res))
        if (defaultQuery.code === 0) {
          if (defaultQuery.data.length > 0) {
            this.prodLineData = defaultQuery.data
          }
        }
      })
      .catch(() => {
        this.$message({
          message: this.$t('lang_pack.mainmain.dataException'),
          type: 'error'
        })
      })
  },
  methods: {
    // Initialize date picker shortcuts
    initPickerOptions() {
      this.pickerOptions = createDatePickerShortcuts(this.$i18n)
    },
    cellStyle() {
      return 'border:0px;border-bottom:1px solid #dfe6ec'
    },
    openFlowChart(row) {
      this.currentRow = row
      this.flowTaskDialogVisible = true
    },
    getStationData() {
      this.query.station_id = ''
      this.stationData = []
      const query = {
        user_name: Cookies.get('userName'),
        enable_flag: 'Y',
        prod_line_id: this.query.prod_line_id
      }
      selStation(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            if (defaultQuery.data.length > 0) {
              this.stationData = defaultQuery.data
            }
          }
        })
        .catch(() => {
          this.$message({
            message: this.$t('lang_pack.vie.queryException'),
            type: 'error'
          })
        })
    },
    getCellIp() {
      var cellId = 0
      const stationInfo = this.stationData.filter(item => item.station_id === parseInt(this.query.station_id))
      if (stationInfo.length > 0) {
        cellId = stationInfo[0].cell_id
      }
      const query = {
        user_name: Cookies.get('userName'),
        cell_id: cellId,
        current_ip: window.location.hostname
      }
      selCellIP(query)
        .then(res => {
          const defaultQuery = JSON.parse(JSON.stringify(res))
          if (defaultQuery.code === 0) {
            const ipInfo = JSON.parse(defaultQuery.result)
            this.cellIp = ipInfo.ip
            this.webapiPort = ipInfo.webapi_port
            this.mqttPort = ipInfo.mqtt_port
          } else {
            this.$message({ message: defaultQuery.msg, type: 'error' })
          }
        })
        .catch(() => {
          this.$message({ message: this.$t('lang_pack.vie.queryException'), type: 'error' })
        })
    },
    resetQuery() {
      this.query = { ...queryDefault }
      this.pageTable = { ...pageTableDefault }
      this.cellIp = ''
      this.webapiPort = ''
      this.tableData = []
    },
    toQuery() {
      if (this.query.prod_line_id === '' || this.query.station_id === '') {
        this.$message({ message: this.$t('lang_pack.monitor.selectProduction'), type: 'info' })
        return
      }
      var method = '/cell/core/flow/CoreFlowTaskListSelectAll'
      var path = ''
      if (process.env.NODE_ENV === 'development') {
        path = 'http://localhost:' + this.webapiPort + method
      } else {
        path = 'http://' + this.cellIp + ':' + this.webapiPort + method
      }
      this.tableLoading = true
      this.tableData = []
      this.query.total = 0
      const data = {
        station_id: this.query.station_id,
        item_date: this.query.item_date,
        flow_task_status: this.query.flow_task_status,
        task_info: this.query.task_info,
        flow_main_code: this.query.flow_main_code,
        flow_main_des: this.query.flow_main_des,
        sort: this.query.sort,
        page: this.query.page,
        size: this.query.size
      }

      //加入语言传递
      var langSel='zh-CN'
      const language = ['zh-CN', 'en-US', 'zh-TW', 'th']
      if (language.includes(localStorage.getItem('language'))) {
        langSel = localStorage.getItem('language') || 'zh-CN'
      }

      axios
        .post(path, data, {
          headers: {
            'Content-Type': 'application/json',
            'Ais-Languages':langSel
          }
        })
        .then(res => {
          this.tableLoading = false
          const defaultQuery = JSON.parse(JSON.stringify(res))
          console.log(defaultQuery)
          if (defaultQuery.data.code === 0) {
            if (defaultQuery.data.count > 0) {
              this.tableData = defaultQuery.data.data
            }
            this.pageTable.total = defaultQuery.data.count
          } else {
            this.$message({ message: defaultQuery.data.msg, type: 'warning' })
          }
        })
        .catch(ex => {
          this.tableLoading = false
          this.$message({ message: this.$t('lang_pack.vie.queryException') + ex, type: 'error' })
        })
    },
    // Page:分页
    sizeChangeHandler(val) {
      // 页大小改变
      this.query.size = val

      // 查询
      this.toQuery()
    },
    pageChangeHandler(val) {
      // 页数改变
      this.query.page = val
      // 查询
      this.toQuery()
    }
  }
}
</script>

<style lang="scss">
#flow-chart-drawer .el-drawer__open .el-drawer.rtl {
  padding: 0px 0px 0px 0px;
}
</style>

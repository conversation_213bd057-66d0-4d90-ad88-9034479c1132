<template>
  <div>
    <el-descriptions :column="1" size="medium" border>
      <el-descriptions-item label-class-name="table-descriptions-label">
        <template slot="label">
          <i class="el-icon-tickets" />
          Carrier/Tray
        </template>
        <el-input ref="webPalletNum" v-model="webPalletNum" clearable size="mini" @input="handleInputPallNum" />
      </el-descriptions-item>
    </el-descriptions>
    <div style="margin-top:10px;text-align:right">
      <el-button type="primary" @click="handleSendInfo('confirm')">{{ $t('lang_pack.dialogMain.manualInput') }}</el-button>
      <el-button type="primary" @click="handleSendInfo('retry')">{{ $t('lang_pack.dialogMain.retry') }}</el-button>
      <el-button type="primary" @click="handleSendInfo('pass')">{{ $t('lang_pack.dialogMain.pass') }}</el-button>
    </div>
  </div>
</template>

<script>
export default {
  components: { },
  props: {
    tag_key_list: {
      type: Object,
      default: null
    },
    DyCheckCode: {
      type: String,
      default: ''
    }
  },
  // 数据模型
  data() {
    return {
      webPalletNum: ''
    }
  },
  mounted: function() {

  },
  created: function() {
    this.$nextTick(x => {
      // 正确写法
      this.$refs.webPalletNum.focus()
    })
  },
  methods: {
    handleInputPallNum(e) {
      if (this.DyCheckCode) {
        if (e.length === 12) {
          this.$message({ type: 'info', message: 'The length of the vehicle number cannot exceed 12 digits' })
          return
        }
        if (e.length > 12) {
          this.$message({ type: 'error', message: 'Vehicle number length greater than 12 digits, cleared' })
          this.webPalletNum = ''
          return
        }
      }
    },
    handleSendInfo(type) {
      var webPanelletConfirmModel = '0'
      if (type === 'confirm') {
        if (this.webPalletNum === '') {
          this.$message({ message: 'Please Input Carrier/Tray', type: 'info' })
          return
        }
        webPanelletConfirmModel = '1'
      } else if (type === 'retry') {
        webPanelletConfirmModel = '2'
      } else if (type === 'pass') {
        webPanelletConfirmModel = '3'
      }
      var sendJson = {}
      var rowJson = []
      var newRow = {
        TagKey: this.tag_key_list.WebPalletNum,
        TagValue: this.webPalletNum
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPalletConfirmModel,
        TagValue: webPanelletConfirmModel
      }
      rowJson.push(newRow)
      newRow = {
        TagKey: this.tag_key_list.WebPalletInfoRequest,
        TagValue: '1'
      }
      rowJson.push(newRow)
      sendJson.Data = rowJson
      sendJson.ClientName = 'SCADA_WEB'
      var sendStr = JSON.stringify(sendJson)
      var topic = 'SCADA_WRITE/' + this.tag_key_list.WebPalletNum.split('/')[0]
      this.$emit('sendMessage', topic, sendStr)
    }
  }
}
</script>
<style>
.table-descriptions-label {
  width: 200px !important;
}
</style>
